#!/bin/bash

# mDNS 广播修复脚本
# 修复局域网 mDNS 广播和解析问题

echo "=== mDNS 广播修复脚本 ==="

# 获取网络信息
INTERFACE="eth0"
IP=$(ip addr show $INTERFACE | grep "inet " | awk '{print $2}' | cut -d'/' -f1)
HOSTNAME=$(hostname)
HOSTNAME_LOCAL="${HOSTNAME,,}.local"

echo "网络接口: $INTERFACE"
echo "IP 地址: $IP"
echo "主机名: $HOSTNAME"
echo "本地域名: $HOSTNAME_LOCAL"
echo ""

# 1. 检查并修复多播支持
echo "=== 1. 修复多播支持 ==="

# 添加多播路由
echo "添加多播路由..."
route add -net ********* netmask 240.0.0.0 dev $INTERFACE 2>/dev/null && echo "✅ 多播路由添加成功" || echo "⚠️ 多播路由可能已存在"

# 启用接口多播
echo "启用接口多播..."
ip link set $INTERFACE multicast on
echo "✅ 接口多播已启用"

# 2. 检查并修复防火墙
echo ""
echo "=== 2. 修复防火墙设置 ==="

# 开放 mDNS 端口
echo "开放 mDNS 端口 5353..."
iptables -C INPUT -p udp --dport 5353 -j ACCEPT 2>/dev/null || {
    iptables -A INPUT -p udp --dport 5353 -j ACCEPT
    echo "✅ INPUT 规则添加成功"
}

iptables -C OUTPUT -p udp --dport 5353 -j ACCEPT 2>/dev/null || {
    iptables -A OUTPUT -p udp --dport 5353 -j ACCEPT
    echo "✅ OUTPUT 规则添加成功"
}

# 允许多播流量
iptables -C INPUT -d *********** -p udp --dport 5353 -j ACCEPT 2>/dev/null || {
    iptables -A INPUT -d *********** -p udp --dport 5353 -j ACCEPT
    echo "✅ 多播 INPUT 规则添加成功"
}

iptables -C OUTPUT -d *********** -p udp --dport 5353 -j ACCEPT 2>/dev/null || {
    iptables -A OUTPUT -d *********** -p udp --dport 5353 -j ACCEPT
    echo "✅ 多播 OUTPUT 规则添加成功"
}

# 3. 重启 mDNS 服务
echo ""
echo "=== 3. 重启 mDNS 服务 ==="

echo "停止现有 mDNS 进程..."
killall mdnsd 2>/dev/null && echo "✅ mDNS 进程已停止" || echo "⚠️ 未找到 mDNS 进程"

# 等待服务自动重启
echo "等待 mDNS 服务重启..."
sleep 3

# 检查服务是否重启
if ps aux | grep -v grep | grep -q mdns; then
    echo "✅ mDNS 服务已重启"
else
    echo "⚠️ mDNS 服务未自动重启，可能需要手动启动"
fi

# 4. 测试 mDNS 功能
echo ""
echo "=== 4. 测试 mDNS 功能 ==="

# 等待服务稳定
sleep 2

echo "测试本地解析..."
ping -c 1 $HOSTNAME_LOCAL >/dev/null 2>&1 && echo "✅ 本地 .local 域名解析成功" || echo "❌ 本地 .local 域名解析失败"

echo "测试 mDNS 查询..."
timeout 3 dig @*********** -p 5353 $HOSTNAME_LOCAL A +short 2>/dev/null | grep -q "$IP" && echo "✅ mDNS 查询成功" || echo "❌ mDNS 查询失败"

# 5. 手动发送 mDNS 广播
echo ""
echo "=== 5. 手动触发 mDNS 广播 ==="

# 创建临时的 mDNS 查询来触发响应
echo "发送 mDNS 查询以触发广播..."
dig @*********** -p 5353 $HOSTNAME_LOCAL A +short >/dev/null 2>&1 &
dig @*********** -p 5353 _services._dns-sd._udp.local PTR +short >/dev/null 2>&1 &

# 6. 验证网络广播
echo ""
echo "=== 6. 验证网络广播 ==="

echo "监听 mDNS 流量 (3秒)..."
timeout 3 tcpdump -i $INTERFACE port 5353 -c 5 2>/dev/null | grep -q "5353" && echo "✅ 检测到 mDNS 流量" || echo "⚠️ 未检测到 mDNS 流量"

# 7. 显示当前状态
echo ""
echo "=== 7. 当前状态 ==="

echo "mDNS 进程:"
ps aux | grep -v grep | grep mdns || echo "未找到 mDNS 进程"

echo "mDNS 端口:"
netstat -an | grep 5353 || echo "mDNS 端口未监听"

echo "多播路由:"
route -n | grep ********* || echo "未找到多播路由"

# 8. 给出测试建议
echo ""
echo "=== 8. 测试建议 ==="
echo "请在局域网其他设备上测试:"
echo "1. ping $HOSTNAME_LOCAL"
echo "2. nslookup $HOSTNAME_LOCAL"
echo "3. 使用 Bonjour Browser 查看服务"
echo ""
echo "如果仍然无法解析，请检查:"
echo "1. 路由器是否支持多播转发"
echo "2. 网络交换机是否支持 IGMP"
echo "3. 客户端设备是否支持 mDNS"
echo ""

echo "=== 修复完成 ==="
