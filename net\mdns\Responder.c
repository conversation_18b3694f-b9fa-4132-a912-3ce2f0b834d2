/* -*- Mode: C; tab-width: 4 -*-
 *
 * Copyright (c) 2002-2004 Apple Computer, Inc. All rights reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
*/

#include "mDNSEmbeddedAPI.h" // Defines the interface to the client layer above
#include "mDNSPosix.h"       // Defines the specific types needed to run mDNS on this platform

#include <assert.h>
#include <stdio.h>  // For printf()
#include <stdlib.h> // For exit() etc.
#include <string.h> // For strlen() etc.
//#include <unistd.h>			// For select()
//#include <errno.h>			// For errno, EINTR
//#include <signal.h>
//#include <fcntl.h>
#include "SetServ.h"
#include <time.h>
#include <sys/stat.h>
//#include "arch.h"
//#include "bios.h"
#include "threads.h"
#include "netservice.h"
#include "project_config.h" //机型相关宏定义使用 2019.12.16
//#include "oem_config.h"
#include "../AirPrint/eSCL/escl_config.h"
#include "netapi.h"
//#endif
extern char      *build_wsd_ethernet_uuid(char *uuid);
extern int        lni_GetPortNumber(int selection, int *i, int length);
extern int        lni_AppStatusGet(int selection);
extern int        lni_GetSystemSetting(int selection, char *tempName, int length);
extern NET_STATUS lni_GetManufactureName(char *ManufactureName, int strLen);
extern NET_STATUS lni_GetModelName(char *ModelName, int strLen);
extern int        lni_GetBonjourSettiing(int selection, char *tempName, int length);
extern int        lni_SetBonjourSettiing(int selection, char *tempName, int length);
extern void       lni_NetStatus_BonjRInitError(void);
extern int        lni_GetSSLServerCommunicationSetting(int option, unsigned int *value);

// 声明外部函数
extern char* get_eth0_ip_str(void);
extern void mDNS_SetFQDN(mDNS *const m);
extern int        lni_GetGcpStatus(int *status, int length);
extern int        lni_GetIPPEnable(int option, unsigned int *value, int length);
extern void       update_cloud_print_name(void);
extern char      *get_cloud_device_id(void);
extern void       websSSLNotifyCheckCert();
extern tStatus    CRM_Value_Get(int option, void *value);

typedef char DNSCString[1024];

static mDNS                 mDNSStorage;     // mDNS core uses this to store its globals
static mDNS_PlatformSupport PlatformStorage; // Stores this platform's globals

static void updateServiceName(ResourceRecord *rr);

static const char *gProgramName = "mDNSResponderPosix";

//static const char kDefaultPIDFile[]     = "";//"/var/run/mDNSResponder.pid";
//static const char kDefaultServiceType[] = "_afpovertcp._tcp.";
static const char kDefaultServiceType[] = "_printer._tcp";
//static const char kDefaultServiceType1[] = "_ipp._tcp";
#if CFG_ippManager
static const char kIPPServiceSubType[] = ",_universal";
#endif
static const char kDefaultServiceDomain[] = "local.";
//Added by Dennis Xiang for GCP20, 2013/9/17
#if 0
static const char kPrivetServiceType[] = "_privet._tcp";
static const char kPrivetServiceSubType[] = ",_printer";
#endif
extern int InterfaceState;
#if AIRPRINT_ENABLED //Terry@20160612
extern int lladdr_state[NETIF_CNT];
#endif
#define TXTNUM 26
#define TMPLEN 128

//#define debugf xprintf

#ifdef TsAdapter_NetModule
static TsThread Thread_BonjourTask = INVALIDTHREAD;
#else
static pthread_t nwk_Thread_BonjourTask;
#endif

#ifdef TsAdapter_NetModule
static TsSemaphore BonjourSem;
#else
static sem_t     nwk_BonjourSem;
#endif

/*
enum {
    kDefaultPortNumber = 548
};
*/
//static mDNSBool    gAvoidPort53      = mDNStrue;
static char        gServiceName[80] = {0};
static const char *gServiceType     = kDefaultServiceType;
static const char *gServiceDomain   = kDefaultServiceDomain;

static mDNSu8  gLPDServiceText[sizeof(RDataBody)] = {0};
static mDNSu16 gLPDServiceTextLen                 = 0;

static mDNSu8  g9100ServiceText[sizeof(RDataBody)] = {0};
static mDNSu16 g9100ServiceTextLen                 = 0;
#if CFG_ippManager
static mDNSu8  gIPPServiceText[sizeof(RDataBody)] = {0};
static mDNSu16 gIPPServiceTextLen                 = 0;

static mDNSu8  gIPPSServiceText[sizeof(RDataBody)] = {0};
static mDNSu16 gIPPSServiceTextLen                 = 0;
#endif
static mDNSu8  gHttpServiceText[sizeof(RDataBody)] = {0};
static mDNSu16 gHttpServiceTextLen                 = 0;

static mDNSu8  gPrivetServiceText[sizeof(RDataBody)] = {0};
static mDNSu16 gPrivetServiceTextLen                 = 0;
#if CONFIG_SCAN
static mDNSu8  geSCLServiceText[sizeof(RDataBody)] = {0};
static mDNSu16 geSCLServiceTextLen                 = 0;
#endif

//char gmacAddr[6]={0};//get MAC address

char sTargetName[TMPLEN];

//static int         gPortNumber       = kDefaultPortNumber;
//static const char *gServiceFile      = "";
//static   mDNSBool  gDaemon           = mDNSfalse;
//static const char *gPIDFile          = kDefaultPIDFile;

typedef struct PosixService PosixService;

struct PosixService
{
    ServiceRecordSet coreServ;
    PosixService    *next;
    int              serviceID;
    char             serviceType[65];
};

static PosixService *gServiceList = NULL;

static int gServiceID = 0;

static mDNSBool gBonjourOn = 0;

static volatile mDNSBool gStopNow;

static int bBonjourNameChanged = 0;

static ServiceRecordSet *g_privetService = 0;
//static TX_SEMAPHORE g_privet_semaphore;

void UpdateMetadata(const mDNSu8 txtinfo[], mDNSu16 txtlen)
{
    // Set up the TXT record rdata,
    // and set DependentOn because we're depending on the SRV record to find and resolve conflicts for us

#ifdef TsAdapter_NetModule
    TSsemWait(BonjourSem);
#else
    sem_wait(&nwk_BonjourSem);
#endif

    //tx_semaphore_get(&g_privet_semaphore, TX_WAIT_FOREVER);
    if (!g_privetService) goto exit;
    if (g_privetService->RR_TXT.resrec.rdata->MaxRDLength < txtlen) g_privetService->RR_TXT.resrec.rdata->MaxRDLength = txtlen;
    if (txtinfo == mDNSNULL)
        g_privetService->RR_TXT.resrec.rdlength = 0;
    else if (txtinfo != g_privetService->RR_TXT.resrec.rdata->u.txt.c)
    {
        g_privetService->RR_TXT.resrec.rdlength = txtlen;
        //if (g_privetService->RR_TXT.resrec.rdlength > g_privetService->RR_TXT.resrec.rdata->MaxRDLength) return;
        mDNSPlatformMemCopy(g_privetService->RR_TXT.resrec.rdata->u.txt.c, txtinfo, txtlen);
    }
    g_privetService->RR_TXT.DependentOn = &g_privetService->RR_SRV;
exit:
#ifdef TsAdapter_NetModule
    TSsemSignal(BonjourSem);
#else
    sem_post(&nwk_BonjourSem);
#endif
    //tx_semaphore_put(&g_privet_semaphore);
}

void mDns_setNameChanged()
{
    bBonjourNameChanged = 1;
    //	update_cloud_print_name();
}

//MarkHsu@Borrow old CB API for Alpha to use
void Bonjour_interface_change_notify()
{
    //char ipv4Addr[20];
    //memset(ipv4Addr, 0, 20);

    //lni_GetIPv4StrAddrNvr("eth0", 0, 1, ipv4Addr);

    mDNS *m = (mDNS *)&mDNSStorage;
    //Log(5,"mDNS: Bonjour_interface_change_notify, IP(%s)\n",ipv4Addr);
    mDNSPlatformPosixRefreshInterfaceList(m);
}

static void RegistrationCallback(mDNS *const m, ServiceRecordSet *const thisRegistration, mStatus status)
// mDNS core calls this routine to tell us about the status of
// our registration.  The appropriate action to take depends
// entirely on the value of status.
{
    switch (status)
    {

        case mStatus_NoError:
        {

            xprintf("RegistrationCallback: %##s Name Registered\n", thisRegistration->RR_SRV.resrec.name->c);
            // Do nothing; our name was successfully registered.  We may
            // get more call backs in the future.

            {
                PosixService *cursor;

                cursor = gServiceList;
                while (cursor != NULL)
                {
                    if (&cursor->coreServ == thisRegistration)
                    {
                        if (strcmp(cursor->serviceType, kDefaultServiceType) == 0)
                        {
                            xprintf("RegistrationCallback:xxx Bonjour ServiceType is %s\n", cursor->serviceType);
                            updateServiceName(&(thisRegistration->RR_SRV.resrec));
                        }
                        break;
                    }
                    cursor = cursor->next;
                }
            }
        }

        break;

        case mStatus_NameConflict:
            xprintf("RegistrationCallback: %##s Name Conflict\n", thisRegistration->RR_SRV.resrec.name->c);

            // In the event of a conflict, this sample RegistrationCallback
            // just calls mDNS_RenameAndReregisterService to automatically
            // pick a new unique name for the service. For a device such as a
            // printer, this may be appropriate.  For a device with a user
            // interface, and a screen, and a keyboard, the appropriate response
            // may be to prompt the user and ask them to choose a new name for
            // the service.
            //
            // Also, what do we do if mDNS_RenameAndReregisterService returns an
            // error.  Right now I have no place to send that error to.

            status = mDNS_RenameAndReregisterService(m, thisRegistration, mDNSNULL);
            assert(status == mStatus_NoError);
            break;

        case mStatus_MemFree:
            xprintf("RegistrationCallback: %##s Memory Free\n", thisRegistration->RR_SRV.resrec.name->c);

            // When debugging is enabled, make sure that thisRegistration
            // is not on our gServiceList.

#if !defined(NDEBUG)
            {
                PosixService *cursor;

                cursor = gServiceList;
                while (cursor != NULL)
                {
                    assert(&cursor->coreServ != thisRegistration);
                    cursor = cursor->next;
                }
            }
#endif
            if (thisRegistration->SubTypes) free(thisRegistration->SubTypes);
            free(thisRegistration);
            break;

        default:
            xprintf("RegistrationCallback: %##s Unknown Status %ld\n", thisRegistration->RR_SRV.resrec.name->c, status);
            break;
    }
}

static void updateTargetName(mDNS *const m)
{
    char sTempTargetName[TMPLEN];

    //xprintf("HostName is %s\n", &m->hostlabel.c[1]);

    memset(sTargetName, 0, sizeof(sTargetName));
    strcpy((char *)sTargetName, (char *)&m->hostlabel.c[1]);

    memset(sTempTargetName, 0, sizeof(sTempTargetName));
    lni_GetBonjourSettiing(2, sTempTargetName, MAX_DOMAIN_LABEL);

#if 0 //Jeff AIRPRINT_ENABLED	//Terry@20160612
	if (lladdr_state[0] < 2 ) {
		websSSLNotifyCheckCert();
	}
#endif
    /* leo@20131010 fix bug 0040523
	if(strcmp(sTargetName,sTempTargetName))
		lni_SetBonjourSettiing(0,sTargetName,strlen(sTargetName)+1);
	*/
}

static void updateServiceName(ResourceRecord *rr)
{
    int  len                 = 0;
    char sOldServiceName[80] = {0};
    char sNewServiceName[80] = {0};

    len = rr->name->c[0];
    //  xprintf("length is %d\n", len);

    memcpy(sNewServiceName, &(rr->name->c[1]), len);
    sNewServiceName[len] = '\0';

    xprintf("xxx Bonjour ServiceName is %s\n", sNewServiceName);

    lni_GetBonjourSettiing(3, sOldServiceName, 80);
    if (strcmp(sOldServiceName, sNewServiceName)) lni_SetBonjourSettiing(1, sNewServiceName, strlen(sNewServiceName) + 1);
}

static void mDNSInitCallback(mDNS *const m, mStatus status)
{
    xprintf("xxx xxx hostName %s, status = %d\n", &m->hostlabel.c[1], status);

    switch (status)
    {
        case mStatus_NameConflict:
            break;
        case mStatus_NoError:
            updateTargetName(m);
            break;
        case mStatus_GrowCache:
            break;
        default:
            xprintf("InitCallback:Unknown Status %ld", status);
            break;
    }
}

static mStatus RegisterOneService(const char *richTextName, const char *serviceType, const char *serviceDomain, const mDNSu8 text[], mDNSu16 textLen,
                                  long portNumber)
{
    mStatus       status;
    PosixService *thisServ;
    domainlabel   name;
    domainname    type;
    domainname    domain;

    status   = mStatus_NoError;
    thisServ = (PosixService *)malloc(sizeof(*thisServ));
    if (thisServ == NULL)
    {
        status = mStatus_NoMemoryErr;
    }
    if (status == mStatus_NoError)
    {
        MakeDomainLabelFromLiteralString(&name, richTextName);
        MakeDomainNameFromDNSNameString(&type, serviceType);
        MakeDomainNameFromDNSNameString(&domain, serviceDomain);
        status = mDNS_RegisterService(&mDNSStorage, &thisServ->coreServ, &name, &type, &domain, // Name, type, domain
                                      NULL, mDNSOpaque16fromIntVal(portNumber), text, textLen,  // TXT data, length
                                      NULL, 0,                                                  // Subtypes
                                      mDNSInterface_Any,                                        // Interface ID
                                      RegistrationCallback, thisServ, 0);                       // Callback, context, flags
    }
    if (status == mStatus_NoError)
    {
        thisServ->serviceID = gServiceID;
        gServiceID += 1;
        strcpy(thisServ->serviceType, serviceType);

        thisServ->next = gServiceList;
        gServiceList   = thisServ;

        if (gMDNSPlatformPosixVerboseLevel > 0)
        {
            fprintf(stderr, "%s: Registered service %d, name \"%s\", type \"%s\", domain \"%s\",  port %ld\n", gProgramName, thisServ->serviceID,
                    richTextName, serviceType, serviceDomain, portNumber);
        }
    }
    else
    {
        if (thisServ != NULL)
        {
            free(thisServ);
        }
    }
    return status;
}

// If there's a comma followed by another character,
// FindFirstSubType overwrites the comma with a nul and returns the pointer to the next character.
// Otherwise, it returns a pointer to the final nul at the end of the string
mDNSlocal char *FindFirstSubType(char *p)
{
    while (*p)
    {
        if (p[0] == '\\' && p[1])
            p += 2;
        else if (p[0] == ',' && p[1])
        {
            *p++ = 0;
            return (p);
        }
        else
            p++;
    }
    return (p);
}

// If there's a comma followed by another character,
// FindNextSubType overwrites the comma with a nul and returns the pointer to the next character.
// If it finds an illegal unescaped dot in the subtype name, it returns mDNSNULL
// Otherwise, it returns a pointer to the final nul at the end of the string
mDNSlocal char *FindNextSubType(char *p)
{
    while (*p)
    {
        if (p[0] == '\\' && p[1]) // If escape character
            p += 2;               // ignore following character
        else if (p[0] == ',')     // If we found a comma
        {
            if (p[1]) *p++ = 0;
            return (p);
        }
        else if (p[0] == '.')
            return (mDNSNULL);
        else
            p++;
    }
    return (p);
}

// Returns -1 if illegal subtype found
mDNSexport mDNSs32 ChopSubTypes(char *regtype)
{
    mDNSs32 NumSubTypes = 0;
    char   *stp         = FindFirstSubType(regtype);
    while (stp && *stp) // If we found a comma...
    {
        if (*stp == ',') return (-1);
        NumSubTypes++;
        stp = FindNextSubType(stp);
    }
    if (!stp) return (-1);
    return (NumSubTypes);
}

mDNSexport AuthRecord *AllocateSubTypes(mDNSs32 NumSubTypes, char *p)
{
    AuthRecord *st = mDNSNULL;
    if (NumSubTypes)
    {
        mDNSs32 i;
        st = malloc(NumSubTypes * sizeof(AuthRecord));
        if (!st) return (mDNSNULL);
        for (i = 0; i < NumSubTypes; i++)
        {
            mDNS_SetupResourceRecord(&st[i], mDNSNULL, mDNSInterface_Any, kDNSQType_ANY, kStandardTTL, 0, AuthRecordAny, mDNSNULL, mDNSNULL);
            while (*p) p++;
            p++;
            if (!MakeDomainNameFromDNSNameString(&st[i].namestorage, p))
            {
                free(st);
                return (mDNSNULL);
            }
        }
    }
    return (st);
}

#if CFG_ippManager
static mStatus RegisterIPPService(const char *richTextName, const char *serviceType, const char *serviceDomain, const mDNSu8 text[], mDNSu16 textLen,
                                  long portNumber)
{
    mStatus       status;
    PosixService *thisServ;
    domainlabel   name;
    domainname    type;
    domainname    domain;
    DNSCString    regtype;
    AuthRecord   *SubTypes    = NULL;
    int           NumSubTypes = 0;

    status   = mStatus_NoError;
    thisServ = (PosixService *)malloc(sizeof(*thisServ));
    if (thisServ == NULL)
    {
        status = mStatus_NoMemoryErr;
    }
    if (status == mStatus_NoError)
    {
        MakeDomainLabelFromLiteralString(&name, richTextName);
        MakeDomainNameFromDNSNameString(&type, serviceType);
        MakeDomainNameFromDNSNameString(&domain, serviceDomain);
        mDNSPlatformMemCopy(regtype, kIPPServiceSubType, strlen(kIPPServiceSubType) + 1);
        NumSubTypes = ChopSubTypes(regtype);
        SubTypes    = AllocateSubTypes(NumSubTypes, regtype);
        if (NumSubTypes && !SubTypes) return mStatus_NoMemoryErr;

        status = mDNS_RegisterService(&mDNSStorage, &thisServ->coreServ, &name, &type, &domain, // Name, type, domain
                                      NULL, mDNSOpaque16fromIntVal(portNumber), text, textLen,  // TXT data, length
                                      SubTypes, NumSubTypes,                                    // Subtypes
                                      mDNSInterface_Any,                                        // Interface ID
                                      RegistrationCallback, thisServ, 0);                       // Callback and context
    }
    if (status == mStatus_NoError)
    {
        thisServ->serviceID = gServiceID;
        gServiceID += 1;
        strcpy(thisServ->serviceType, serviceType);

        thisServ->next = gServiceList;
        gServiceList   = thisServ;

        if (gMDNSPlatformPosixVerboseLevel > 0)
        {
            fprintf(stderr, "%s: Registered service %d, name '%s', type '%s', port %ld\n", gProgramName, thisServ->serviceID, richTextName,
                    serviceType, portNumber);
        }
    }
    else
    {
        if (thisServ != NULL)
        {
            free(thisServ);
        }
    }
    return status;
}
#endif
#if 0 //jeff 
static mStatus RegisterPrivetService(const char *  richTextName,
                                  const char *  serviceType,
                                  const char *  serviceDomain,
                                  const mDNSu8  text[],
                                  mDNSu16       textLen,
                                  long          portNumber)
{
    mStatus             status;
    PosixService *      thisServ;
    domainlabel         name;
    domainname          type;
    domainname          domain;
		DNSCString 					regtype;
		AuthRecord *				SubTypes = NULL;
		int									NumSubTypes = 0;

    int PosixServiceStruLen;
    PosixServiceStruLen = sizeof(*thisServ);
    //xprintf("[BCT Debug]: PosixServiceStruLen=%d\n", PosixServiceStruLen);
    status = mStatus_NoError;
    thisServ = (PosixService *) malloc(sizeof(*thisServ));
    if (thisServ == NULL) {
        status = mStatus_NoMemoryErr;
    }
    if (status == mStatus_NoError) {
        MakeDomainLabelFromLiteralString(&name,  richTextName);
        MakeDomainNameFromDNSNameString(&type, serviceType);
        MakeDomainNameFromDNSNameString(&domain, serviceDomain);
        mDNSPlatformMemCopy(regtype, kPrivetServiceSubType, strlen(kPrivetServiceSubType)+1);
        NumSubTypes = ChopSubTypes(regtype);
				SubTypes = AllocateSubTypes(NumSubTypes, regtype);
				if (NumSubTypes && !SubTypes) return mStatus_NoMemoryErr;

        status = mDNS_RegisterService(&mDNSStorage, &thisServ->coreServ,
                &name, &type, &domain,				// Name, type, domain
                NULL, mDNSOpaque16fromIntVal(portNumber),
                text, textLen,						// TXT data, length
                SubTypes, NumSubTypes,		// Subtypes
                mDNSInterface_Any,				// Interface ID
                RegistrationCallback, thisServ, 0);	// Callback and context

    }
    if (status == mStatus_NoError) {
        thisServ->serviceID = gServiceID;
        gServiceID += 1;
        strcpy(thisServ->serviceType, serviceType);

        thisServ->next = gServiceList;
        gServiceList = thisServ;

        //Added by Dennis Xiang for Privet, 2013/10/12
        TSsemWait(BonjourSem);
	      //tx_semaphore_get(&g_privet_semaphore, TX_WAIT_FOREVER);
        g_privetService = &thisServ->coreServ;
        TSsemSignal(BonjourSem);
	      //tx_semaphore_put(&g_privet_semaphore);

        if (gMDNSPlatformPosixVerboseLevel > 0) {
            fprintf(stderr,
                    "%s: Registered service %d, name '%s', type '%s', port %ld\n",
                    gProgramName,
                    thisServ->serviceID,
                    richTextName,
                    serviceType,
                    portNumber);
        }
    } else {
        if (thisServ != NULL) {
            free(thisServ);
        }
    }
    return status;
}
#endif
typedef struct
{
    char *pKeyStr;
} TxtRecord;

void makeLPDServiceTxt()
{
    TxtRecord txtRecord[TXTNUM] = {{0}};

    unsigned int keylen = 0, i = 0;

    char  str_ty[TMPLEN]          = {0};
    char  str_product[TMPLEN]     = {0};
    char  str_manufacture[TMPLEN] = {0};
    char  str_model[TMPLEN]       = {0};
    char  str_note[TMPLEN]        = {0};
    char *pLPDServiceText         = (char *)gLPDServiceText;
    gLPDServiceTextLen            = 0;

    lni_GetModelName(gServiceName, 64);
    strcpy(str_ty, "ty=");
    strcat(str_ty, gServiceName); //Printer Description
    txtRecord[0].pKeyStr = str_ty;

    strcpy(str_product, "product=(");
    lni_GetModelName(&str_product[9], TMPLEN - 9);
    strcat(str_product, ")");
    txtRecord[1].pKeyStr = str_product;

    strcpy(str_manufacture, "usb_MFG=");
    lni_GetManufactureName(&str_manufacture[8], TMPLEN - 8);
    txtRecord[2].pKeyStr = str_manufacture; //MANUFACTURER(Vendor) name

    //strcpy(str_model, "usb_MDL=");
    // lni_GetModelName(&str_model[8], TMPLEN - 8);
    //txtRecord[3].pKeyStr = str_model; //MODEL name

    strcpy(str_model, "usb_MDL=");                //"Secure Laser Printer"
    strcat(str_model, CONFIG_PRODUCT_SHORT_NAME); //
    txtRecord[3].pKeyStr = str_model;             //MODEL name
    xprintf("[lpd]: model = %s\n", str_model);

    txtRecord[4].pKeyStr = "qtotal=1";
    txtRecord[5].pKeyStr = "rp=printer";
    txtRecord[6].pKeyStr = "priority=20";

    strcpy(str_note, "note="); //location
    lni_GetSystemSetting(6, &str_note[5], TMPLEN - 5);
    txtRecord[6].pKeyStr = str_note;

    for (i = 0; i < TXTNUM; i++)
    {
        if (!txtRecord[i].pKeyStr) continue;

        keylen = strlen(txtRecord[i].pKeyStr);
        if (keylen <= 0) continue;

        *pLPDServiceText = keylen;
        pLPDServiceText++;
        memcpy(pLPDServiceText, txtRecord[i].pKeyStr, keylen);
        pLPDServiceText += keylen;
        gLPDServiceTextLen += keylen + 1;
    }
}

#if CONFIG_SCAN
void makeeSCLServiceTxt()
{
    TxtRecord txtRecord[TXTNUM] = {{0}};

    unsigned int keylen = 0, i = 0;

    char str_ty[TMPLEN]       = {0};
    char str_note[TMPLEN]     = {0};
    char uuid[TMPLEN]         = {0};
    char str_adminurl[TMPLEN] = {0};
    char str_iconurl[TMPLEN]  = {0};

    char *peSCLServiceText = (char *)geSCLServiceText;
    geSCLServiceTextLen    = 0;

    txtRecord[i++].pKeyStr = "txtvers=1";
    txtRecord[i++].pKeyStr = "vers=" CONFIG_PWG_VERSION;
    strcpy(str_adminurl, "adminurl=http://");
    strcat(str_adminurl, sTargetName);
    //strcat(str_adminurl, ".local./setting/setairprint.asp");
    strcat(str_adminurl, ".local./index.asp");
    txtRecord[i++].pKeyStr = str_adminurl;

    strcpy(str_iconurl, "representation=http://");
    strcat(str_iconurl, sTargetName);
    strcat(str_iconurl, ".local./images/icon_128.png");
    txtRecord[i++].pKeyStr = str_iconurl;

    txtRecord[i++].pKeyStr = "rs=" CONFIG_ESCL_URL;

    lni_GetModelName(gServiceName, 64);
    strcpy(str_ty, "ty=");
    strcat(str_ty, gServiceName); //Description
    txtRecord[i++].pKeyStr = str_ty;

    strcpy(str_note, "note="); //location
    lni_GetSystemSetting(6, &str_note[5], TMPLEN - 5);
    txtRecord[i++].pKeyStr = str_note;
#if CONFIG_ESCL_PDF
    txtRecord[i++].pKeyStr = "pdl=application/pdf,image/jpeg";
#else
    txtRecord[i++].pKeyStr = "pdl=image/jpeg";
#endif
    strcpy(uuid, "UUID=");
    build_wsd_ethernet_uuid(uuid + 5);
    //strcpy(uuid, "UUID=00000001-0000-1000-8000-080000888880");
    txtRecord[i++].pKeyStr = uuid;

    txtRecord[i++].pKeyStr = "cs=color,grayscale";
#if CONFIG_ESCL_ADF
    txtRecord[i++].pKeyStr = "is=platen,adf";
#else
    txtRecord[i++].pKeyStr = "is=platen";
#endif
    txtRecord[i++].pKeyStr = "duplex=F";

    for (i = 0; i < TXTNUM; i++)
    {
        if (!txtRecord[i].pKeyStr) continue;

        keylen = strlen(txtRecord[i].pKeyStr);
        if (keylen <= 0) continue;

        *peSCLServiceText = keylen;
        peSCLServiceText++;
        memcpy(peSCLServiceText, txtRecord[i].pKeyStr, keylen);
        peSCLServiceText += keylen;
        geSCLServiceTextLen += keylen + 1;
    }
}
#endif
void make9100ServiceTxt()
{
    TxtRecord txtRecord[TXTNUM] = {{0}};

    unsigned int keylen = 0, i = 0;

    char  str_ty[TMPLEN]          = {0};
    char  str_product[TMPLEN]     = {0};
    char  str_manufacture[TMPLEN] = {0};
    char  str_model[TMPLEN]       = {0};
    char  str_note[TMPLEN]        = {0};
    char *p9100ServiceText        = (char *)g9100ServiceText;
    g9100ServiceTextLen           = 0;

    lni_GetModelName(gServiceName, 64); //"Secure Laser Printer"
    strcpy(str_ty, "ty=");
    strcat(str_ty, gServiceName); //Printer Description
    txtRecord[0].pKeyStr = str_ty;

    //xprintf("[9100]: ServiceName = %s\n",str_ty);

    strcpy(str_product, "product=(");
    lni_GetModelName(&str_product[9], TMPLEN - 9);
    strcat(str_product, ")");
    txtRecord[1].pKeyStr = str_product;
    //xprintf("[9100]: product = %s\n",str_product);

    strcpy(str_manufacture, "usb_MFG="); ////Lenovo Image
    lni_GetManufactureName(&str_manufacture[8], TMPLEN - 8);
    txtRecord[2].pKeyStr = str_manufacture; //MANUFACTURER(Vendor) name
    //xprintf("[9100]: manufacture = %s\n",str_manufacture);

    strcpy(str_model, "usb_MDL="); //"Secure Laser Printer"
    //lni_GetModelName(&str_model[8], TMPLEN - 8);
    strcat(str_model, CONFIG_PRODUCT_SHORT_NAME); //使用短名称
    txtRecord[3].pKeyStr = str_model;             //MODEL name
    xprintf("[9100]: model = %s\n", str_model);

    strcpy(str_note, "note="); //location
    lni_GetSystemSetting(6, &str_note[5], TMPLEN - 5);
    txtRecord[4].pKeyStr = str_note;
    //xprintf("[9100]:note = %s\n",str_note);

    txtRecord[5].pKeyStr = "qtotal=1";
    txtRecord[6].pKeyStr = "priority=10";

    for (i = 0; i < TXTNUM; i++)
    {
        if (!txtRecord[i].pKeyStr) continue;

        keylen = strlen(txtRecord[i].pKeyStr);
        if (keylen <= 0) continue;

        *p9100ServiceText = keylen;
        p9100ServiceText++;
        memcpy(p9100ServiceText, txtRecord[i].pKeyStr, keylen);
        p9100ServiceText += keylen;
        g9100ServiceTextLen += keylen + 1;
    }
}

#if CFG_ippManager
void makeIPPServiceTxt()
{
    TxtRecord txtRecord[TXTNUM] = {{0}};

    unsigned int keylen = 0, i = 0;

    char  str_ty[TMPLEN]          = {0};
    char  str_product[TMPLEN]     = {0};
    char  str_manufacture[TMPLEN] = {0};
    char  str_model[TMPLEN]       = {0};
    char  str_adminurl[TMPLEN]    = {0};
    char  str_note[TMPLEN]        = {0};
    char  uuid[TMPLEN]            = {0};
    char *pIPPServiceText         = (char *)gIPPServiceText;
    gIPPServiceTextLen            = 0;

    txtRecord[0].pKeyStr = "txtvers=1";

    //strcpy(str_note, "note="); //location
    //lni_GetSystemSetting(6, &str_note[5], TMPLEN - 5);

    strcpy(str_note, "note=en-us"); //location  //jeff

    txtRecord[1].pKeyStr = str_note;

    txtRecord[2].pKeyStr = "rp=ipp/print";
    txtRecord[3].pKeyStr = "qtotal=1";
    txtRecord[4].pKeyStr = "priority=40";

    lni_GetModelName(gServiceName, 64);
    strcpy(str_ty, "ty=");
    strcat(str_ty, gServiceName); //Printer Description
    txtRecord[5].pKeyStr = str_ty;

    strcpy(str_product, "product=(");
    lni_GetModelName(&str_product[9], TMPLEN - 9);
    strcat(str_product, ")");
    txtRecord[6].pKeyStr = str_product;

    txtRecord[7].pKeyStr = "pdl=application/pdf,image/urf"; //jeff add image/jpeg for airprint certification
    //	txtRecord[7].pKeyStr = "pdl=application/postscript,application/pdf,image/urf,image/jpeg,application/vnd.hp-PCLXL,application/vnd.hp-PCL";

    strcpy(str_adminurl, "adminurl=http://");
    strcat(str_adminurl, sTargetName);
    //strcat(str_adminurl, ".local./setting/setairprint.asp");
    strcat(str_adminurl, ".local./index.asp");
    txtRecord[8].pKeyStr = str_adminurl;

    strcpy(str_manufacture, "usb_MFG=");
    lni_GetManufactureName(&str_manufacture[8], TMPLEN - 8);
    txtRecord[9].pKeyStr = str_manufacture; //MANUFACTURER(Vendor) name

    strcpy(str_model, "usb_MDL=");
    lni_GetModelName(&str_model[8], TMPLEN - 8);
    txtRecord[10].pKeyStr = str_model; //MODEL name

    //  txtRecord[6].pKeyStr = "printer-state=3";
    //  txtRecord[7].pKeyStr = "printer-type=0x3056";
    //  txtRecord[8].pKeyStr = "Transparent=T";

    txtRecord[11].pKeyStr = "Binary=T";
    txtRecord[12].pKeyStr = "TBCP=T";

    //strcpy(uuid, "UUID=00000001-0000-1000-8000-080000888880");
    strcpy(uuid, "UUID=");
    build_wsd_ethernet_uuid(uuid + 5);
    txtRecord[13].pKeyStr = uuid;
//	txtRecord[14].pKeyStr = "URF=W8,SRGB24,RS200-600,PQ3-4-5,IS1-4-20,MT1-3-4-5,CP1,OB10,IFU0,V1.4";
#if AUTO_DUPLEX
    txtRecord[14].pKeyStr = "URF=W8,SRGB24,RS200-600,PQ3-4-5,IS1-4-20,MT1-3-4-5,DM3,CP1,OB9,OFU0,IFU0,V1.4";
#else
    txtRecord[14].pKeyStr  = "URF=W8,SRGB24,RS200-600,PQ3-4-5,IS1-4-20,MT1-3-4-5,CP1,OB9,OFU0,IFU0,V1.4";
#endif
    //	txtRecord[15].pKeyStr = "Color=T";
    txtRecord[15].pKeyStr = "Color=F";
#if AUTO_DUPLEX
    txtRecord[16].pKeyStr = "Duplex=T";
#else
    txtRecord[16].pKeyStr  = "Duplex=F";
#endif
    txtRecord[17].pKeyStr = "Fax=F";

#if 1 //jeff CONFIG_SCAN
      /*remove ipp
	#if ALTO_PROJECT
		txtRecord[18].pKeyStr = "Scan=T";
	#else*/
    txtRecord[18].pKeyStr = "Scan=T";
    //#endif
#else
    txtRecord[18].pKeyStr  = "Scan=F";
#endif
    txtRecord[19].pKeyStr = "TLS=1.2"; //Added by Dennis Xiang, 2013/05/14
    txtRecord[20].pKeyStr = "air=none";
    txtRecord[21].pKeyStr = "PaperMax=legal-A4";
    txtRecord[22].pKeyStr = "kind=document,envelope";
#if 1 //	mopria
      /******************for mopria 1.3*************************/
    txtRecord[23].pKeyStr = "print_wfds=T";
    txtRecord[24].pKeyStr = "mopria-certified=2.1";
/********************************************************/
#endif
    //	txtRecord[23].pKeyStr = "rfo=ipp/faxout";

    //txtRecord[11].pKeyStr = "Copies=T";
    //txtRecord[14].pKeyStr = "URF=W8,RS600-1200,IS1-4,CP1,DM1";		// Good

    for (i = 0; i < TXTNUM; i++)
    {
        if (!txtRecord[i].pKeyStr) continue;

        keylen = strlen(txtRecord[i].pKeyStr);
        if (keylen <= 0) continue;

        *pIPPServiceText = keylen;
        pIPPServiceText++;

        if (gIPPServiceTextLen + keylen + 1 > sizeof(gIPPServiceText))
            while (1)
                ;

        memcpy(pIPPServiceText, txtRecord[i].pKeyStr, keylen);
        pIPPServiceText += keylen;
        gIPPServiceTextLen += keylen + 1;
    }
}

void makeIPPSServiceTxt()
{
    TxtRecord txtRecord[TXTNUM] = {{0}};

    unsigned int keylen = 0, i = 0;

    char  str_ty[TMPLEN]          = {0};
    char  str_product[TMPLEN]     = {0};
    char  str_manufacture[TMPLEN] = {0};
    char  str_model[TMPLEN]       = {0};
    char  str_adminurl[TMPLEN]    = {0};
    char  str_note[TMPLEN]        = {0};
    char  uuid[TMPLEN]            = {0};
    char *pIPPServiceText         = (char *)gIPPSServiceText;
    gIPPSServiceTextLen           = 0;

    txtRecord[0].pKeyStr = "txtvers=1";

    strcpy(str_note, "note=en-us"); //location  //jeff
    //lni_GetSystemSetting(6, &str_note[5], TMPLEN - 5);
    txtRecord[1].pKeyStr = str_note;

    txtRecord[2].pKeyStr = "rp=ipp/print";
    txtRecord[3].pKeyStr = "qtotal=1";
    txtRecord[4].pKeyStr = "priority=30";

    lni_GetModelName(gServiceName, 64);
    strcpy(str_ty, "ty=");
    strcat(str_ty, gServiceName); //Printer Description
    txtRecord[5].pKeyStr = str_ty;

    strcpy(str_product, "product=(");
    lni_GetModelName(&str_product[9], TMPLEN - 9);
    strcat(str_product, ")");
    txtRecord[6].pKeyStr = str_product;

    txtRecord[7].pKeyStr = "pdl=application/pdf,image/urf";
    //	txtRecord[7].pKeyStr = "pdl=application/postscript,application/pdf,image/urf,image/jpeg,application/vnd.hp-PCLXL,application/vnd.hp-PCL";

    strcpy(str_adminurl, "adminurl=http://");
    strcat(str_adminurl, sTargetName);
    //  strcat(str_adminurl, ".local./setting/setairprint.asp");
    strcat(str_adminurl, ".local./index.asp");
    txtRecord[8].pKeyStr = str_adminurl;

    strcpy(str_manufacture, "usb_MFG=");
    lni_GetManufactureName(&str_manufacture[8], TMPLEN - 8);
    txtRecord[9].pKeyStr = str_manufacture; //MANUFACTURER(Vendor) name

    strcpy(str_model, "usb_MDL=");
    lni_GetModelName(&str_model[8], TMPLEN - 8);
    txtRecord[10].pKeyStr = str_model; //MODEL name

    //  txtRecord[6].pKeyStr = "printer-state=3";
    //  txtRecord[7].pKeyStr = "printer-type=0x3056";
    //  txtRecord[8].pKeyStr = "Transparent=T";

    txtRecord[11].pKeyStr = "Binary=T";
    txtRecord[12].pKeyStr = "TBCP=T";

    //strcpy(uuid, "UUID=00000001-0000-1000-8000-080000888880");
    strcpy(uuid, "UUID=");
    build_wsd_ethernet_uuid(uuid + 5);
    txtRecord[13].pKeyStr = uuid;
//	txtRecord[14].pKeyStr = "URF=W8,SRGB24,RS200-600,PQ3-4-5,IS1-4-20,MT1-3-4-5,CP1,OB10,IFU0,V1.4";
#if AUTO_DUPLEX
    txtRecord[14].pKeyStr = "URF=W8,SRGB24,RS200-600,PQ3-4-5,IS1-4-20,MT1-3-4-5,DM3,CP1,OB9,OFU0,IFU0,V1.4";
#else
    txtRecord[14].pKeyStr  = "URF=W8,SRGB24,RS200-600,PQ3-4-5,IS1-4-20,MT1-3-4-5,CP1,OB9,OFU0,IFU0,V1.4";
#endif
    //	txtRecord[15].pKeyStr = "Color=T";
    txtRecord[15].pKeyStr = "Color=F";
#if AUTO_DUPLEX
    txtRecord[16].pKeyStr = "Duplex=T";
#else
    txtRecord[16].pKeyStr  = "Duplex=F";
#endif
    txtRecord[17].pKeyStr = "Fax=F";
#if CONFIG_SCAN
    /*remove ipp
	#if ALTO_PROJECT
		txtRecord[18].pKeyStr = "Scan=T";
	#else*/
    txtRecord[18].pKeyStr = "Scan=F";
    //#endif
#else
    txtRecord[18].pKeyStr  = "Scan=F";
#endif
    txtRecord[19].pKeyStr = "TLS=1.2"; //Added by Dennis Xiang, 2013/05/14
    txtRecord[20].pKeyStr = "air=none";
    txtRecord[21].pKeyStr = "PaperMax=legal-A4";
    txtRecord[22].pKeyStr = "kind=document,envelope";
#if 0 //mopria
/******************for mopria 1.3*************************/
	txtRecord[23].pKeyStr = "print_wfds=T";
	txtRecord[24].pKeyStr = "mopria-certified=1.3";
/********************************************************/
#endif
    //	txtRecord[23].pKeyStr = "rfo=ipp/faxout";

    //txtRecord[11].pKeyStr = "Copies=T";
    //txtRecord[14].pKeyStr = "URF=W8,RS600-1200,IS1-4,CP1,DM1";		// Good

    for (i = 0; i < TXTNUM; i++)
    {
        if (!txtRecord[i].pKeyStr) continue;

        keylen = strlen(txtRecord[i].pKeyStr);
        if (keylen <= 0) continue;

        *pIPPServiceText = keylen;
        pIPPServiceText++;

        if (gIPPSServiceTextLen + keylen + 1 > sizeof(gIPPSServiceText))
            while (1)
                ;

        memcpy(pIPPServiceText, txtRecord[i].pKeyStr, keylen);
        pIPPServiceText += keylen;
        gIPPSServiceTextLen += keylen + 1;
    }
}
#endif

void makeHttpServiceTxt()
{
    TxtRecord txtRecord[TXTNUM] = {{0}};

    unsigned int keylen = 0, i = 0;

    char  str_ty[TMPLEN]          = {0};
    char  str_product[TMPLEN]     = {0};
    char  str_manufacture[TMPLEN] = {0};
    char  str_model[TMPLEN]       = {0};
    char  str_adminurl[TMPLEN]    = {0};
    char  str_note[TMPLEN]        = {0};
    char *pHttpServiceText        = (char *)gHttpServiceText;
    gHttpServiceTextLen           = 0;

    lni_GetModelName(gServiceName, 64);
    strcpy(str_ty, "ty=");
    strcat(str_ty, gServiceName); //Printer Description
    txtRecord[0].pKeyStr = str_ty;

    strcpy(str_product, "product=(");
    lni_GetModelName(&str_product[9], TMPLEN - 9);
    strcat(str_product, ")");
    txtRecord[1].pKeyStr = str_product;

    strcpy(str_adminurl, "adminurl=http://");
    //lni_GetBonjourSettiing(2, &str_adminurl[16], TMPLEN-16);
    strcat(str_adminurl, sTargetName);

    strcat(str_adminurl, ".local./");
    txtRecord[2].pKeyStr = str_adminurl;

    strcpy(str_manufacture, "usb_MFG=");
    lni_GetManufactureName(&str_manufacture[8], TMPLEN - 8);
    txtRecord[3].pKeyStr = str_manufacture; //MANUFACTURER(Vendor) name

    strcpy(str_model, "usb_MDL=");
    lni_GetModelName(&str_model[8], TMPLEN - 8);
    txtRecord[4].pKeyStr = str_model; //MODEL name

    strcpy(str_note, "note="); //location
    lni_GetSystemSetting(6, &str_note[5], TMPLEN - 5);
    txtRecord[5].pKeyStr = str_note;

    for (i = 0; i < TXTNUM; i++)
    {
        if (!txtRecord[i].pKeyStr) continue;

        keylen = strlen(txtRecord[i].pKeyStr);
        if (keylen <= 0) continue;

        *pHttpServiceText = keylen;
        pHttpServiceText++;
        memcpy(pHttpServiceText, txtRecord[i].pKeyStr, keylen);
        pHttpServiceText += keylen;
        gHttpServiceTextLen += keylen + 1;
    }
}

void makePrivetServiceTxt()
{
    TxtRecord txtRecord[TXTNUM] = {{0}};
    char      str_ty[TMPLEN]    = {0};
    char      str_id[TMPLEN]    = {0};

    unsigned int keylen = 0, i = 0;

    char *pGCPServiceText = (char *)gPrivetServiceText;
    gPrivetServiceTextLen = 0;

    txtRecord[0].pKeyStr = "txtvers=1";

    //lni_GetModelName(gServiceName, 64);
    //lni_GetPrinterName(gServiceName, 64);
    lni_GetBonjourSettiing(3, gServiceName, 80);
    strcpy(str_ty, "ty=");
    strcat(str_ty, gServiceName); //Printer Description
    txtRecord[1].pKeyStr = str_ty;
    txtRecord[2].pKeyStr = "note=Printer emulator";
    txtRecord[3].pKeyStr = "";
    txtRecord[4].pKeyStr = "type=printer";
    strcpy(str_id, "id=");
    //  strcat(str_id, get_cloud_device_id());
    txtRecord[5].pKeyStr = str_id;
    txtRecord[6].pKeyStr = "cs=online";

    for (i = 0; i < TXTNUM; i++)
    {
        if (!txtRecord[i].pKeyStr) continue;

        keylen = strlen(txtRecord[i].pKeyStr);
        if (keylen <= 0) continue;

        *pGCPServiceText = keylen;
        pGCPServiceText++;
        memcpy(pGCPServiceText, txtRecord[i].pKeyStr, keylen);
        pGCPServiceText += keylen;
        gPrivetServiceTextLen += keylen + 1;
    }
}

/**
 * RegisterOurServices_local() - 本地初始化，不做网络注册
 * 只做内存、数据结构等本地资源初始化
 */
static mStatus RegisterOurServices_local(void)
{
    Log(5, "RegisterOurServices_local: preparing local resources\n");
    // 这里只做本地资源准备，不做实际的 mDNS 注册
    // 具体的 mDNS 注册由 NetMonitorTask 统一调度
    return mStatus_NoError;
}

/**
 * update_mdns_hostname() - 强制更新 mDNS 主机名
 * 确保 hostlabel 和 nicelabel 使用正确的主机名
 */
static void update_mdns_hostname(void)
{
    char buf[TMPLEN];
    mDNS *m = &mDNSStorage;

    Log(5, "update_mdns_hostname: Re-initializing mDNS services...\n");

    // 获取 Bonjour 设置的主机名
    memset(buf, 0, sizeof(buf));
    lni_GetBonjourSettiing(2, buf, sizeof(buf));

    Log(5, "update_mdns_hostname: lni_GetBonjourSettiing tempName = %s\n", buf);

    // 更新 sTargetName
    memset(sTargetName, 0, sizeof(sTargetName));
    memcpy(sTargetName, buf, sizeof(sTargetName));

    // 更新 mDNS 的 hostlabel 和 nicelabel
    MakeDomainLabelFromLiteralString(&m->hostlabel, buf);
    MakeDomainLabelFromLiteralString(&m->nicelabel, buf);

    // 重新设置 FQDN
    mDNS_SetFQDN(m);

    Log(5, "update_mdns_hostname: Updated hostlabel and nicelabel to %s\n", buf);
    Log(5, "update_mdns_hostname: hostlabel = %.*s\n", m->hostlabel.c[0], &m->hostlabel.c[1]);
    Log(5, "update_mdns_hostname: nicelabel = %.*s\n", m->nicelabel.c[0], &m->nicelabel.c[1]);

    // 强制刷新网络接口列表，确保 mDNS 在正确的接口上广播
    mStatus status = mDNSPlatformPosixRefreshInterfaceList(m);
    if (status != mStatus_NoError) {
        Log(5, "update_mdns_hostname: mDNSPlatformPosixRefreshInterfaceList failed, status = %d\n", status);
    } else {
        Log(5, "update_mdns_hostname: Network interface list refreshed successfully\n");
    }
}

/**
 * diagnose_mdns_status() - 诊断 mDNS 服务状态
 */
static void diagnose_mdns_status(void)
{
    mDNS *m = &mDNSStorage;
    char ip_str[16];

    Log(5, "=== mDNS 服务诊断 ===\n");

    // 检查 mDNS 核心状态
    Log(5, "mDNS core initialized: %s\n", m->mDNS_busy ? "Yes" : "No");
    Log(5, "mDNS hostlabel: %.*s\n", m->hostlabel.c[0], &m->hostlabel.c[1]);
    Log(5, "mDNS nicelabel: %.*s\n", m->nicelabel.c[0], &m->nicelabel.c[1]);

    // 检查网络接口
    NetworkInterfaceInfo *intf;
    for (intf = m->HostInterfaces; intf; intf = intf->next) {
        if (intf->ip.type == mDNSAddrType_IPv4) {
            snprintf(ip_str, sizeof(ip_str), "%d.%d.%d.%d",
                    intf->ip.ip.v4.b[0], intf->ip.ip.v4.b[1],
                    intf->ip.ip.v4.b[2], intf->ip.ip.v4.b[3]);
            Log(5, "Interface %s: IP=%s, Advertise=%s, Multicast=%s\n",
                intf->ifname, ip_str,
                intf->Advertise ? "Yes" : "No",
                intf->McastTxRx ? "Yes" : "No");
        }
    }

    // 检查注册的服务
    PosixService *service;
    int service_count = 0;
    for (service = gServiceList; service; service = service->next) {
        service_count++;
        Log(5, "Registered service %d: %.*s\n", service_count,
            service->coreServ.RR_SRV.resrec.name->c[0],
            &service->coreServ.RR_SRV.resrec.name->c[1]);
    }
    Log(5, "Total registered services: %d\n", service_count);
    Log(5, "=== mDNS 诊断结束 ===\n");
}

/**
 * try_register_mdns() - 尝试注册 mDNS 服务
 * 检查 IP 是否有效，有效则注册 mDNS
 */
mStatus try_register_mdns(void)
{
    char* ip_str = get_eth0_ip_str();
    mStatus status;

    Log(5, "try_register_mdns: current IP = %s\n", ip_str);

    if (strcmp(ip_str, "0.0.0.0") != 0) {
        Log(5, "IP is ready, updating mDNS hostname and registering services\n");

        // 强制更新主机名
        update_mdns_hostname();

        // 等待一下让网络接口稳定
        sleep(1);

        // 注册服务
        status = RegisterOurServices();

        // 诊断 mDNS 状态
        diagnose_mdns_status();

        if (status == mStatus_NoError) {
            Log(5, "mDNS services registered successfully\n");
        } else {
            Log(5, "mDNS services registration failed, status = %d\n", status);
        }

        return status;
    } else {
        Log(5, "IP not ready (0.0.0.0), skip mDNS registration\n");
        return mStatus_NoError;
    }
}

static mStatus RegisterOurServices(void)
{
    mStatus status      = mStatus_NoError;
    int     portnum9100 = 0, portnumHttp = 0;

    Log(5, "RegisterOurServices\r\n");
    makeLPDServiceTxt();
    make9100ServiceTxt();
#if CFG_ippManager
    makeIPPServiceTxt();
    makeIPPSServiceTxt();
#endif
    makeHttpServiceTxt();
    //makePrivetServiceTxt();
#if CONFIG_SCAN
    makeeSCLServiceTxt();
#endif

#if CFG_ippManager
    unsigned int ippStatus = 0;
    lni_GetIPPEnable(1, &ippStatus, sizeof(ippStatus));
    ippStatus = 1; //jeff
#endif

    memset(gServiceName, 0, 80);
    lni_GetBonjourSettiing(3, gServiceName, 80); //Use Printer Name from EWS setting

    if (gServiceName[0] != 0)
    {
        // if (lni_AppStatusGet(1) == 1)//lpd print
        {
            xprintf("Register LPD ~~\n");
            status = RegisterOneService(gServiceName,
                                        gServiceType, //"_printer._tcp"
                                        gServiceDomain, gLPDServiceText, gLPDServiceTextLen, 515 /*gPortNumber*/);
        }

        // if (lni_AppStatusGet(1) != 1 ||lni_AppStatusGet(0) == 1)//9100 print
        {
            lni_GetPortNumber(0, &portnum9100, sizeof(portnum9100));
            xprintf("Register 9100 ~~\n");
            status = RegisterOneService(gServiceName, "_pdl-datastream._tcp", gServiceDomain, g9100ServiceText, g9100ServiceTextLen, portnum9100);
        }

#if AIRPRINT_ENABLED
        if (ippStatus == 1)
        {
            lni_GetPortNumber(1, &portnumHttp, sizeof(portnumHttp));
            xprintf("Register IPP ~~\n");
            status = RegisterIPPService(gServiceName, "_ipp._tcp", gServiceDomain, gIPPServiceText, gIPPServiceTextLen, 631);

            unsigned int enable_ssl = 0;
            //lni_GetSSLServerCommunicationSetting(1, &enable_ssl);
            SetServ_GetCurrentNetSetting(SETSERV_NET_AirprintIppAuth, &enable_ssl);
            if (enable_ssl)
            {
                xprintf("Register IPPS ~~\n");
                status = RegisterIPPService(gServiceName, "_ipps._tcp", gServiceDomain, gIPPSServiceText, gIPPSServiceTextLen, 631);
            }

#if CONFIG_SCAN
            xprintf("Register ESCL ~~\n");
            status = RegisterOneService(gServiceName, "_uscan._tcp", gServiceDomain, geSCLServiceText, geSCLServiceTextLen, 631);
            if (enable_ssl)
            {
                status = RegisterOneService(gServiceName, "_uscans._tcp", gServiceDomain, geSCLServiceText, geSCLServiceTextLen, 631);
            }
#endif
        }
#endif
        //        lni_GetGcpStatus(&value, sizeof(value));
        //        if (value)
        //        {
        //           //Added by Dennis Xiang, 2013/9/17
        //           xprintf("Register GCP ~~\n");
        //           status = RegisterPrivetService(gServiceName,
        //                                    kPrivetServiceType,
        //                                    kDefaultServiceDomain,
        //                                    gPrivetServiceText, gPrivetServiceTextLen,
        //                                    10101);
        //        }
#if 1 //jeff
        if (1 /*lni_AppStatusGet(3) == 1*/)
        {
            lni_GetPortNumber(1, &portnumHttp, sizeof(portnumHttp));
            //Roger.Huang modification by 2013/10/18
            //Modify for pass "Link-Local to Routable Communicationg of BCT"
            //Keep the HTTP service is the last one.
            xprintf("Register http ~~\n");
            status = RegisterOneService(gServiceName, "_http._tcp", gServiceDomain, gHttpServiceText, gHttpServiceTextLen, 80 /*portnumHttp*/);
        }
#endif
    }
    //.if (status == mStatus_NoError && gServiceFile[0] != 0) {
    //.    status = RegisterServicesInFile(gServiceFile);
    //.}
    return status;
}

static void DeregisterOurServices(void)
{
    PosixService *thisServ;
    int           thisServID;

    while (gServiceList != NULL)
    {
        thisServ     = gServiceList;
        gServiceList = thisServ->next;

        thisServID = thisServ->serviceID;

        mDNS_DeregisterService(&mDNSStorage, &thisServ->coreServ);

        if (gMDNSPlatformPosixVerboseLevel > 0)
        {
            fprintf(stderr, "%s: Deregistered service %d\n", gProgramName, thisServID);
        }
    }

#ifdef TsAdapter_NetModule
    TSsemWait(BonjourSem);
#else
    sem_wait(&nwk_BonjourSem);
#endif

    //tx_semaphore_get(&g_privet_semaphore, TX_WAIT_FOREVER);
    g_privetService = 0;
#ifdef TsAdapter_NetModule
    TSsemSignal(BonjourSem);
#else
    sem_post(&nwk_BonjourSem);
#endif
    //tx_semaphore_put(&g_privet_semaphore);
}

extern int SetServ_GetCurrentNetSetting(eSETSERVNetID id, void *param);
#define EINTR 4 /* Interrupted system call */
int ResponderTask()
{
    mStatus        status = 0;
    int            result;
    mDNSBool       gotData = mDNSfalse;
    mDNSs32        ticks;
    struct timeval timeout;
    sigset_t       signals;
    gStopNow              = 0;
    char bonjour_name[64] = {0};

    memset(bonjour_name, 0, 32);

    //get the TargetName from Nvram
    memset(sTargetName, 0, sizeof(sTargetName));
    lni_GetBonjourSettiing(3, sTargetName, MAX_DOMAIN_LABEL);
    lni_GetBonjourSettiing(3, bonjour_name, MAX_DOMAIN_LABEL);
    xprintf("bonjour_name: %s\n", bonjour_name);

    if (1) //if (sTargetName[0] == 0)
    {
        result = lni_SetBonjourSettiing(0, bonjour_name, strlen(bonjour_name) + 1);
        if (-1 == result) Log(5, "ResponderTask result = %d \n", result);
    }
    else
    {
        Log(5, "ResponderTask sTargetName = %s \n", sTargetName);
    }
    /* Modified by Dennis Xiang, 2011-11-11
 *  while(1)
 */
    if (!gBonjourOn)
    {

        //if(tx_semaphore_create(&g_privet_semaphore, "privet_service_semaphore", 1) != TX_SUCCESS)
        //return (2);
#ifdef TsAdapter_NetModule
        BonjourSem = TScreateSemaphore(0);
        if (BonjourSem == INVALIDSEM)
        {
            return 2;
            Log(5, "Can't create job mutex\n");
        }
#else
        sem_init(&nwk_BonjourSem, 0, 0);
#endif

#ifdef TsAdapter_NetModule
        TSsemSignal(BonjourSem);
#else
        sem_post(&nwk_BonjourSem);
#endif

        status = mDNS_Init(&mDNSStorage, &PlatformStorage, mDNS_Init_NoCache, mDNS_Init_ZeroCacheSize, mDNS_Init_AdvertiseLocalAddresses,
                           /*mDNS_Init_NoInitCallback,*/ mDNSInitCallback, mDNS_Init_NoInitCallbackContext);
        if (status != mStatus_NoError)
        {
            Log(5, "mDNS_Init error\r\n");
            return (2);
        }

        // 只做本地初始化，实际注册由 NetMonitorTask 统一调度
        Log(5, "ResponderTask: doing local initialization only\n");
        status = RegisterOurServices_local();
        if (status != mStatus_NoError)
        {
            Log(5, "RegisterOurServices_local error\r\n");
            return (2);
        }
        Log(5, "ResponderTask: local init done, actual mDNS registration handled by NetMonitorTask\n");

        gBonjourOn = 1;

        while (!gStopNow)
        {

            if (!gotData)
            {
                mDNSs32 nextTimerEvent = mDNS_Execute(&mDNSStorage);
                //				nextTimerEvent = udsserver_idle(nextTimerEvent);
                ticks = nextTimerEvent - mDNS_TimeNow(&mDNSStorage);
                if (ticks < 1) ticks = 1;
            }
            else // otherwise call EventLoop again with 0 timemout
                ticks = 0;

            if (bBonjourNameChanged)
            {
                bBonjourNameChanged = 0;
                DeregisterOurServices();
                status = mDNSPlatformPosixRefreshInterfaceList(&mDNSStorage);
                if (status != mStatus_NoError) break;
                status = RegisterOurServices();
                if (status != mStatus_NoError) break;
            }
            timeout.tv_sec  = ticks / mDNSPlatformOneSecond;
            timeout.tv_usec = (ticks % mDNSPlatformOneSecond) * 1000000 / mDNSPlatformOneSecond;

            timeout.tv_sec = timeout.tv_sec > 1 ? 1 : timeout.tv_sec;
            (void)mDNSPosixRunEventLoopOnce(&mDNSStorage, &timeout, &signals, &gotData);
        }

        Log(5, "Bonjour Exiting...\n");

        DeregisterOurServices();
        mDNS_Close(&mDNSStorage);

        gStopNow   = 0;
        gBonjourOn = 0;
        //tx_semaphore_delete(&g_privet_semaphore);

        /* Deleted by Dennis Xiang, 2011-11-11
 *      lens_sleep(5);
 */
    }

    if (status == mStatus_NoError)
    {
        result = 0;
    }
    else
    {
        result = 2;
    }
    if ((result != 0) || (gMDNSPlatformPosixVerboseLevel > 0))
    {
        fprintf(stderr, "%s: Finished with status %d, result %d\n", gProgramName, (int)status, result);
    }

    return result;
}

//#define RESPONDERSTACKSIZE 1024 * 5
//char Responder_Stack[RESPONDERSTACKSIZE];
int Responder_main(void)
{

    Log(5, "Bonjour Responder_main...\n");

    /*lens_pthread_attr_t attr;
    lens_pthread_t 	Responderid;
    lens_sched_param 	param;
    NET_INT32 ret;*/

    //Terry@20140812 if bonjour has started, just update name.
    if (gBonjourOn)
    {
        mDns_setNameChanged();
        return 0;
    }
    //    tx_thread_create(&Bonjour_Keeper_Thread,             // The thread structure to create.
    //                                  "BonjourKeeper",           // The thread name.
    //                                  KeeperTask,           // The thread entry function.
    //                                  0,                        // The thread entry input parameter.
    //                                  Responder_Stack,         // The stack for our thread.
    //                                  RESPONDERSTACKSIZE, // The stack size in bytes.
    //                                  16,                       // Priority
    //                                  16,                          // Preempt threshold
    //                                  TX_NO_TIME_SLICE,         // Timeslice
    //                                  TX_AUTO_START);           // Automatically start the thread.

    /*
    lens_pthread_attr_init(&attr);
    lens_pthread_attr_getschedparam(&attr, &param);
    param.sched_priority =18;// 19 Leo@take priority over EG(28);  //16->24, Sunup@20090610
    lens_pthread_attr_setschedparam(&attr, &param);

    ret = lens_pthread_create("ResponderTask",&Responderid, &attr, (NET_VOID*)ResponderTask, NET_NULL);
    */

#ifdef TsAdapter_NetModule
    Thread_BonjourTask = TScreateThread(ResponderTask, NETSERVICE_STACK_SIZE, NETSERVICE_TASK_PRIORITY, NULL, "ResponderTask");

    if (Thread_BonjourTask == INVALIDTHREAD)
    {
        Error("Can't start client thread\r\n");
        return -2;
    }
#else
    int res = 0;

    res = pthread_create(&nwk_Thread_BonjourTask, NULL, (void *)ResponderTask, NULL);
    if (res < 0)
    {
        xprintf("Can't start client thread\r\n");
        return -2;
    }
#endif

    /*if(ret == NET_ERROR) 
    {
        lni_NetStatus_BonjRInitError();
        return -1;
    }*/
    //	while(1)
    //		sleep(100);
    return 0;
}
