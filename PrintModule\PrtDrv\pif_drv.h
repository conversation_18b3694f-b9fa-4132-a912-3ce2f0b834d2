#ifndef __pif_drv_h__
#define __pif_drv_h__

#include "type.h"
#include "iomacros.h"
#include "StatusServ.h"
//====== [Todo: resolve loongarch compile error] ======//
#include "skytest.h"
//=====================================================//

#ifdef __PIF_DRV_C__
#define EXTERN /**< nothing when PIF_DRV_C__ is defined              */
#else
#define EXTERN extern /**< Declare external when __PIF_DRV_C__ is not defined */
#endif                /* def __PIF_DRV_C__ */
/************ ********* ********* ********* ********* *********** ****
*               Defines
********* ********* ********* ********* ********* ********* **********/
#define INFERNO53XX 1

/************ Sub Definition ************/

/************for driver variables  ************/

//for Threadx's API
#define msgENGERR_MsgNumber  20
#define msgENGERR_SIZE       16
#define msgPAR2DRV_MsgNumber 5
#define msgPrintPara_SIZE    4


#define DATA_MASK  2//0 is all band data is 0
                    //1 is all band data is 1
					//2 is normal data from prn


typedef enum
{
    ENG_TRAY_MSI = 0x00,
    ENG_TRAY_PSI = 0x01,
} ePrtDrv_EngTray;

typedef enum
{
    PRTDRV_CANCELJOB,
    PRTDRV_RECORDPAGE,
    PRTDRV_RECORDJOBERROR,
    PRTDRV_DORECOVERY,
    PRTDRV_MAXMSGID,
} DRV_ProcessMsgID;

typedef enum
{
#if (!TONER_WITH_CHIP)
    PRTCOMM_RESETTONER,
#endif
#if (!DRUM_WITH_CHIP)
    PRTCOMM_RESETDRUM,
#endif
    PRTCOMM_RESETUSB,
    PRTCOMM_MAXMSGID,
} DRV_DualCommMsgID;

typedef struct
{
    DRV_DualCommMsgID msgId;
    tUint32           para1;
} DRV_DualCommMsg;

typedef struct
{
    long            mtype;
    DRV_DualCommMsg msg;
} stDrvMsgDualCommBuf;

#ifdef EE_Test
typedef enum
{
    EE_TEST_IDLE     = 0,
    EE_TEST_ON_GOING = 0x01,
    EE_TEST_NOT_PASS = 0x02,
    EE_TEST_PASS     = 0x04,
    EE_TEST_MAX
} EE_TEST_STATUS;
#endif

typedef struct
{
    DRV_ProcessMsgID msgId;
    tUint32          para1;
    tUint16          para2;
    tUint16          para3;
    tUint16          para4;
    tUint16          para5;
    tUint16          para6;
} DRV_ProcessMsg;

typedef struct
{
    long           mtype;
    DRV_ProcessMsg msg;
} stDrvMsgProcessBuf;

#define PRTCMD_EVENT_GET_ENV_SENSOR  0x00000001 // get environment sensor value.
#define PRTCMD_EVENT_GET_TONER_SIZE  0x00000002 // get toner id and size value.
#define PRTCMD_EVENT_GET_TONER_STR   0x00000004 // get toner string data.
#define PRTCMD_EVNET_GET_NVM_BACKUP  0x00000008 // get the backup data from NVRAM.
#define PRTCMD_EVENT_GET_NVM_RESTORE 0x00000010 // get the NVRAM restore result from Eng.
#define PRTCMD_EVNET_GET_VERSION_NO  0x00000020 // get the revision number from Eng.
#define PRTCMD_EVENT_GET_DIAG_PAR    0x00000040 // get the engine parameters of Eng.
#define PRTCMD_EVENT_GET_CTLMODE     0x00000080 // get the CTLMODE value of RDSELCTL command

//for HISR
#define DMA_ISR_PRIORITY      5
#define PRTDRV_DMA_STACK_SIZE 2048
#define CONSUMEPAGE           0xEE

//function return definition
#define PRTDRV_OK       0
#define PRTDRV_TIME_OUT -1
#define PRTDRV_ERROR    -2

/************ ********* ********* ********* ********* *********** ****
*               Margin Defines
********* ********* ********* ********* ********* ********* **********/
#define TOP_MARGIN_DEFAULT 95 //@ (20091113 TT modified. Base on --Grande,MCU(ULC)_V.01.28.02. #M1-1)

#define TOP_MARGIN_LIMIT_CUSTOM(h)  h * 1.1811 //(((h/10)/25.4)*600)/2
#define TOP_MARGIN_LIMIT_A4         7008 / 2
#define TOP_MARGIN_LIMIT_B5         6064 / 2
#define TOP_MARGIN_LIMIT_A5         4960 / 2
#define TOP_MARGIN_LIMIT_A6         //(TBD) for 7801
#define TOP_MARGIN_LIMIT_FOLIO      //(TBD) for 7801
#define TOP_MARGIN_LIMIT_LETTER     6600 / 2
#define TOP_MARGIN_LIMIT_HLETTER    3300 / 2 //Half-Letter (TBV) for 7801
#define TOP_MARGIN_LIMIT_EXEC       6296 / 2
#define TOP_MARGIN_LIMIT_LEGAL13    7800 / 2
#define TOP_MARGIN_LIMIT_LEGAL14    8400 / 2
#define TOP_MARGIN_LIMIT_POSTCARD   3496 / 2
#define TOP_MARGIN_LIMIT_C10        5696 / 2 //short edge input 2472/2
#define TOP_MARGIN_LIMIT_MONA_LANDS 2320 / 2 //Monarch
#define TOP_MARGIN_LIMIT_MONA_PORTR 4496 / 2
#define TOP_MARGIN_LIMIT_C5         5408 / 2
#define TOP_MARGIN_LIMIT_DL_LANDS   2592 / 2
#define TOP_MARGIN_LIMIT_DL_PORTR   5192 / 2
#define TOP_MARGIN_LIMIT_INVOICE    5096 / 2 //short edge input 3296/2

//#define LEFT_MARGIN_DEFAULT_CUSTOM(w)   2787-w*1.1811+95 //(((100.341-(((w/10)/2)/1.17599))*1000)/35.998)+95
#define LEFT_MARGIN_DEFAULT_CUSTOM 95
#define LEFT_MARGIN_DEFAULT_A4     95 //in 43xxEVT (ROS Engine +100 =shift 8mm)

#define LEFT_MARGIN_DEFAULT_B5         95
#define LEFT_MARGIN_DEFAULT_A5         95
#define LEFT_MARGIN_DEFAULT_LETTER     95 //(20090930_Grande,MCU(ULC)_V.01.17.00.)
#define LEFT_MARGIN_DEFAULT_EXEC       95
#define LEFT_MARGIN_DEFAULT_LEGAL13    95
#define LEFT_MARGIN_DEFAULT_LEGAL14    95
#define LEFT_MARGIN_DEFAULT_POSTCARD   95
#define LEFT_MARGIN_DEFAULT_C10        95
#define LEFT_MARGIN_DEFAULT_MONA_LANDS 95
#define LEFT_MARGIN_DEFAULT_MONA_PORTR 95
#define LEFT_MARGIN_DEFAULT_C5         95
#define LEFT_MARGIN_DEFAULT_DL_LANDS   95
#define LEFT_MARGIN_DEFAULT_DL_PORTR   95
//#define LEFT_MARGIN_DEFAULT_INVOICE       1137+95
#define LEFT_MARGIN_LIMIT 2787 // 100.341(micro sec.)*1000/35.998(nano sec.)

/************ ********* ********* ********* ********* *********** ****
*           Function name
********* ********* ********* ********* ********* ********* **********/

/************ ********* ********* ********* ********* *********** ****
*           Macro
********* ********* ********* ********* ********* ********* **********/

/********************************************************
        DEBUG
**********************************************************/

/************ ********* ********* ********* ********* *********** ****
*              Function prototypes
********* ********* ********* ********* ********* ********* **********/
int M3doCommand(Uint32  command,           ///< [in] command code
                int     nargs,             ///< [in] No of arguments
                Uint32 *response,          ///< [out] response buffer
                int    *nretargs,          ///< [out] No. of response
                Uint32  args[QM3_MAX_ARGS] ///< [in] argument buffer
);
#if NXDEMO
int M3doCommand_demo(Uint32  command,           ///< [in] command code
                     int     nargs,             ///< [in] No of arguments
                     Uint32 *response,          ///< [out] response buffer
                     int    *nretargs,          ///< [out] No. of response
                     Uint32  args[QM3_MAX_ARGS] ///< [in] argument buffer
);

#endif
tBool  PrtDrv_HWSetup(void);   //call this to create an init task.
tBool  PRTDRV_SLEEP_ON(void);  //To enter the sleep mode of MCU.
void   PRTDRV_SLEEP_OFF(void); //To leave the sleep mode of MCU.
tInt32 PRTDRV_ResetPif(tVoid);
tInt32 PRTDRV_SetupPif(tVoid);
tInt32 PRTDRV_PageConfig(tUint32 width, tUint32 height, tInt32 top_margin, tInt32 left_margin, tUint32 papersize, tUint32 paper_width,
                         tUint32 paper_height);

tUint8                       PrtDrv_CheckEngStatus(tVoid);
tVoid                        PRTDRV_SetMismatch(tUint8 sheetNo, tUint8 short_Long);
Sint32                       PRTDRV_VirtualDriverTask(void *parm);
void                         PRTDRV_InitTask(tBool initFromSleepMode);
void                         PRTDRV_Init(tBool initFromSleepMode);
void                         PRTDRV_ConsumeDmaBandData(void);
extern Uint32                par_GetCurrentTonerPageCounter(tBool needUpdate);
extern Uint32                par_GetCurrentTonerDotCounter(tBool needUpdate);
extern Uint32                par_GetCurrentOPCPageCounter(tBool needUpdate);
extern Uint32                par_GetCurrentOPCDotCounter(tBool needUpdate);
extern void                  ActiveSolenoid(void);
extern void                  Lot26SetNetworkConnectStatus(unsigned char status);
extern tInt8                 Lot26GetNetworkConnectStatus(void);
EXTERN eStatusServ_PrnStatus PRTDRV_GetError(void);
#undef EXTERN
#endif
