WEB_API_DIR=api_fx
EXTRA_DEFINES += UEMF WEBS OPENSSL OS="LINUX" LINUX LENOVO_PROJECT WEBS_SSL_SUPPORT
#EXTRA_DEFINES += UEMF WEBS OS="LINUX" LINUX LENOVO_PROJECT
CDEFS  +=  $(EXTRA_DEFINES:%=-D %)
CFLAGS += $(CDEFS)

obj-y += asp.o
obj-y += balloc.o
obj-y += base64.o
obj-y += cgi.o
obj-y += default.o
obj-y += ejlex.o
obj-y += ejparse.o
obj-y += form.o
obj-y += h.o
obj-y += htmEnc.o
obj-y += htmWrite.o
obj-y += handler.o
obj-y += md5c.o
obj-y += mkcert.o
obj-y += mime.o
obj-y += misc.o
obj-y += mocana_ssl.o
obj-y += page.o
obj-y += ringq.o
obj-y += rom.o
obj-y += security.o
obj-y += sock.o
obj-y += sockGen.o
obj-y += sym.o
obj-y += uemf.o
obj-y += um.o
obj-y += umui.o
obj-y += url.o
obj-y += value.o
obj-y += WebMgr.o
obj-y += webs.o
obj-y += websda.o
obj-y += websmain.o
obj-y += websSSL.o
obj-y += WebString.o
obj-y += websuemf.o
obj-y += copyright.o
obj-y += ewsAddressBook.o
obj-y += ewsLVString.o
obj-y += ewsxc.o
obj-y += ewsxcProperties.o
obj-y += ewsxcStatus.o
obj-y += ewsxcSupport.o
obj-y += IPv6StringMutiLan.o
obj-y += JobsName.o
obj-y += JobsStatus.o
obj-y += JobsTab.o
obj-y += Others.o
obj-y += PaperSize.o
obj-y += PaperType.o
obj-y += PortSetting.o
obj-y += PrinterEvent.o
obj-y += ProperGeneral.o
obj-y += ProperSecurity.o
obj-y += PropertiesFax.o
obj-y += PropertiesLeft.o
obj-y += ProtocolSetting.o
obj-y += statusCoverConsume.o
obj-y += StatusGeneral.o
obj-y += statusInOutTray.o
obj-y += StatusLeft.o
obj-y += struct_JobsName.o
obj-y += Support.o
obj-y += webs_netapi.o
obj-y += portForCPath.o 
obj-y += webrom_lnv_airprint.o
obj-y += emfdb.o
