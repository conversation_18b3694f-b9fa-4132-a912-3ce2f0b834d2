=~=~=~=~=~=~=~=~=~=~=~= PuTTY log 2025.07.03 11:15:10 =~=~=~=~=~=~=~=~=~=~=~=
 ( &/696/0!!)!./t6)(a)!&)9')0578048f
DDR:0660048f
VID:053c048b
PMON2000 LOONGARCH Initializing. Standby...

Start Init Memory, wait a while......
NODE 0 MEMORY CONFIG BEGIN


s1 = 0x00000000__f9a18104

Enable register space of MEMORY
run to wait dram init ok!3

Disable register space of MEMORY

Disable register space of MEMORY done.
Start Hard Leveling...

Enable register space of MEMORY

write leveling begin

all dll_wrdqs set 0

set leveling mode to be WRITE LEVELING

write leveling ready

write leveling finist.h and gate leveling begin

PREAMBLE CHECK!!

The preamble check failed @00000055

PREAMBLE CHECK!!

The preamble check failed @0000003f

PREAMBLE CHECK!!

The preamble check success

PREAMBLE CHECK!!

The preamble check failed @00000055

PREAMBLE CHECK!!

The preamble check success

MC0 Config DONE

msize = 0x00000001

lock scache 90000000 - 90040000
Lock Scache Done.
copy text section to lock cache done.
run in cache.
Copy Bios to memory OK, Uncompressing Bios..........................................................................OK, Booting Bios

unlock scache windows first

flush scache to unlock scache

unlock_base 0x9000000090000000

raw_memsz: 0x1
memorysize: 0xf000000
memorysize_high: 0x10000000
memorysize_total: 0x200
DONE
set ebase done
DEVI
ENVI
MAPV
in envinit
nvram=1c000000
NVRAM@1c0ff000
STDV
80100000:  memory between f800000-f800000  is already been allocated,heap is already above this point
SBDD
Icache 0x8000
Dcache 0x8000
L2 cache 0x80000
L3 cache 0x1
NETI
RTCL
in configure
mainbus0 (root)
localbus0 at mainbus0
MCI: ls mci system init start
emmc0 at localbus0syn0 at localbus0syn0 Mac is invalid, now get a new mac
set syn0  Mac address: 3e:d0:62:f5:46:94 
syn0 Mac is invalid, please use set_mac to update spi mac address
 phy device ID is: 4f51,  phy vendor ID is: e91b
gmac phy: YT8531 recognized
==== YT8531 phy reset ok.
in if attach
syn1 at localbus0syn1 Mac is invalid, now get a new mac
set syn1  Mac address:  a:88:db:8e:24:1e 
syn1 Mac is invalid, please use set_mac to update spi mac address
 phy device ID is: 0,  phy vendor ID is: ffff
==== Warning: unrecognized gmac phy!
in if attach
ohci1 at localbus0OHCI revision: 0x01000010
  RH: a: 0x00000000 b: 0x00000000
early period(0x0)
OHCI b01f800 initialized ok
drive at ohci1 devnum 1, Product OHCI Root Hub
 not configured
ohci0 at localbus0OHCI revision: 0x00000010
  RH: a: 0x02000902 b: 0x00000000
early period(0x0)
OHCI b01f000 initialized ok
drive at ohci0 devnum 2, Product OHCI Root Hub
 not configured
loopdev0 at mainbus0out configure
devconfig done.
ifinit done.
domaininit done.
init_proc....
HSTI
SYMI
SBDE

|||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||
||  |||||||||       |||||       ||||   |||||  |||||      |||||       |||||       ||||   |||||  ||
||  ||||||||   ||||  |||   ||||  |||    ||||  ||||  ||||  |||   ||||  |||   ||||  |||    ||||  ||
||  ||||||||  |||||| |||  |||||| |||  |  |||  |||  ||||||||||||   |||||||  |||||| |||  |  |||  ||
||  ||||||||  |||||| |||  |||||| |||  ||  ||  |||  |||    |||||||    ||||  |||||| |||  ||  ||  ||
||  ||||||||  |||||| |||  |||||| |||  |||  |  |||  |||||  ||||||||||  |||  |||||| |||  |||  |  ||
||  ||||||||   ||||  |||   ||||  |||  ||||    |||   ||||  |||   |||  ||||   ||||  |||  ||||    ||
||       ||||       |||||       ||||  |||||   ||||       |||||      ||||||       ||||  |||||   ||
|||||||||||||||||||||||||||||||||||||||[2020 LOONGSON]|||||||||||||||||||||||||||||||||||||||||||
Gnu C Version: 8.3.0
Standard C Version: 201710
Build date:       Nov 21 2024 at 11:01:48
FW ver:           LK_0.24.003-D

Configuration [loongson,EL,NET,IDE]
Version: PMON 5.0.3-Release (ls2p500) #7: Thu 21 Nov 2024 11:02:11 AM CST commit 2e22b4d7354997958fae009c9ea936c5418cb61d Merge: dc5e538ee 62296a4c5 Author: Yang.Sun <<EMAIL>> .
Supported loaders [txt, srec, elf, bin]
Supported filesystems [tfcard, emmc, mtd, net, ext4, fat, fs, disk, socket, tty, ram]
This software may be redistributed under the BSD copyright.
Copyright 2000-2002, Opsycon AB, Sweden.
Copyright 2005, ICT CAS.
CPU 3A5000 @ 600.00 MHzMemory size 512 MB .
L1 Instruction cache size 32KB (128 Line, 4 Way)
L1 Data cache size 32KB (128 Line, 4 Way)
L2 cache size 512KB
L3 cache size 0KB
|BEV1
BEV in SR set to zero.
dtb chsum err! you should load_dtb before boot kernel!!!
Not find the file or directory!
/Not find the file or directory!
-PmonFwVer:LK_0.24.003-D
BootNum:      0 0 0 0
BootPath:     0 0 0 2
Rtfs1_WFlag_S:0 0 0 28
Rtfs1_WFlag_E:0 0 0 28
Rtfs2_WFlag_S:0 0 0 29
Rtfs2_WFlag_E:0 0 0 29
PmonFwVer:LK_0.24.003-D
BootNum:      0 0 0 1
BootPath:     0 0 0 2
Rtfs1_WFlag_S:0 0 0 28
Rtfs1_WFlag_E:0 0 0 28
Rtfs2_WFlag_S:0 0 0 29
Rtfs2_WFlag_E:0 0 0 29
\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|















































































                                                                                                                                    

                 Boot Menu List                                  Boot Menu List                 

    1 'LoongOS(Safe Mode)'                                                       1 'LoongOS(Safe Mode)'                                                   
    2 'LoongOS(Part A)'                                                          2 'LoongOS(Part A)'                                                      
 -> 3 'LoongOS(Part B)'                                                       -> 3 'LoongOS(Part B)'                                                      
                                                                                                                                                          
                                                                                                                                                          
                                                                                                                                                          
                                                                                                                                                          
                                                                                                                                                          
                                                                                                                                                          
 Please Select Boot Menu [3]                     Please Select Boot Menu [3]                    

Use the UP and DOWN keys to select the entry.Use the UP and DOWN keys to select the entry.
Press ENTER to boot selected OS.Press ENTER to boot selected OS.
Press 'c' to command-line.Press 'c' to command-line.
                                                 Booting system in [3] second(s)                                                  Booting system in [3] second(s) 
                                                 Booting system in [2] second(s) 
                                                 Booting system in [2] second(s) 
Now booting the 'LoongOS(Part B)'
/Loading file: (emmc0,2)/vmlinuz -(elf)
(elf)
0x90000000011d0000/5278208 \|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\ + 0x16d8a00/4202528(z)  + 
Entry address is 90000000011d0000
Loading initrd image (emmc0,2)/rootfs.cpio.gz|/dl_offset 9000000004000000 addr 9000000004000000
(bin)
-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-\|/-
Loaded 17156206 bytes
Boot with parameters: console=tty console=ttyS0,115200 swiotlb=noforce rootwait
dtb chsum err!!!
[    0.000000] Linux version 4.19.190 (osboxes@osboxes) (gcc version 8.3.0 (LoongArch GNU toolchain rc1.4 (20240615))) #1 SMP Thu Jun 12 10:11:11 CST 2025
[    0.000000] 64-bit Loongson Processor probed (LA364 Core)
[    0.000000] CPU0 revision is: 0014b010 (Loongson-64bit)
[    0.000000] FPU0 revision is: 00000000
[    0.000000] efi:  SMBIOS=0xfffe000  INITRD=0xb012b00 
[    0.000000] earlycon: ns16550a0 at MMIO 0x0000000014200000 (options '115200n8')
[    0.000000] bootconsole [ns16550a0] enabled
[    0.000000] SMBIOS 2.6 present.
[    0.000000] DMI: Loongson Loongarch-2P500-V1.0/Loongarch-2P500-V1.0, BIOS Loongson-PMON-V3.3-0AM0 
[    0.000000] CpuClock = 600000000
[    0.000000] The BIOS Version: Loongson-PMON-V3.3-0AM0
[    0.000000] UEFI runtime services will not be available!
[    0.000000] software IO TLB: mapped [mem 0x01237000-0x01237800] (0MB)
[    0.000000] PM: Registered nosave memory: [mem 0x00fcc000-0x00fccfff]
[    0.000000] Detected 1 available CPU(s)
[    0.000000] Zone ranges:
[    0.000000]   DMA32    [mem 0x0000000000200000-0x00000000ffffffff]
[    0.000000]   Normal   empty
[    0.000000] Movable zone start for each node
[    0.000000] Early memory node ranges
[    0.000000]   node   0: [mem 0x0000000000200000-0x000000000effffff]
[    0.000000]   node   0: [mem 0x0000000090000000-0x0000000099ffffff]
[    0.000000] Zeroed struct page in unavailable ranges: 4608 pages
[    0.000000] Initmem setup node 0 [mem 0x0000000000200000-0x0000000099ffffff]
[    0.000000] percpu: Embedded 22 pages/cpu s50200 r8192 d31720 u90112
[    0.000000] CPU0 __my_cpu_offset: 90000
[    0.000000] Built 1 zonelists, mobility grouping on.  Total pages: 100495
[    0.000000] Kernel command line: earlycon console=tty console=ttyS0,115200 swiotlb=noforce rootwait 
[    0.000000] Dentry cache hash table entries: 65536 (order: 7, 524288 bytes)
[    0.000000] Inode-cache hash table entries: 32768 (order: 6, 262144 bytes)
[    0.000000] Memory: 362928K/407552K available (10183K kernel code, 1190K rwdata, 3916K rodata, 900K init, 409K bss, 44624K reserved, 0K cma-reserved)
[    0.000000] SLUB: HWalign=64, Order=0-3, MinObjects=0, CPUs=1, Nodes=1
[    0.000000] ftrace: allocating 34035 entries in 133 pages
[    0.000000] rcu: Hierarchical RCU implementation.
[    0.000000] rcu: RCU restricting CPUs from NR_CPUS=2 to nr_cpu_ids=1.
[    0.000000] rcu: Adjusting geometry for rcu_fanout_leaf=16, nr_cpu_ids=1
[    0.000000] NR_IRQS: 4160, nr_irqs: 4160, preallocated irqs: 16
[    0.000000] Constant clock event device register
[    0.000000] clocksource: Constant: mask: 0xffffffffffffffff max_cycles: 0x171024e7e0, max_idle_ns: 440795205315 ns
[    0.000006] sched_clock: 64 bits at 100MHz, resolution 10ns, wraps every 4398046511100ns
[    0.008106] Constant clock source device register
[    0.013028] Console: colour dummy device 80x25
[    0.018261] console [tty0] enabled
[    0.021742] Calibrating delay loop (skipped), value calculated using timer frequency.. 200.00 BogoMIPS (lpj=400000)
[    0.032261] pid_max: default: 32768 minimum: 301
[    0.037019] Security Framework initialized
[    0.041205] Mount-cache hash table entries: 1024 (order: 1, 8192 bytes)
[    0.047869] Mountpoint-cache hash table entries: 1024 (order: 1, 8192 bytes)
[    0.056489] Performance counters: loongarch/loongson64 PMU enabled, 4 64-bit counters available to each CPU.
[    0.066529] rcu: Hierarchical SRCU implementation.
[    0.073401] smp: Bringing up secondary CPUs ...
[    0.077982] smp: Brought up 1 node, 1 CPU
[    0.082425] devtmpfs: initialized
[    0.088121] random: get_random_u32 called from bucket_table_alloc.isra.9+0x94/0x1e0 with crng_init=0
[    0.097741] clocksource: jiffies: mask: 0xffffffff max_cycles: 0xffffffff, max_idle_ns: 7645041785100000 ns
[    0.107807] futex hash table entries: 256 (order: 2, 16384 bytes)
[    0.114168] pinctrl core: initialized pinctrl subsystem
[    0.120905] NET: Registered protocol family 16
[    0.126762] audit: initializing netlink subsys (disabled)
[    0.134158] cpuidle: using governor menu
[    0.138374] audit: type=2000 audit(1577836800.088:1): state=initialized audit_enabled=0 res=1
[    0.151324] loongson pinctrl 14000490.pinctrl: 3 gpio chip add success, pins 48
[    0.160050] loongson pinctrl 14000490.pinctrl: loongson pinctrl probe success
[    0.211052] HugeTLB registered 2.00 MiB page size, pre-allocated 0 pages
[    0.219048] ACPI: Interpreter disabled.
[    0.224216] vgaarb: loaded
[    0.227618] SCSI subsystem initialized
[    0.232497] ls-spi 14010000.spi: controller is unqueued, this is deprecated
[    0.240950] usbcore: registered new interface driver usbfs
[    0.247230] usbcore: registered new interface driver hub
[    0.252782] usbcore: registered new device driver usb
[    0.258689] pps_core: LinuxPPS API ver. 1 registered
[    0.263747] pps_core: Software ver. 5.3.6 - Copyright 2005-2007 Rodolfo Giometti <<EMAIL>>
[    0.273370] PTP clock support registered
[    0.277918] Registered efivars operations
[    0.283575] clocksource: Switched to clocksource Constant
[    0.365822] pnp: PnP ACPI: disabled
[    0.387011] NET: Registered protocol family 2
[    0.392998] tcp_listen_portaddr_hash hash table entries: 256 (order: 0, 4096 bytes)
[    0.401011] TCP established hash table entries: 4096 (order: 3, 32768 bytes)
[    0.408262] TCP bind hash table entries: 4096 (order: 4, 65536 bytes)
[    0.414825] TCP: Hash tables configured (established 4096 bind 4096)
[    0.421385] UDP hash table entries: 256 (order: 1, 8192 bytes)
[    0.427291] UDP-Lite hash table entries: 256 (order: 1, 8192 bytes)
[    0.433713] NET: Registered protocol family 1
[    0.438545] RPC: Registered named UNIX socket transport module.
[    0.444502] RPC: Registered udp transport module.
[    0.449226] RPC: Registered tcp transport module.
[    0.453950] RPC: Registered tcp NFSv4.1 backchannel transport module.
[    0.460710] Unpacking initramfs...
[    1.123600] random: fast init done
[    1.533444] Freeing initrd memory: 16752K
[    1.540640] Initialise system trusted keyrings
[    1.546217] workingset: timestamp_bits=62 max_order=17 bucket_order=0
[    1.569988] SGI XFS with security attributes, no debug enabled
[    3.115142] Key type asymmetric registered
[    3.119401] Asymmetric key parser 'x509' registered
[    3.124527] Block layer SCSI generic (bsg) driver version 0.4 loaded (major 250)
[    3.131978] io scheduler noop registered
[    3.136271] io scheduler cfq registered (default)
[    3.141012] io scheduler mq-deadline registered
[    3.145574] io scheduler kyber registered
[    3.158861] Serial: 8250/16550 driver, 16 ports, IRQ sharing disabled
[    3.172915] console [ttyS0] disabled
[    3.176779] 14200000.serial: ttyS0 at MMIO 0x14200000 (irq = 16, base_baud = 7812500) is a 16550A
[    3.185897] console [ttyS0] enabled
[    3.185897] console [ttyS0] enabled
[    3.192912] bootconsole [ns16550a0] disabled
[    3.192912] bootconsole [ns16550a0] disabled
[    3.202482] 14200400.serial: ttyS1 at MMIO 0x14200400 (irq = 17, base_baud = 7812500) is a 16550A
[    3.212667] [drm] loongson kernel modesetting driver enable
[    3.231886] loop: module loaded
[    3.237486] libphy: Fixed MDIO Bus: probed
[    3.242901] stmmaceth 14140000.ethernet: no reset control found
[    3.249139] stmmaceth 14140000.ethernet: User ID: 0x11, Synopsys ID: 0x37
[    3.255999] stmmaceth 14140000.ethernet: DWMAC1000
[    3.260942] stmmaceth 14140000.ethernet: DMA HW capability register supported
[    3.268111] stmmaceth 14140000.ethernet: RX Checksum Offload Engine supported
[    3.275276] stmmaceth 14140000.ethernet: COE Type 2
[    3.280176] stmmaceth 14140000.ethernet: TX Checksum insertion supported
[    3.286904] stmmaceth 14140000.ethernet: Wake-Up On Lan supported
[    3.293181] stmmaceth 14140000.ethernet: Enhanced/Alternate descriptors
[    3.299836] stmmaceth 14140000.ethernet: Enabled extended descriptors
[    3.306308] stmmaceth 14140000.ethernet: Ring mode enabled
[    3.311819] stmmaceth 14140000.ethernet: Enable RX Mitigation via HW Watchdog Timer
[    3.334185] libphy: stmmac: probed
[    3.337668] mdio_bus stmmac-0:00: attached PHY driver [unbound] (mii_bus:phy_addr=stmmac-0:00, irq=POLL)
[    3.347195] mdio_bus stmmac-0:01: attached PHY driver [unbound] (mii_bus:phy_addr=stmmac-0:01, irq=POLL)
[    3.358065] dwc2 14100000.otg: 14100000.otg supply vusb_d not found, using dummy regulator
[    3.366975] dwc2 14100000.otg: Linked as a consumer to regulator.0
[    3.373234] dwc2 14100000.otg: 14100000.otg supply vusb_a not found, using dummy regulator
[    3.381729] dwc2 14100000.otg: dwc2_check_params: Invalid parameter lpm=1
[    3.388586] dwc2 14100000.otg: dwc2_check_params: Invalid parameter lpm_clock_gating=1
[    3.396540] dwc2 14100000.otg: dwc2_check_params: Invalid parameter besl=1
[    3.403444] dwc2 14100000.otg: dwc2_check_params: Invalid parameter hird_threshold_en=1
[    3.411484] dwc2 14100000.otg: dwc2_check_params: Invalid parameter g_rx_fifo_size=1024
[    3.419522] dwc2 14100000.otg: dwc2_check_params: Invalid parameter g_np_tx_fifo_size=1024
[    3.427911] dwc2 14100000.otg: EPs: 11, dedicated fifos, 2984 entries in SPRAM
[    3.435799] ehci_hcd: USB 2.0 'Enhanced' Host Controller (EHCI) Driver
[    3.442613] ehci-pci: EHCI PCI platform driver
[    3.447171] ehci-platform: EHCI generic platform driver
[    3.452717] ehci-platform 14030000.ehci: EHCI Host Controller
[    3.458592] ehci-platform 14030000.ehci: new USB bus registered, assigned bus number 1
[    3.466720] ehci-platform 14030000.ehci: irq 22, io mem 0x14030000
[    3.487571] ehci-platform 14030000.ehci: USB 2.0 started, EHCI 1.00
[    3.494733] hub 1-0:1.0: USB hub found
[    3.498709] hub 1-0:1.0: 2 ports detected
[    3.503387] ohci_hcd: USB 1.1 'Open' Host Controller (OHCI) Driver
[    3.509913] ohci-platform: OHCI generic platform driver
[    3.515404] ohci-platform 14038000.ohci: Generic Platform OHCI controller
[    3.522278] ohci-platform 14038000.ohci: new USB bus registered, assigned bus number 2
[    3.530410] ohci-platform 14038000.ohci: irq 21, io mem 0x14038000
[    3.600366] hub 2-0:1.0: USB hub found
[    3.605123] hub 2-0:1.0: 2 ports detected
[    3.610254] usbcore: registered new interface driver usblp
[    3.616422] usbcore: registered new interface driver usb-storage
[    3.623009] i8042: PNP: No PS/2 controller found.
[    3.627775] i8042: Probing ports directly.
[    4.134941] i8042: Can't read CTR while initializing i8042
[    4.140937] i8042: probe of i8042 failed with error -5
[    4.146854] mousedev: PS/2 mouse device common for all mice
[    4.153371] i2c /dev entries driver
[    4.158435] device-mapper: ioctl: 4.39.0-ioctl (2018-04-03) initialised: <EMAIL>
[    4.168175] ls2k_sdio ********.sdio0_emmc: Use exclusive dma engine.
[    4.600772] usbcore: registered new interface driver usbhid
[    4.606422] usbhid: USB HID core driver
[    4.811837] Initializing XFRM netlink socket
[    4.817031] NET: Registered protocol family 10
[    4.822701] Segment Routing with IPv6
[    4.826658] sit: IPv6, IPv4 and MPLS over IPv4 tunneling driver
[    4.833483] NET: Registered protocol family 17
[    4.838242] NET: Registered protocol family 15
[    4.842769] Key type dns_resolver registered
[    4.848036] Loading compiled-in X.509 certificates
[    4.853321] hctosys: unable to open rtc device (rtc0)
[    5.083736] Freeing unused kernel memory: 900K
[    5.088241] This architecture does not have kernel memory protection.
[    5.094710] Run /init as init process
mount: mounting proc on /proc failed: Device or resource busy
[Performance]: rc.local init start
mmcblk0 device found!
check mmcblk0p7 partition.
PLABLE is ********
settings: Superblock last write time (Tue Dec 31 23:23:09 2024,
now = Wed Jan  1 00:00:06 2020) is in the future.
FIXED.
settings: 13/25688 files (0.0% non-contiguous), 3266/102400 blocks
e2fsck 1.45.6 (20-Mar-2020)
settings: clean, 13/25688 files, 3266/102400 blocks
mmcblk0p7 partition check done.
check mmcblk0p8 partition.
PLABLE is 726f7473
storage: Superblock last write time (Tue Dec 31 23:23:09 2024,
now = Wed Jan  1 00:00:07 2020) is in the future.
FIXED.
storage: 16/128016 files (6.3% non-contiguous), 40332/512000 blocks
e2fsck 1.45.6 (20-Mar-2020)
storage: clean, 16/128016 files, 40332/512000 blocks
mmcblk0p8 partition check done.
check mmcblk0p9 partition.
PLABLE is 73676f6c
logs: Superblock last write time (Tue Dec 31 23:23:09 2024,
now = Wed Jan  1 00:00:08 2020) is in the future.
FIXED.
logs: 19/128016 files (21.1% non-contiguous), 22023/512000 blocks
e2fsck 1.45.6 (20-Mar-2020)
logs: clean, 19/128016 files, 22023/512000 blocks
mmcblk0p9 partition check done.
eth0 exists. start phy config
PHY ID 
PHY ID 
Error: Failed to read PHY registers
Starting syslogd: OK
Starting klogd: OK
Running sysctl: OK
Saving random seed: OK
Starting rpcbind: OK
Starting network: OK
Starting ntpd: OK
Starting telnetd: OK
Starting NFS statd: OK
Starting NFS services: point 1
point 2
point 3
OK
Starting NFS mountd: OK
modprobe: module lmem not found in modules.dep
NOMAL

01-01 08:00:09.551 934 I db_json_load: file A is vaild
01-01 08:00:09.552 934 I db_json_load: file B is vaild
01-01 08:00:09.553 934 I sync_db_file: Force sync db_file start, cur:00000001 A:00000008 B:00000000!
01-01 08:00:09.554 934 I sync_db_file: sync db_file A successed.!
01-01 07:19:09.002 934 I ShowVidSysTime: Today is Wed Jan  1 07:19:09 2025
01-01 07:19:09.002 934 I LogRedirect: LogDirection file.
01-01 07:19:09.003 934 I LogRedirect: LogDirection file.
01-01 07:19:09.003 934 I main: char = 1, short = 2, long = 8, long long = 8, short int = 2, int = 4, Point=8
01-01 07:19:09.003 934 I main: tChar=1,tUchar=1,tInt8=1,tUint8=1,tInt16=2,tUint16=2,tInt32=4,tUint32=4,tInt64=8,tUint64=8
01-01 07:19:09.014 941 I logRotateTask: start
01-01 07:19:09.016 940 I sync_db_file: sync db_file B successed.!
01-01 07:19:09.021 934 I lsg_system: exec cmd:   mkdir -p /mnt/huge
[root@LS-GD ~]# 01-01 07:19:09.045 934 I lsg_system: exec cmd:   mount n /mnt/huge -t hugetlbfs
01-01 07:19:09.057 934 I lsg_system: exec cmd:   echo 20 > /sys/kernel/mm/hugepages/hugepages-2048kB/nr_hugepages
01-01 07:19:09.064 934 I lmem_init: lmem init
01-01 07:19:09.068 934 I lmem_open: virtual addr 0x2aaac00000, phy 0xffffffff93e00000
01-01 07:19:09.070 934 I lmem_open: MAX_BLOCK SIZE = 20
01-01 07:19:09.149 934 I MemServ_Initial: MemServ: 0  VAddr= 0x1274d0bc4, size = 3840
01-01 07:19:09.149 934 I MemServ_Initial: MemServ: 1  VAddr= 0x1274dd9a4, size = 18176
01-01 07:19:09.149 934 I MemServ_Initial: MemServ: 2  VAddr= 0x1274e20b4, size = 5632
01-01 07:19:09.150 934 I MemServ_Initial: MemServ: 6  VAddr= 0x1274e36c4, size = 204800
01-01 07:19:09.150 934 I MemServ_Initial: MemServ: 7  VAddr= 0x1275156d4, size = 256000
01-01 07:19:09.150 934 I MemServ_Initial: MemServ: 10  VAddr= 0x127553ee4, size = 65536
01-01 07:19:09.150 934 I MemServ_Initial: MemServ: 19  VAddr= 0x127563ef4, size = 131072
01-01 07:19:09.150 934 I MemServ_Initial: MemServ: 21  VAddr= 0x127583f04, size = 131072
01-01 07:19:09.150 934 I MemServ_Initial: MemServ EXTRA enter -- id =26 
01-01 07:19:09.150 934 I MemServ_Initial: MemServ EXTRA PRINT: 26 PAddr = 0x9cc00000, VAddr= 0x7ff6468000, size = 0x200000
01-01 07:19:09.161 934 I MemServ_Initial: MemServ: 30  VAddr= 0x7ff5c67018, size = 8388608
01-01 07:19:09.161 934 I MemServ_Initial: MemServ: 43  VAddr= 0x1275a3f14, size = 5120
01-01 07:19:09.161 934 I MemServ_Initial: MemServ: 44  VAddr= 0x1275a5324, size = 10752
01-01 07:19:09.161 934 I MemServ_Initial: MemServ: 45  VAddr= 0x1275a7d34, size = 23976
01-01 07:19:09.162 934 I MemServ_Initial: MemServ EXTRA enter -- id =46 
01-01 07:19:09.162 934 I MemServ_Initial: MemServ EXTRA PRINT: 46 PAddr = 0x9d000000, VAddr= 0x7ff5067000, size = 0xc00000
01-01 07:19:09.225 934 I MemServ_Initial: MemServ: 47  VAddr= 0x7ff4c66018, size = 4194304
01-01 07:19:09.225 934 I MemServ_Initial: MemServ EXTRA enter -- id =50 
01-01 07:19:09.226 934 I MemServ_Initial: MemServ EXTRA PRINT: 50 PAddr = 0x9e000000, VAddr= 0x7fef000000, size = 0x1000000
01-01 07:19:09.310 934 I MemServ_Initial: MemServ EXTRA enter -- id =51 
01-01 07:19:09.310 934 I MemServ_Initial: MemServ EXTRA PRINT: 51 PAddr = 0x9f000000, VAddr= 0x7ff4b66000, size = 0x100000
01-01 07:19:09.316 934 I MemServ_Initial: MemServ EXTRA enter -- id =52 
01-01 07:19:09.316 934 I MemServ_Initial: MemServ EXTRA PRINT: 52 PAddr = 0x9f100000, VAddr= 0x7ff4a66000, size = 0x100000
01-01 07:19:09.321 934 I MemServ_Initial: MemServ EXTRA enter -- id =53 
01-01 07:19:09.321 934 I MemServ_Initial: MemServ EXTRA PRINT: 53 PAddr = 0x9f200000, VAddr= 0x7ff4966000, size = 0x100000
01-01 07:19:09.327 934 I MemServ_Initial: MemServ EXTRA enter -- id =54 
01-01 07:19:09.327 934 I MemServ_Initial: MemServ EXTRA PRINT: 54 PAddr = 0x9f300000, VAddr= 0x7ff4866000, size = 0x100000
01-01 07:19:09.332 934 I MemServ_Initial: MemServ: totalCacheSize is (13438888-13123K-12M), totalUnCacheSize is (0-0K-0M)
01-01 07:19:09.332 934 I MemServ_Initial: MemServ: totalDeclareSize is (49090472-47939K-46M)
01-01 07:19:09.333 934 I m2prt_mCoreIsUp: prtTaskLogPhyAddr = 0x9f000000
01-01 07:19:09.333 934 I m2prt_mCoreIsUp: prtTaskLogSize = 0xffff8
01-01 07:19:09.333 934 I m2prt_mCoreIsUp: prtISRLogPhyAddr = 0x9f100000
01-01 07:19:09.333 934 I m2prt_mCoreIsUp: prtISRLogSize = 0xffff8
01-01 07:19:09.333 934 I main: mCoreStartPrtTime 9890, littleCoreRecvTime 1000, mCoreRecvTime 9890, calOffset 8890
01-01 07:19:09.333 934 I Flash_Partion_init: [FlashDriver]partion size 0x2000000
01-01 07:19:09.338 934 I _FlashMgr_CheckData: _FlashMgr_CheckData START !
01-01 07:19:09.372 934 I _FlashMgr_CheckData: _FlashMgr_CheckData Done !
01-01 07:19:09.373 934 I _FlashMgr_SecTableInit: [Flash] Machine size 972, page 4, sector 0, offset 0x100000!
01-01 07:19:09.373 934 I _FlashMgr_SecTableInit: [Flash] Printer size 24, page 1, sector 0, offset 0x110000!
01-01 07:19:09.376 934 I _FlashMgr_SecTableInit: [Flash] Scanner size 480, page 2, sector 0, offset 0x120000!
01-01 07:19:09.376 934 I _FlashMgr_SecTableInit: [Flash] Copier size 100, page 1, sector 0, offset 0x130000!
01-01 07:19:09.376 934 I _FlashMgr_SecTableInit: [Flash] Network size 4632, page 32, sector 2, offset 0x140000!
01-01 07:19:09.377 934 I _FlashMgr_SecTableInit: [Flash] Wi-Fi size 356, page 2, sector 0, offset 0x150000!
01-01 07:19:09.377 934 I _FlashMgr_SecTableInit: [Flash] Engine size 832, page 4, sector 0, offset 0x160000!
01-01 07:19:09.378 934 I _FlashMgr_SecTableInit: [Flash] Eng Counter size 68, page 1, sector 0, offset 0x170000!
01-01 07:19:09.378 934 I _FlashMgr_SecTableInit: [Flash] Counter size 1160, page 8, sector 0, offset 0x180000!
01-01 07:19:09.378 934 I Sector_Cntr_init:  engSec  int 0xff,0xff,0xff,0xff 
01-01 07:19:09.378 934 I Sector_Cntr_init:  engSec  378 int 0xff,0xff,0xff
01-01 07:19:09.379 934 I FlashStorageSettingTest_start: macAddr aa:bb:cc:dd:ee:ff
01-01 07:19:09.379 934 I FlashStorageSettingTest_start: Country: code CN
01-01 07:19:09.379 934 I USBD_Initial: USBD: run command: insmod /usr/lib/modules/4.19.190/qgadget.ko idVendor=4993 idProduct=13064 serialNumber=''
01-01 07:19:09.380 949 I receive_mbox: receive_mbox: Start.
01-01 07:19:09.422 934 I StatusServ_Initial: StatusServ_Initial: Start.
01-01 07:19:09.423 934 I RptMgr_RptJobInfoInit: [RPTM] RptMgr JobInfo Init ok.
01-01 07:19:09.423 934 I RptMgr_RptDataBaseInit: [RPTD] RptMgr DataBase Init ok.
01-01 07:19:09.423 934 I PIS_BufInit: PIS_BufInit: ONE_BEAM type =2 colorStartId=3 colorEndId=3 bandCnt=25
01-01 07:19:09.424 934 I PIS_BufInit: PIS_BufInit: ONE_BEAM type =3 colorStartId=3 colorEndId=3 bandCnt=76
01-01 07:19:09.424 934 I PrintMgr_Init: PrintMgr_Init: virtualMode=0, rawModePDL=0, rawMdoeRAW=1
01-01 07:19:09.425 934 I PRTDRV_Init: DPipe: init Done
01-01 07:19:09.425 934 I prtdrv_M3doCommand: ---- will sendcommand engCmdId=11
01-01 07:19:09.425 969 I PRTDRV_GetError: IREADY NOT READY status=1
01-01 07:19:09.425 969 I PRTDRV_PrtDriverTask: PRTDRV_PrtDriverTask: Start.
01-01 07:19:09.426 970 I PRTDRV_ServantTask: PRTDRV_ServantTask: Start.
01-01 07:19:09.426 971 I PRTDRV_PushBandTask: PRTDRV_PushBandTask: Start.
01-01 07:19:09.426 972 I PRTDRV_EngDualCommTask: PRTDRV_EngDualCommTask: Start.
01-01 07:19:09.426 961 I JobSearchTask: JobSearchTask: Start.
01-01 07:19:09.426 962 I RptMgrTask: RptMgrTask: Start.
01-01 07:19:09.427 966 I PrtMgr_Encode_Task: PrtMgr_Encode_Task: Start.
01-01 07:19:09.427 963 I RptDBMTask: RptDBMTask: Start.
01-01 07:19:09.427 964 I PrtMgr_Decode_Task: PrtMgr_Decode_Task: Start.
01-01 07:19:09.427 965 I PrtMgr_RamDisk_Task: PrtMgr_RamDisk_Task: Start.
01-01 07:19:09.427 967 I PrtMgr_Render_Task: PrtMgr_Render_Task: Start.
01-01 07:19:09.427 968 I PRTDRV_EngCommTask: PRTDRV_EngCommTask: Start.
01-01 07:19:09.427 960 I JobCtrlerTaskCancelJob: JobCtrlerTaskCancelJob: Start.
01-01 07:19:09.427 959 I JobCtrlerTaskJobCtrler: JobCtrlerTaskJobCtrler: Start.
01-01 07:19:09.428 958 I JobCtrlerTaskEventHdler: JobCtrlerTaskEventHdler: Start.
01-01 07:19:09.463 976 I _LShell_Monitor_Task: [LShell] USB or Network is NOT READY yet, LShell is DIS stage!
01-01 07:19:09.464 976 I _Monitor_Dis_Stage: [LOG] [LShell] _Monitor_Dis_Stage: doing local initialization only
01-01 07:19:09.464 976 I _Monitor_Dis_Stage: [LOG] [LShell] USB driver mounted.
01-01 07:19:09.464 976 I _NwDrv_Mount_local: [LOG] [LShell] _NwDrv_Mount_local: preparing local resources
01-01 07:19:09.465 976 I _Monitor_Dis_Stage: [LOG] [LShell] Network driver local init done, actual network listening handled by NetMonitorTask
01-01 07:19:09.465 934 I main: [LOG] [Main] Starting network services...
01-01 07:19:09.465 934 D NetMonitorTask_Init: [LOG] [NetMonitor] Initializing NetMonitorTask
01-01 07:19:09.466 934 D NetMonitorTask_Init: [LOG] [NetMonitor] NetMonitorTask created successfully
01-01 07:19:09.466 974 I Status_Monitor_Task: Status_Monitor_Task: Start.
01-01 07:19:09.468 982 D NetMonitorTask: [LOG] [NetMonitor] NetMonitorTask started
01-01 07:19:09.800 982 I get_eth0_ip_str: [LOG] [NetAPI] get_eth0_ip_str: 0.0.0.0
01-01 07:19:09.469 973 I IoMgrUsbPrtParserMain: IoMgrUsbPrtParserMain: Start.
01-01 07:19:09.801 973 I isUSBConnected: [isUSBVBUSConnected]open /sys/class/udc/14100000.otg/state OK
01-01 07:19:09.801 973 I isUSBConnected: [isUSBVBUSConnected]usb_state_fd select ret 1
01-01 07:19:09.566 934 I EthernetInit: EthernetInit will read mac
01-01 07:19:09.801 934 I EthernetInit: EthernetInit read mac OK [0]=0xaa,[1]=0xbb,[2]=0xcc,[3]=0xdd,[4]=0xee,[5]=0xff
01-01 07:19:09.801 934 I EthernetInit: EthernetInit current mac is valid, check_mac_addr_OK_flag set 1
01-01 07:19:09.801 934 I EthernetInit: EthernetInit default mac don't set
01-01 07:19:09.801 934 I main: [LOG] [Main] NetMonitorTask initialized
01-01 07:19:09.801 934 D Responder_main: Bonjour Responder_main...
01-01 07:19:09.813 934 I SNMPAlertEventHandlerInit: SNMPAlertEventHandlerInit: u32ErrorTableFullItems = 51
01-01 07:19:09.914 985 D ResponderTask: ResponderTask: doing local initialization only
01-01 07:19:09.914 985 D RegisterOurServices_local: RegisterOurServices_local: preparing local resources
01-01 07:19:09.914 985 D ResponderTask: ResponderTask: local init done, actual mDNS registration handled by NetMonitorTask
01-01 07:19:10.679 934 I main: [LOG] [Main] NetServerProlog completed
01-01 07:19:10.679 934 I main: Anny demo-main V6: main...
01-01 07:19:10.679 934 I OCP_start: [PanelServ_Prologue] OCP_start 
01-01 07:19:13.680 934 I main: Anny wait 3...
01-01 07:19:14.801 982 I get_eth0_ip_str: [LOG] [NetAPI] get_eth0_ip_str: 0.0.0.0
01-01 07:19:16.680 934 I main: Anny wait 3...
01-01 07:19:19.680 934 I main: Anny wait 3...
01-01 07:19:19.801 982 I get_eth0_ip_str: [LOG] [NetAPI] get_eth0_ip_str: 0.0.0.0
01-01 07:19:22.680 934 I main: Anny wait 3...
01-01 07:19:24.801 982 I get_eth0_ip_str: [LOG] [NetAPI] get_eth0_ip_str: 0.0.0.0
01-01 07:19:25.680 934 I main: Anny wait 3...
01-01 07:19:28.681 934 I main: Anny wait 3...
01-01 07:19:29.802 982 I get_eth0_ip_str: [LOG] [NetAPI] get_eth0_ip_str: 0.0.0.0
01-01 07:19:31.681 934 I main: Anny wait 3...
01-01 07:19:31.681 934 I main: PRTDRV_InitOK_SendAgain
01-01 07:19:31.683 949 I receive_mbox: receive_mbox: 0x83 Rev mtype=0x3 sts1=0x1010303 sts2=0x7c0601e4
01-01 07:19:31.683 968 I PRTDRV_EngCommTask: Sky: engStatusId.=3, value=0x1, setOrClear=1
01-01 07:19:31.692 949 I receive_mbox: receive_mbox: 0x83 Rev mtype=0x3 sts1=0x20303 sts2=0x7c0601e4
01-01 07:19:31.692 968 I PRTDRV_EngCommTask: Sky: engStatusId.=3, value=0x2, setOrClear=0
01-01 07:19:31.702 949 I receive_mbox: receive_mbox recv 8A
01-01 07:19:34.681 934 I main: Anny wait 3...
01-01 07:19:34.682 934 I main: Anny wait OK V6
01-01 07:19:34.682 934 I main: Anny main start
01-01 07:19:34.802 982 I get_eth0_ip_str: [LOG] [NetAPI] get_eth0_ip_str: 0.0.0.0
01-01 07:19:39.802 982 I get_eth0_ip_str: [LOG] [NetAPI] get_eth0_ip_str: 0.0.0.0
01-01 07:19:44.803 982 I get_eth0_ip_str: [LOG] [NetAPI] get_eth0_ip_str: 0.0.0.0
01-01 07:19:49.803 982 I get_eth0_ip_str: [LOG] [NetAPI] get_eth0_ip_str: 0.0.0.0
01-01 07:19:54.804 982 I get_eth0_ip_str: [LOG] [NetAPI] get_eth0_ip_str: 0.0.0.0
01-01 07:19:59.804 982 I get_eth0_ip_str: [LOG] [NetAPI] get_eth0_ip_str: 0.0.0.0
01-01 07:20:04.804 982 I get_eth0_ip_str: [LOG] [NetAPI] get_eth0_ip_str: 0.0.0.0
01-01 07:20:09.805 982 I get_eth0_ip_str: [LOG] [NetAPI] get_eth0_ip_str: 0.0.0.0
01-01 07:20:14.805 982 I get_eth0_ip_str: [LOG] [NetAPI] get_eth0_ip_str: 0.0.0.0
01-01 07:20:19.806 982 I get_eth0_ip_str: [LOG] [NetAPI] get_eth0_ip_str: 0.0.0.0
01-01 07:20:24.806 982 I get_eth0_ip_str: [LOG] [NetAPI] get_eth0_ip_str: 0.0.0.0

[root@CUMTENN_CTP3006DN_ ~]# ip 01-01 07:20:29.806 982 I get_eth0_ip_str: [LOG] [NetAPI] get_eth0_ip_str: 0.0.0.0
addr
1: lo: <LOOPBACK,UP,LOWER_UP> mtu 65536 qdisc noqueue state UNKNOWN group default qlen 1000
    link/loopback 00:00:00:00:00:00 brd 00:00:00:00:00:00
    inet 127.0.0.1/8 scope host lo
       valid_lft forever preferred_lft forever
    inet6 ::1/128 scope host 
       valid_lft forever preferred_lft forever
2: eth0: <BROADCAST,MULTICAST,UP,LOWER_UP> mtu 1500 qdisc mq state UP group default qlen 1000
    link/ether 3e:d0:62:f5:46:94 brd ff:ff:ff:ff:ff:ff
    inet6 fe80::3cd0:62ff:fef5:4694/64 scope link 
       valid_lft forever preferred_lft forever
3: sit0@NONE: <NOARP> mtu 1480 qdisc noop state DOWN group default qlen 1000
    link/sit 0.0.0.0 brd 0.0.0.0
[root@CUMTENN_CTP3006DN_ ~]# 01-01 07:20:34.807 982 I get_eth0_ip_str: [LOG] [NetAPI] get_eth0_ip_str: 0.0.0.0
01-01 07:20:39.807 982 I get_eth0_ip_str: [LOG] [NetAPI] get_eth0_ip_str: 0.0.0.0
01-01 07:20:43.826 983 I NetMonitor: -----[NetMonitor] eth0:Down!!
01-01 07:20:44.808 982 I get_eth0_ip_str: [LOG] [NetAPI] get_eth0_ip_str: 0.0.0.0
01-01 07:20:49.808 982 I get_eth0_ip_str: [LOG] [NetAPI] get_eth0_ip_str: 0.0.0.0
01-01 07:20:54.808 982 I get_eth0_ip_str: [LOG] [NetAPI] get_eth0_ip_str: 0.0.0.0
01-01 07:20:59.809 982 I get_eth0_ip_str: [LOG] [NetAPI] get_eth0_ip_str: 0.0.0.0
01-01 07:21:04.307 983 I NetMonitor: -----[NetMonitor] eth0:Up!!
01-01 07:21:04.307 983 D Get_IP_ConfigMode: Get_IP_ConfigMode: 5.
01-01 07:21:04.307 983 I Start_IpConfig: ipConfMode = AUTO
01-01 07:21:04.307 983 D Set_DHCP_IP_V4: interface[eth0], Entering.........
01-01 07:21:04.308 983 D Set_DHCP_IP_V4: cmd = [udhcpc -R -b -i eth0 -T 1 -t 30 -x hostname:CUMTENN_CTP3006DN_ -p /var/run/udhcpc.eth0.pid]
01-01 07:21:04.423 983 D Set_DHCP_IP_V4: interface[eth0], Leaving.........
01-01 07:21:04.809 982 I get_eth0_ip_str: [LOG] [NetAPI] get_eth0_ip_str: ***************
01-01 07:21:04.809 982 D NetMonitorTask: [LOG] [NetMonitor] IP changed from 0.0.0.0 to ***************
01-01 07:21:04.810 982 D NetMonitorTask: [LOG] [NetMonitor] IP is ready, starting network services
01-01 07:21:04.810 982 I get_eth0_ip_str: [LOG] [NetAPI] get_eth0_ip_str: ***************
01-01 07:21:04.810 982 D initWebs: [LOG] [WebS] initWebs: current IP = ***************
01-01 07:21:04.810 982 D initWebs: [LOG] [WebS] IP is ready, starting web server network listening
01-01 07:21:04.810 982 D websOpenServer: websOpenServer

01-01 07:21:04.810 982 D websUrlHandlerOpen: websUrlHandlerOpen

01-01 07:21:04.810 982 D websAspOpen: websAspOpen

01-01 07:21:04.810 982 D websOpenListen: DEBUG webs: Listening for HTTP requests at address  host:
01-01 07:21:04.810 982 D initWebs: [LOG] [WebS] Web server network listening started successfully
01-01 07:21:04.811 982 D NetMonitorTask: [LOG] [NetMonitor] Web server started successfully
01-01 07:21:04.811 982 I get_eth0_ip_str: [LOG] [NetAPI] get_eth0_ip_str: ***************
01-01 07:21:04.811 982 D try_register_mdns: try_register_mdns: current IP = ***************
01-01 07:21:04.811 982 D try_register_mdns: IP is ready, registering mDNS services
01-01 07:21:04.811 982 D RegisterOurServices: RegisterOurServices

01-01 07:21:04.812 982 D NetMonitorTask: [LOG] [NetMonitor] mDNS services registered successfully
01-01 07:21:04.812 982 I get_eth0_ip_str: [LOG] [NetAPI] get_eth0_ip_str: ***************
01-01 07:21:04.812 982 I try_start_lshell_net: [LOG] [LShell] try_start_lshell_net: current IP = ***************
01-01 07:21:04.812 982 I try_start_lshell_net: [LOG] [LShell] IP is ready, starting network driver
01-01 07:21:04.812 982 I try_start_lshell_net: [LOG] [LShell] Network driver mounted successfully
01-01 07:21:04.812 982 D NetMonitorTask: [LOG] [NetMonitor] All network services started
01-01 07:21:05.423 983 D Start_IpConfig: ****************************************************
01-01 07:21:05.424 983 D Start_IpConfig: EthernetIPAddress ***************
01-01 07:21:05.424 983 D Start_IpConfig: EthernetSubMask *************
01-01 07:21:05.446 983 D Start_IpConfig: EthernetGateway 0.0.0.0
01-01 07:21:05.460 983 D Start_IpConfig: EthernetPrimaryDns 0.0.0.0
01-01 07:21:05.473 983 D Start_IpConfig: EthernetSecondaryDns 0.0.0.0
01-01 07:21:05.474 983 I get_ipv6_address_with_getifaddrs: Interface: eth0, IPv6: fe80::3cd0:62ff:fef5:4694
01-01 07:21:05.474 983 I get_ipv6_prefix_length: Interface: eth0, IPv6: fe800000000000003cd062fffef54694/64
01-01 07:21:05.489 983 D Start_IpConfig: ****************************************************
01-01 07:21:09.239 940 I sync_db_file: sync db_file A successed.!
01-01 07:21:09.812 982 I get_eth0_ip_str: [LOG] [NetAPI] get_eth0_ip_str: ***************
01-01 07:21:14.250 940 I sync_db_file: sync db_file B successed.!
01-01 07:21:14.813 982 I get_eth0_ip_str: [LOG] [NetAPI] get_eth0_ip_str: ***************
01-01 07:21:19.813 982 I get_eth0_ip_str: [LOG] [NetAPI] get_eth0_ip_str: ***************
01-01 07:21:24.814 982 I get_eth0_ip_str: [LOG] [NetAPI] get_eth0_ip_str: ***************
01-01 07:21:29.814 982 I get_eth0_ip_str: [LOG] [NetAPI] get_eth0_ip_str: ***************
01-01 07:21:30.110 979 I lshell_usb_task_r: [LShell][recv]cmdlen is 0 recvlen is 8 
01-01 07:21:30.110 979 I lshell_usb_task_r: [LShell][recv]recv buffer 4D/3C/2B/1A 00/00/00/00 00/00/00
01-01 07:21:34.815 982 I get_eth0_ip_str: [LOG] [NetAPI] get_eth0_ip_str: ***************
01-01 07:21:39.815 982 I get_eth0_ip_str: [LOG] [NetAPI] get_eth0_ip_str: ***************
01-01 07:21:44.815 982 I get_eth0_ip_str: [LOG] [NetAPI] get_eth0_ip_str: ***************
01-01 07:21:49.816 982 I get_eth0_ip_str: [LOG] [NetAPI] get_eth0_ip_str: ***************
01-01 07:21:54.816 982 I get_eth0_ip_str: [LOG] [NetAPI] get_eth0_ip_str: ***************
01-01 07:21:59.817 982 I get_eth0_ip_str: [LOG] [NetAPI] get_eth0_ip_str: ***************
01-01 07:22:04.817 982 I get_eth0_ip_str: [LOG] [NetAPI] get_eth0_ip_str: ***************
01-01 07:22:09.818 982 I get_eth0_ip_str: [LOG] [NetAPI] get_eth0_ip_str: ***************
01-01 07:22:14.818 982 I get_eth0_ip_str: [LOG] [NetAPI] get_eth0_ip_str: ***************
01-01 07:22:19.818 982 I get_eth0_ip_str: [LOG] [NetAPI] get_eth0_ip_str: ***************
01-01 07:22:24.819 982 I get_eth0_ip_str: [LOG] [NetAPI] get_eth0_ip_str: ***************
01-01 07:22:29.819 982 I get_eth0_ip_str: [LOG] [NetAPI] get_eth0_ip_str: ***************
01-01 07:22:34.820 982 I get_eth0_ip_str: [LOG] [NetAPI] get_eth0_ip_str: ***************
01-01 07:22:39.820 982 I get_eth0_ip_str: [LOG] [NetAPI] get_eth0_ip_str: ***************
01-01 07:22:44.820 982 I get_eth0_ip_str: [LOG] [NetAPI] get_eth0_ip_str: ***************
01-01 07:22:49.821 982 I get_eth0_ip_str: [LOG] [NetAPI] get_eth0_ip_str: ***************
01-01 07:22:54.821 982 I get_eth0_ip_str: [LOG] [NetAPI] get_eth0_ip_str: ***************
01-01 07:22:59.822 982 I get_eth0_ip_str: [LOG] [NetAPI] get_eth0_ip_str: ***************
01-01 07:23:04.822 982 I get_eth0_ip_str: [LOG] [NetAPI] get_eth0_ip_str: ***************
01-01 07:23:09.822 982 I get_eth0_ip_str: [LOG] [NetAPI] get_eth0_ip_str: ***************
01-01 07:23:14.823 982 I get_eth0_ip_str: [LOG] [NetAPI] get_eth0_ip_str: ***************
01-01 07:23:19.823 982 I get_eth0_ip_str: [LOG] [NetAPI] get_eth0_ip_str: ***************
01-01 07:23:24.824 982 I get_eth0_ip_str: [LOG] [NetAPI] get_eth0_ip_str: ***************
01-01 07:23:29.824 982 I get_eth0_ip_str: [LOG] [NetAPI] get_eth0_ip_str: ***************
01-01 07:23:34.824 982 I get_eth0_ip_str: [LOG] [NetAPI] get_eth0_ip_str: ***************
01-01 07:23:39.825 982 I get_eth0_ip_str: [LOG] [NetAPI] get_eth0_ip_str: ***************
01-01 07:23:44.825 982 I get_eth0_ip_str: [LOG] [NetAPI] get_eth0_ip_str: ***************
01-01 07:23:49.826 982 I get_eth0_ip_str: [LOG] [NetAPI] get_eth0_ip_str: ***************
01-01 07:23:54.826 982 I get_eth0_ip_str: [LOG] [NetAPI] get_eth0_ip_str: ***************
01-01 07:23:59.826 982 I get_eth0_ip_str: [LOG] [NetAPI] get_eth0_ip_str: ***************
01-01 07:24:04.827 982 I get_eth0_ip_str: [LOG] [NetAPI] get_eth0_ip_str: ***************
01-01 07:24:09.827 982 I get_eth0_ip_str: [LOG] [NetAPI] get_eth0_ip_str: ***************
01-01 07:24:14.828 982 I get_eth0_ip_str: [LOG] [NetAPI] get_eth0_ip_str: ***************
01-01 07:24:19.828 982 I get_eth0_ip_str: [LOG] [NetAPI] get_eth0_ip_str: ***************
01-01 07:24:24.829 982 I get_eth0_ip_str: [LOG] [NetAPI] get_eth0_ip_str: ***************
01-01 07:24:29.829 982 I get_eth0_ip_str: [LOG] [NetAPI] get_eth0_ip_str: ***************
01-01 07:24:34.829 982 I get_eth0_ip_str: [LOG] [NetAPI] get_eth0_ip_str: ***************
01-01 07:24:39.830 982 I get_eth0_ip_str: [LOG] [NetAPI] get_eth0_ip_str: ***************
01-01 07:24:44.830 982 I get_eth0_ip_str: [LOG] [NetAPI] get_eth0_ip_str