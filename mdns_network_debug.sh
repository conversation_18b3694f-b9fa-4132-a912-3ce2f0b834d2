#!/bin/bash

# mDNS 网络诊断脚本
# 用于诊断局域网 mDNS 广播和解析问题

echo "=== mDNS 网络诊断脚本 ==="
echo "时间: $(date)"
echo ""

# 1. 基本网络信息
echo "=== 1. 基本网络信息 ==="
echo "主机名: $(hostname)"
echo "IP 地址:"
ip addr show eth0 | grep "inet " | awk '{print $2}'
echo "路由信息:"
ip route show | head -3
echo ""

# 2. 检查 mDNS 进程
echo "=== 2. mDNS 相关进程 ==="
ps aux | grep -i mdns | grep -v grep
ps aux | grep -i bonjour | grep -v grep
ps aux | grep -i responder | grep -v grep
echo ""

# 3. 检查网络端口
echo "=== 3. 网络端口检查 ==="
echo "mDNS 端口 (5353):"
netstat -an | grep 5353
echo "IPP 端口 (631):"
netstat -an | grep 631
echo "HTTP 端口 (80):"
netstat -an | grep :80
echo ""

# 4. 检查多播支持
echo "=== 4. 多播支持检查 ==="
echo "网络接口多播支持:"
ip maddr show eth0
echo "多播路由:"
ip route show | grep 224
echo ""

# 5. 检查防火墙
echo "=== 5. 防火墙检查 ==="
echo "iptables 规则 (UDP 5353):"
iptables -L | grep -i 5353 || echo "未找到 5353 端口规则"
echo "iptables 规则 (多播):"
iptables -L | grep -i multicast || echo "未找到多播规则"
echo ""

# 6. 测试本地 mDNS 解析
echo "=== 6. 本地 mDNS 测试 ==="
HOSTNAME=$(hostname)
HOSTNAME_LOCAL="${HOSTNAME,,}.local"  # 转换为小写

echo "测试主机名: $HOSTNAME"
echo "测试 .local 域名: $HOSTNAME_LOCAL"

echo "本地 ping 测试:"
ping -c 1 $HOSTNAME 2>/dev/null && echo "✅ 主机名 ping 成功" || echo "❌ 主机名 ping 失败"
ping -c 1 $HOSTNAME_LOCAL 2>/dev/null && echo "✅ .local 域名 ping 成功" || echo "❌ .local 域名 ping 失败"
echo ""

# 7. 监听 mDNS 流量
echo "=== 7. mDNS 流量监听 (5秒) ==="
echo "监听 mDNS 广播流量..."
timeout 5 tcpdump -i eth0 port 5353 -c 10 2>/dev/null || echo "无法监听 mDNS 流量"
echo ""

# 8. 手动发送 mDNS 查询
echo "=== 8. 手动 mDNS 查询测试 ==="
echo "查询本机 A 记录:"
dig @*********** -p 5353 $HOSTNAME_LOCAL A +short +time=2 2>/dev/null || echo "mDNS 查询失败"
echo ""

# 9. 检查 /etc/hosts 文件
echo "=== 9. /etc/hosts 文件检查 ==="
echo "相关条目:"
grep -E "($(hostname)|\.local)" /etc/hosts || echo "未找到相关条目"
echo ""

# 10. 网络连通性测试
echo "=== 10. 网络连通性测试 ==="
echo "测试网关连通性:"
GATEWAY=$(ip route | grep default | awk '{print $3}' | head -1)
ping -c 1 $GATEWAY 2>/dev/null && echo "✅ 网关连通" || echo "❌ 网关不通"

echo "测试外网连通性:"
ping -c 1 ******* 2>/dev/null && echo "✅ 外网连通" || echo "❌ 外网不通"
echo ""

# 11. 建议的修复命令
echo "=== 11. 建议的修复命令 ==="
echo "如果发现问题，可以尝试以下命令:"
echo ""
echo "# 重启网络接口"
echo "ifdown eth0 && ifup eth0"
echo ""
echo "# 重启 mDNS 服务"
echo "killall mdnsd"
echo ""
echo "# 手动添加多播路由"
echo "route add -net ********* netmask 240.0.0.0 dev eth0"
echo ""
echo "# 开放 mDNS 端口"
echo "iptables -A INPUT -p udp --dport 5353 -j ACCEPT"
echo "iptables -A OUTPUT -p udp --dport 5353 -j ACCEPT"
echo ""

echo "=== 诊断完成 ==="
echo ""
echo "请将此诊断结果发送给技术支持进行分析。"
