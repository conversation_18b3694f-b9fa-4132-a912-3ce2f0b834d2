
#include <errno.h>
#include <sys/wait.h>
#include <unistd.h>
#include <fcntl.h>
#include <stdbool.h>
#include <string.h>
#include <sys/resource.h>
#include <sys/syscall.h>

#include "common.h"
#include "MemServ.h"
#include "JobMgr.h"
#include "versionInfo.h"
#include "../gpio/gpio.h"
#include "db_json.h"
#include "logger.h"
#include "littleCoreLog.h"

#if defined(BUILD_TYPE) && BUILD_TYPE == 1
static const char *s_buildType = "Release Version";
#elif defined(BUILD_TYPE) && BUILD_TYPE == 2
static const char *s_buildType = "UT Version";
#else
static const char *s_buildType = "Debug Version";
#endif

extern void  MemServ_Initial(void);
extern void  FlashSrv_Prologue(void);
extern int   StatusServ_Initial(void);
extern int   StatusServ_Initial(void);
extern tVoid JobMgr_Initial(tVoid);
extern void  RptMgr_Initial(void);
extern void  CodecServ_InitalCodecService(void);
extern tBool PrintMgr_Init(tVoid);
extern tVoid IoMgr_InitialTask(tVoid);
extern tVoid StatusMon_Initial(tVoid);
extern int   MailBox_Initial();
extern void  NetMonitorTask_Init(void);
extern void  USBDataBufInit();
extern int   NetServerProlog();
extern void  JbigHWProlog();
extern void LShell_DbgMsgInit(void);
extern void LShell_Monitor_Prologue(void);
extern int load_little_core(int type);
extern void OCP_start(void);

extern int gpio_cfg(int gpionum, int gpiovalue);

extern int misData_EmmcP5_Init();
extern int misData_EmmcP5_ReadAllMisData(tUint8 *src);
extern int misData_EmmcP5_WritetMP(tUint8 *src);
extern int testFlag;
extern int errno;

extern int64_t mCore2PrtTimeOffset;
extern int64_t mCore2ScaTimeOffset;

#define VENDOR_ID  CONFIG_USB_VID
#define PRODUCT_ID CONFIG_USB_PID

static int s_machine_board_id_vlaue = 0xFF;
extern int getMach_BoardID( void );
extern int gpio_export(const uint16_t gpio_num);
extern int gpio_set_direction(const uint16_t gpio_num, const enum gpio_direction direction);
extern int gpio_get_value(enum gpio_value *value, const uint16_t gpio_num);
extern unsigned int getMachineFunction( unsigned char rv_boardid, unsigned char rv_function_code );

#define MACHINE_TYPE_GPIO1_PIN		(38)
#define MACHINE_TYPE_GPIO2_PIN		(39)
#define MACHINE_TYPE_GPIO3_PIN		(40)
#define MACHINE_COUNT_MAX           (3)
#define FUNCTION_COUNT_MAX          (9)
#define FUNC_HAVE                   (1)
#define FUNC_NONE                   (0)

/* 各机型内部功能模块对应的信息 */
static unsigned int c_machine_function_infomation[MACHINE_COUNT_MAX][FUNCTION_COUNT_MAX] = 
{
    { FUNC_HAVE, FUNC_HAVE,	FUNC_HAVE, FUNC_HAVE, FUNC_HAVE, FUNC_HAVE, FUNC_HAVE, 0x300E, 0x10C9},		/* DM31ADNL */
    { FUNC_HAVE, FUNC_HAVE,	FUNC_HAVE, FUNC_NONE, FUNC_HAVE, FUNC_HAVE, FUNC_HAVE, 0x300E, 0x10CA},		/* DM31DNL */
    { FUNC_HAVE, FUNC_NONE,	FUNC_NONE, FUNC_NONE, FUNC_HAVE, FUNC_HAVE, FUNC_HAVE, 0x300E, 0x10CB}  	/* DP31DNL */			
};

/******************************************************************************
函数名 : getMach_BoardID()
参数   : void
返回值 : int
作者   : 张虎
描述   : 获取BoardID的对外接口函数。
******************************************************************************/
int getMach_BoardID( void )
{
    /* 如果s_machine_board_id_vlaue为初始值，说明还没有获取到正确的Board ID值，报错处理 */
    if(0xFF == s_machine_board_id_vlaue )
    {
         printf("Machine Board ID get Failed\n");
    }
    return s_machine_board_id_vlaue;
}
#if 0
/******************************************************************************
函数名 : getMachineBoardID()
参数   : void
返回值 : void
作者   : 张虎
描述   : 通过读取BoardID的GPIO值，获取BoardID。
******************************************************************************/
static void getMachineBoardID( void )
{
    enum gpio_value at_gpio_1, at_gpio_2, at_gpio_3;
    unsigned char at_board_id;

    /* 从系统获取操作GPIO端口权限 */
    gpio_export(MACHINE_TYPE_GPIO1_PIN);
    gpio_export(MACHINE_TYPE_GPIO2_PIN);
    gpio_export(MACHINE_TYPE_GPIO3_PIN);

    /* 设置GPIO方向 */
    gpio_set_direction( MACHINE_TYPE_GPIO1_PIN, GPIO_IN);
    gpio_set_direction( MACHINE_TYPE_GPIO2_PIN, GPIO_IN);
    gpio_set_direction( MACHINE_TYPE_GPIO3_PIN, GPIO_IN);

    /* 读取GPIO值 */
    gpio_get_value(&at_gpio_1, MACHINE_TYPE_GPIO1_PIN);
    gpio_get_value(&at_gpio_2, MACHINE_TYPE_GPIO1_PIN);
    gpio_get_value(&at_gpio_2, MACHINE_TYPE_GPIO1_PIN);

    /* 整合GPIO读取值 */
    at_board_id =  (at_gpio_1 & 0x01) | ((at_gpio_2 & 0x01) << 1) | ((at_gpio_3 & 0x01) << 2);
    printf("at_board_id= %d, at_gpio_1 = %d,at_gpio_2 = %d,at_gpio_3 = %d\n",at_board_id,at_gpio_1,at_gpio_2,at_gpio_3);

    /* 转换成Board ID */
    switch(at_board_id)
    {
    case 0:
        s_machine_board_id_vlaue = 0;
        break;
    case 4:
        s_machine_board_id_vlaue = 1;
        break;
    case 6:
        s_machine_board_id_vlaue = 2;
        break;
    default:
        s_machine_board_id_vlaue = 0xFF;
        break;
    }    

    printf("Machine Board ID is %d\n",s_machine_board_id_vlaue);
}
#endif
/******************************************************************************
函数名 : getMachineFunction()
参数   : rv_boardid : unsigned char         机器的Board ID值
         rv_function_code : unsigned char   功能代码
返回值 : unsigned int
作者   : 张虎
描述   : 通过BoardID值，获取机器功能参数。
         参数2功能代码定义为：
         0 --- 打印，1 --- 复印，2 --- 扫描，3 --- ADF，4 --- USB，
         5 --- 以太网，6 --- 自动双面打印，7 --- VID，8 --- PID。
         返回值的定义为
         1 - 7 返回值为0，则表示没有该功能；返回值为1，则表示有该功能；
         8 - 9 返回值为对应的VID和PID值；
         如果返回值为0xFFFF，则表明机器的Board ID值或者功能代码参数错误。
******************************************************************************/
unsigned int getMachineFunction( unsigned char rv_boardid, unsigned char rv_function_code )
{
    /* 参数检查 */
    if((rv_boardid > MACHINE_COUNT_MAX) || (rv_function_code > FUNCTION_COUNT_MAX))
    {
        return 0xFFFF;
    }

    return c_machine_function_infomation[rv_boardid][rv_function_code];    
}

#ifdef LOONGSON_COREDUMP_ENABLE
static void CoreDumpEnable()
{
    int           iRes = RLIMIT_CORE;
    struct rlimit limitParam;
    limitParam.rlim_cur = 1 ? RLIM_INFINITY : 0;
    limitParam.rlim_max = 1 ? RLIM_INFINITY : 0;
    if (setrlimit(iRes, &limitParam))
    {
        return;
    }

    system("echo /storage/%e.coredump > /proc/sys/kernel/core_pattern");

    return;
}
#endif
int sys_cmd(const char *cmdstring)
{
    pid_t pid;
    int   status;

    if (cmdstring == NULL) return (1);

    if ((pid = vfork()) < 0)
    {
        status = -1;
    }
    else if (pid == 0)
    {
        execl("/bin/sh", "sh", "-c", cmdstring, (char *)0);
        return -1;
    }
    else
    {
        while (waitpid(pid, &status, 0) < 0)
        {
            if (errno != EINTR)
            {
                status = -1;
                break;
            }
        }
    }

    return (status);
}

// 获取系统运行的毫秒数
static uint64_t getSystemRunTime_ms()
{
    struct timespec ts;

    // 使用 CLOCK_MONOTONIC 获取系统启动以来的时间
    if (clock_gettime(CLOCK_MONOTONIC, &ts) != 0) {
        perror("clock_gettime failed");
        return 0;
    }

    // 将秒和纳秒转换为毫秒
    return (uint64_t)ts.tv_sec * 1000 + ts.tv_nsec / 1000000;
}

int InitBootNum(void)
{
    char misData[74] = {0};
	printf("InitBootNum:enter\n");
    if (misData_EmmcP5_Init())
    {
        printf("Error:Mis Data Init Failed\n");
        return -1;
    }
    if (misData_EmmcP5_ReadAllMisData(misData) <= 0)
    {
        printf("Error:Mis Data Read Failed\n");
        return -1;
    }
    else
    {
        misData[50] = 0;
        misData[51] = 0;
        misData[52] = 0;
        misData[53] = 0;
        if (misData_EmmcP5_WritetMP(misData) <= 0)
        {
            printf("Error:Mis Data Write Failed\n");
            return -1;
        }
    }
	printf("InitBootNum:end\n");
    return 0;
}

void USBD_Initial(void)
{
    //int     rc;
    char sysCmd[100] = {0};

    char serialNO[32] = {""};

    snprintf(sysCmd, 128,
             "insmod /usr/lib/modules/4.19.190/qgadget.ko"
             " idVendor=%d idProduct=%d serialNumber='%s'",
             VENDOR_ID, PRODUCT_ID, serialNO);
#if 1
    PSPRINTF("USBD: run command: %s\n", sysCmd);
#endif
    //rc =
    sys_cmd(sysCmd);
    //rc =
    sys_cmd("ls /dev/qusb*");
}

int main(int argc, char *argv[])
{
    int i;
    uint64_t mCoreStartPrtTime = 0;
    uint64_t mCoreStartScaTime = 0;
    uint64_t littleCoreRecvTime = 0;
    uint64_t mCoreRecvTime = 0;

#ifdef LOONGSON_COREDUMP_ENABLE
    CoreDumpEnable();
#endif
    db_json_init();
    SetSysTime(0);
    LogInit();
    printf("---- LOONSGON MAIN %s ----\n", s_buildType);
#ifdef __GNUC__
    printf("Gnu C Version: %d.%d", __GNUC__, __GNUC_MINOR__);
    if (__GNUC__ >= 3)
    {
        printf(".%d", __GNUC_PATCHLEVEL__);
    }
    printf("\n");
#endif

#ifdef __STDC_VERSION__
    printf("Standard C Version: %ld\n", __STDC_VERSION__);
#endif
    printf("Build date:       %s at %s\n", __DATE__, __TIME__);
    printf("FW ver:           %s\n", VERSION_INFO);
    PSPRINTF("char = %d, short = %d, long = %d, long long = %d, short int = %d, int = %d, Point=%d\n", sizeof(char), sizeof(short), sizeof(long),
             sizeof(long long), sizeof(short int), sizeof(int), sizeof(int *));

    PSPRINTF("tChar=%d,tUchar=%d,tInt8=%d,tUint8=%d,tInt16=%d,tUint16=%d,tInt32=%d,tUint32=%d,tInt64=%d,tUint64=%d\n", sizeof(tChar), sizeof(tUchar),
             sizeof(tInt8), sizeof(tUint8), sizeof(tInt16), sizeof(tUint16), sizeof(tInt32), sizeof(tUint32), sizeof(tInt64), sizeof(tUint64));
    //getMachineBoardID();
            
    LShell_DbgMsgInit();
    //prt
    load_little_core(0);
    //sca
    //load_little_core(1);
    
    MemServ_Initial();

    littleCoreLogInit();

    mCoreStartPrtTime = getSystemRunTime_ms();
    littleCoreRecvTime = (uint64_t)m2prt_mCoreIsUp(MemServ_GetPWorkingBufPtr(MemServ_prtCoreTaskLogBuf), MemServ_prtCoreTaskLogBufSize - 8,
                    MemServ_GetPWorkingBufPtr(MemServ_prtCoreISRLogBuf), MemServ_prtCoreISRLogBufSize - 8);
    //MarkHsu@250627 - CT4 not real send MBOX to prtCore, fake 1000ms delay
    littleCoreRecvTime = 1000;
    mCoreRecvTime = getSystemRunTime_ms();
    //小核时间到大核时间的差值，大核总是早于小核
    mCore2PrtTimeOffset = ((mCoreStartPrtTime - littleCoreRecvTime) + (mCoreRecvTime - littleCoreRecvTime)) / 2;
    PSPRINTF("mCoreStartPrtTime %d, littleCoreRecvTime %d, mCoreRecvTime %d, calOffset %d\n", 
                mCoreStartPrtTime, littleCoreRecvTime, mCoreRecvTime, mCore2PrtTimeOffset);

    FlashSrv_Prologue();
    MailBox_Initial();
    USBD_Initial();
    StatusServ_Initial();
    JobMgr_InitSystemMode(JOBMGR_NormalMode);
    JobMgr_Initial();
    RptMgr_Initial();
#if 1
    JbigHWProlog();
#endif

    CodecServ_InitalCodecService();
    PrintMgr_Init();

    IoMgr_InitialTask();

    StatusMon_Initial();

    LShell_Monitor_Prologue();

    for (i = 0; i < 3; i++)
    {
        if (InitBootNum() != 0)
        {
            printf("Error:BootNum Init Failed\n");
        }
    }
#if 1 //MarkHsu@250514 - open network service after confirm, LPR/9100 OK
	  NetMonitorTask_Init();
	  NetServerProlog();
#endif
#if 0
    {    	
    	sleep(3);
    	extern void JobMgr_PrintReport_forKey5(void);
    	JobMgr_PrintReport_forKey5();
    }
#endif

    PSPRINTF("Anny demo-main V6: main...\n");
	//write io50 1,set led1 off
//	gpio_cfg(50,1);
	OCP_start();

    // MarkHsu@Prepare for scan flow
    #if(0)
    if (MachCheckSupportScan())
    {
        ScanProlog();
        IIprolog();
        PIPEprolog();
        ScanApp_Initial();
    }
    else
    {
        PSPRINTF("Not support Scan Mode\n");
    }
    #endif

#if NXDEMO

    while (!testFlag)
    {
        extern void PRTDRV_InitOK_SendAgain(void);
        static int timeout_counter = 0;
        
        PSPRINTF("Anny wait 3...\n");
        //if (timeout_counter > 5)
        {
          PSPRINTF("PRTDRV_InitOK_SendAgain\n");
        	PRTDRV_InitOK_SendAgain();
        	//timeout_counter = 0;
        }
        //timeout_counter++;
		sleep(3);
    }
    PSPRINTF("Anny wait OK V6\n");
#endif
#if 0		  
	  {
		 PSPRINTF("Anny wait OK...load data\n");
		USBDataBufInit();
		PSPRINTF("Anny prepare OK data OK...\n");
	  }
#endif
    PSPRINTF("Anny main start\n");
    while (1)
    {
        sleep(3000000);
    }
    return 0;
}
