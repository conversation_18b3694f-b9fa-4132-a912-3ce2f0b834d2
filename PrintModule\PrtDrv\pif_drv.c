/**
 * @file pif_drv.c
FILE NAME : pif_drv.c
PURPOSE   : Print management and IOT Communication.
AUTHOR    : <PERSON>
PHASE     :
Document  :
History   :
Date        Author        Version  Description
----------  ------------  -------  ---------------------------------
            Barton

 */

#ifndef __PIF_DRV_C__
#define __PIF_DRV_C__

/************ MAJOR DEFINE (These defines can only be used in this c file.)************/

/************ Sub Definition ************/

/************ EXTERNAL DEPENDENCY ************/
#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <stdarg.h>
#include <unistd.h>
#include "type.h"   //(eg. tUint32, tSin8, etc..)
#include "common.h" //Used to define OS_IF's task priority and task stack size
#include "rtc.h"    //Real time clock
#include "JobMgr.h" //paper type
#include "SetServ.h"
#include "MemServ.h"
#include "../PrtMgr/PrtMain.h"
#include "pif_debug.h"
#include "pif_drv.h"
#include "pif_par.h"
#include "StatusServ.h"
#include "ReportMgr.h"
//#include "SecurePrtJob.h"
//#include "print.h" //for QM3_MAX_ARGS
#include "print_command.h" //for M3doCommand's Command

#include "ENG2CTL.h"
#include "FlashSrv.h"                                        //for sFLASHSRV_TonerInfo
#include "../../FlashManager/Setting/Spec/SetServ_Machine.h" //for SetServ_MACHINE_DrumResetFlag
#include "CTL2ENG.h"
#include "CodecServ.h"
#if PRINTMODULE_DEBUG_PRINTF_ENABLE
#define PRTDRV_PRINTF PSPRINTF
#else
#define PRTDRV_PRINTF(...)
#endif

/************ Global variables ************/
#define PRT_CHECK_SIZE_TYPE 0
#define CFG_Engine_Type     3
#define PURE_VIRTUAL_MODE   0
//.#define PRT_LPRI_PRE_SIZE       (LPRI1_BASE-LPRI0_BASE)
//.#define PRT_IDBASE_PRE_SIZE     (IDBASE1-IDBASE0)
//.#define PRT_ENDIAN_PRE_SIZE     (FR_ENDIAN1-FR_ENDIAN0)
#define PRINT_ALWAYS_TEST 0
#define PRINT_USELASTBANDFEED   1
// BSP@2018, beside the DMA stop, we'd better to disable LDDx output immediately, too
#if 1
#define PRT_STOP_DMA_IMMEDIATELY(dmaCh)                                                                                                           \
    {                                                                                                                                             \
        IO_WRITE32(LPRI0LPMODE + PRT_LPRI_PRE_SIZE * dmaCh, (IO_READ32(LPRI0LPMODE + PRT_LPRI_PRE_SIZE * dmaCh) & 0xFFFFFFF3) | 0x00000004);      \
        WRREG_UINT32(MFP_OVLCTL1,                                                                                                                 \
                     ((RDREG_UINT32(MFP_OVLCTL1) & (~MFP_OVLCTL1__PRIO0__MASK)) | (OVLCTL1_PRIO0_TGENPI0_TGENPO0 << MFP_OVLCTL1__PRIO0__SHIFT))); \
        WRREG_UINT32(TG_BANK0_IODIR, ((RDREG_UINT32(TG_BANK0_IODIR) & 0xFFFFFFFE) | 0x01));                                                       \
    }
#endif
#if 0
#define PRT_STOP_DMA_IMMEDIATELY(dmaCh)                                                                                                      \
    {                                                                                                                                        \
        IO_WRITE32(LPRI0LPMODE + PRT_LPRI_PRE_SIZE * dmaCh, (IO_READ32(LPRI0LPMODE + PRT_LPRI_PRE_SIZE * dmaCh) & 0xFFFFFFF3) | 0x00000004); \
        IO_WRITE32(MFP_OVLCTL1,                                                                                                              \
                   ((IO_READ32(MFP_OVLCTL1) & (~MFP_OVLCTL1__PRIO0__MASK)) | (OVLCTL1_PRIO0_TGENPI0_TGENPO0 << MFP_OVLCTL1__PRIO0__SHIFT))); \
        IO_WRITE32(TG_BANK1_IODIR, (IO_READ32(TG_BANK1_IODIR) | 0x01));                                                                      \
    }
#endif

Sint16 LMargin_E = 0;
int    msgVirtualEng;

int          msgQDriver, msgQServant, msgQEngComm, msgQEngDualComm, msgQCommResponse, msgQPushPageBand, msgQFlushPage;

Uint8 EngineReadyInSleepMode  = 0; //0: Engine normal mode. 1: Engine ready to sleep 2: PAR_ReadStatus2 is ready to stop loop
tBool EngineAllowShutdown     = tFALSE;
tBool EngineInSleepProcessing = tFALSE;

unsigned char TonerSN[]         = "tONER";
unsigned char DrumSN[]          = "dRUM";
Uint8         serialnoArray[21] = {0};

typedef struct
{
    long   mtype;
    tUint8 virtualBuf[msgENGERR_SIZE];
} stPrtDrvVirtualMsgBuf;

//BANDINFO* gpst4DmaSendingBand1[JOBMGR_MAXCOLORID] = {NULL, NULL, NULL, NULL};
//BANDINFO* gpst4DmaSendingBand2[JOBMGR_MAXCOLORID] = {NULL, NULL, NULL, NULL};

eJobMgr_CMYK colorMapping_ChannelToPrtMgr_evt0[JOBMGR_MAXCOLORID] = {JOBMGR_Yellow, JOBMGR_Cyan, JOBMGR_Black, JOBMGR_Magenta};
tUint8       colorMapping_PrtMgrToChannel_evt0[JOBMGR_MAXCOLORID] = {0, 3, 1, 2};

#if DUAL_BEAM
eJobMgr_CMYK colorMapping_ChannelToPrtMgr_others[JOBMGR_MAXCOLORID] = {JOBMGR_Cyan, JOBMGR_Magenta, JOBMGR_Yellow, JOBMGR_Black};
tUint8       colorMapping_PrtMgrToChannel_others[JOBMGR_MAXCOLORID] = {0, 1, 2, 3};
#else
eJobMgr_CMYK colorMapping_ChannelToPrtMgr_others[JOBMGR_MAXCOLORID] = {JOBMGR_Black, JOBMGR_Cyan, JOBMGR_Magenta, JOBMGR_Yellow};
tUint8       colorMapping_PrtMgrToChannel_others[JOBMGR_MAXCOLORID] = {3, 1, 2, 0};

#endif

eJobMgr_CMYK *colorMapping_ChannelToPrtMgr = &colorMapping_ChannelToPrtMgr_others[0];
tUint8       *colorMapping_PrtMgrToChannel = &colorMapping_PrtMgrToChannel_others[0];

extern PrtMgr_Manager PrintManager;

sFLASHSRV_TonerInfo *TonerInstallHistory = tNULL;
sFLASHSRV_TonerInfo *DrumInstallHistory  = tNULL;
#if DUAL_BEAM
#define MAXDMACNT 4
#endif

/******************************************************************************
  Status Service
 ******************************************************************************/
//static Uint8 vpllMode = 0;
//#define PULLHI_CTL_ENGSLP   IO_WRITE32(PIODATC, IO_READ32(PIODATC) | 0x00000001)
//#define PULLLO_CTL_ENGSLP   IO_WRITE32(PIODATC, IO_READ32(PIODATC) & 0xFFFFFFFE)

//#define PULLHI_PSU_SW       IO_WRITE32(PIODATA, IO_READ32(PIODATA) | 0x00400000)
//#define PULLLO_PSU_SW       IO_WRITE32(PIODATA, IO_READ32(PIODATA) & 0xFFBFFFFF)

/******************************************************************************/
/* Below two table are depended on job manager defined resolution table */
tPrivate tUint16 PrtMgrResConvTableX[JOBMGR_DPIMAX + 1] = {
    203,  //JOBMGR_Standard
    203,  //JOBMGR_Fine
    203,  //JOBMGR_SuperFine
    406,  //JOBMGR_ExtraFine
    203,  //JOBMGR_HalfToneStandard
    203,  //JOBMGR_HalfToneFine
    203,  //JOBMGR_HalfToneSuperFine
    406,  //JOBMGR_HalfToneExtraFine
    150,  //JOBMGR_DPI150x150
    200,  //JOBMGR_DPI200x100
    200,  //JOBMGR_DPI200x200
    600,  //JOBMGR_DPI300x150
    300,  //JOBMGR_DPI300x300
    400,  //JOBMGR_DPI400x400
    600,  //JOBMGR_DPI600x600
    600,  //JOBMGR_DPI600x600x2bit
    600,  //JOBMGR_DPI600x600x4bit
    1200, //JOBMGR_DPI1200x1200
    600,  //JOBMGR_DPIMAX
};

tPrivate tUint16 PrtMgrResConvTableY[JOBMGR_DPIMAX + 1] = {
    98,   //JOBMGR_Standard
    196,  //JOBMGR_Fine
    392,  //JOBMGR_SuperFine
    392,  //JOBMGR_ExtraFine
    98,   //JOBMGR_HalfToneStandard
    196,  //JOBMGR_HalfToneFine
    392,  //JOBMGR_HalfToneSuperFine
    392,  //JOBMGR_HalfToneExtraFine
    150,  //JOBMGR_DPI150x150
    100,  //JOBMGR_DPI200x100
    200,  //JOBMGR_DPI200x200
    600,  //JOBMGR_DPI300x150
    300,  //JOBMGR_DPI300x300
    400,  //JOBMGR_DPI400x400
    600,  //JOBMGR_DPI600x600
    600,  //JOBMGR_DPI600x600x2bit
    600,  //JOBMGR_DPI600x600x4bit
    1200, //JOBMGR_DPI1200x1200
    600,  //JOBMGR_DPIMAX
};

tUint16 PrtMgrAreaTableX[JOBMGR_UnknowPaperSize + 1] = {
    4832,  //mm    inch  lines
    6784,  //JOBMGR_A3L //(297-8)/2.54*600,6827
    5760,  //JOBMGR_B4L //5882
    4832,  //JOBMGR_A4L //205.0 8.071  4842
    4160,  //JOBMGR_B5L //177.0 6.969 4181
    2848,  //JOBMGR_B6L //(128-8)/2.54*600,2835
    3392,  //JOBMGR_A5L //144.0 5.669 3401
    4928,  //JOBMGR_FLS //(216-8)/2.54*600,4913
    4832,  //JOBMGR_A4C
    4160,  //JOBMGR_B5C
    2848,  //JOBMGR_B6C
    3392,  //JOBMGR_A5C
    10016, //JOBMGR_Ledger //(432-8)/2.54*600,10016
    4992,  //JOBMGR_11x14  //210.9 8.303  4981
    4960,  //JOBMGR_LegalL //210.9 8.303  4981
    4960,  //JOBMGR_LetterL//210.9 8.303  4981
    3072,  //JOBMGR_InvoiceL//134.7 5.303  3181
    4960,  //JOBMGR_LetterC
    3072,  //JOBMGR_InvoiceC
    5120,  //JOBMGR_8KL
    5120,  //JOBMGR_16KL
    5120,  //JOBMGR_16KC
    4224,  //JOBMGR_Executive//179.2 7.055  4233
    4960,  //JOBMGR_Folio    //210.9 8.303  4981
    2240,  //JOBMGR_PostCard //95.0  3.740 2244
    2336,  //JOBMGR_EnvelopCOM10 //99.8  3.929 2357
    2176,  //JOBMGR_EnvelopMonarch //93.4  3.677 2206
    2496,  //JOBMGR_EnvelopDL //106.0 4.173 2504
    3680,  //JOBMGR_EnvelopC5 //157.0 6.181 3708
    4352,  //JOBMGR_EnvelopMonarchC //185.5  7.303 4381
    5088,  //JOBMGR_EnvelopDlC//216.0 8.504 5102
    3488,  //JOBMGR_WPostCard //148.0 5.826 3496
#if 0
    2496, //JOBMGR_Yougata2
    2112, //JOBMGR_Yougata3
    2304, //JOBMGR_Yougata4
    2112, //JOBMGR_Yougata6
    4288, //JOBMGR_Younagata3
    4288, //JOBMGR_Naqaqate4,
    4288, //JOBMGR_Kakuquta3,
    4288, //JOBMGR_Naqaqate3,
#endif
    2688, //JOBMGR_EnvYokei2 //114.0 4.488 2692
    2304, //JOBMGR_EnvYokei3 //98.0  3.858 2314
    2464, //JOBMGR_EnvYokei4 //105.0 4.134 2480
    2304, //JOBMGR_EnvYokei6 //98.0  3.858 2314
    2816, //JOBMGR_EnvYochokei3 //120.0 4.724 2834
    2816, //JOBMGR_EnvChokei3 //120.0 4.724 2834
    2112, //JOBMGR_EnvChokei4 //90.0  3.543 2126
    5088, //JOBMGR_EnvKakukei3 //216.0 8.504 5102
    2688, //JOBMGR_EnvYokei2C
    2304, //JOBMGR_EnvYokei3C
    4832, //JOBMGR_FreeSize
    4832, //JOBMGR_UnknowPaperSize
};

tUint16 PrtMgrAreaTableY[JOBMGR_UnknowPaperSize + 1] = {
    6848, //mm    inch  lines
    9728, //JOBMGR_A3L //(420-8)/2.54*600,9733
    8320, //JOBMGR_B4L //8410
    6848, //JOBMGR_A4L //293.0 11.535 6921
    5888, //JOBMGR_B5L //253.0 9.961 5976
    4128, //JOBMGR_B6L //(182-8)/2.54*600,4110
    4800, //JOBMGR_A5L //206.0 8.110 4866
    7616, //JOBMGR_FLS //(330-8)/2.54*600,7606
    6848, //JOBMGR_A4C
    5888, //JOBMGR_B5C
    4128, //JOBMGR_B6C
    4800, //JOBMGR_A5C
    6432, //JOBMGR_Ledger  //(280-8)/2.54*600,6425
    8320, //JOBMGR_11x14 //351.6 13.843 8305
    8224, //JOBMGR_LegalL //326.2 13.843 8305
    6336, //JOBMGR_LetterL //for Mantis13296, cut 96lins(4mm)
    4928, //JOBMGR_InvoiceL //211.9 8.343  5005
    6336, //JOBMGR_LetterC
    4928, //JOBMGR_InvoiceC
    6912, //JOBMGR_8KL
    6912, //JOBMGR_16KL
    6912, //JOBMGR_16KC
    6112, //JOBMGR_Executive //262.7 10.343 6205
    7616, //JOBMGR_Folio //326.2 12.843 7705
    3328, //JOBMGR_PostCard //144.0 5.669 3401
    5536, //JOBMGR_EnvelopCOM10 //237.3 9.343 5605
    4320, //JOBMGR_EnvelopMonarch //186.5 7.343 4405
    4992, //JOBMGR_EnvelopDL //215.0 8.465 5079
    5248, //JOBMGR_EnvelopC5 //225.0 8.858 5314
    2144, //JOBMGR_EnvelopMonarchC //94.4   3.717 2230
    2400, //JOBMGR_EnvelopDlC //105.0 4.134 2480
    4704, //JOBMGR_WPostCard //200.0 7.874 4724
#if 0
    3648, //JOBMGR_Yougata2
    3296, //JOBMGR_Yougata3
    5376, //JOBMGR_Yougata4
    4288, //JOBMGR_Yougata6
    4288, //JOBMGR_Younagata3
    4288, //JOBMGR_Naqaqate4,
    4288, //JOBMGR_Kakuquta3,
    4288, //JOBMGR_Naqaqate3,
#endif
    3808, //JOBMGR_EnvYokei2 //162.0 6.378 3826
    3488, //JOBMGR_EnvYokei3 //148.0 5.826 3496
    5536, //JOBMGR_EnvYokei4 //235.0 9.252 5551
    4480, //JOBMGR_EnvYokei6 //190.0 7.480 4488
    5536, //JOBMGR_EnvYochokei3 //235.0 9.252 5551
    5536, //JOBMGR_EnvChokei3 //235.0 9.252 5551
    4832, //JOBMGR_EnvChokei4 //205.0 8.070 4842
    6528, //JOBMGR_EnvKakukei3 //277.0 10.905 6543
    3808, //JOBMGR_EnvYokei2C
    3488, //JOBMGR_EnvYokei3C
    6848, //JOBMGR_FreeSize
    6848, //JOBMGR_UnknowPaperSize,
};

par_StatusS par_Status[] = {
    {{{CMD_ECC62, "ServiceCallError"},
      {CMD_SR33, "OperatorCallError"},
      {CMD_SR32, "RequestFromEngine"},
      {CMD_SR42, "PrintDisable"},
      {CMD_SR36, "Misprint"},
      {0x00, "Paper Delivery"},
      {CMD_SR35, "Warning"},
      {0x00, NULL}},
     (unsigned char *)&eng_Info.BasicStatus}  /*ST0*/,
    {{{CMD_SR2, "Main Tray"}, {CMD_SR4, "By-passTray"}, {CMD_SR6, "ExitTray"}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}},
     (unsigned char *)&eng_Info.PaperTrayStatus}  /*ST1*/,
    {{{0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}},
     (unsigned char *)&eng_Info.MainTrayStatus}  /*ST2*/,
    {
     {{0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}},
     NULL, }  /*ST3*/,
    {{{0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}},
     (unsigned char *)&eng_Info.BypassTrayStatus}  /*ST4*/,
    {{{0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}}, NULL}  /*ST5*/,
    {{{0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, "TrayStatus"}},
     (unsigned char *)&eng_Info.ExistTrayStatus}  /*ST6*/,
    {
     {{0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}},
     NULL, }  /*ST7*/,
    {
     {{0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}},
     NULL, }  /*ST8*/,
    {
     {{0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}},
     NULL, }  /*ST9*/,
    {
     {{0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}},
     NULL, }  /*ST10*/,
    {
     {{0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}},
     NULL, }  /*ST11*/,
    {
     {{0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}},
     NULL, }  /*ST12*/,
    {
     {{0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}},
     NULL, }  /*ST13*/,
    {{{0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}}, NULL}  /*ST14*/,
    {{{0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}}, NULL}  /*ST15*/,
    {
     {{0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}},
     NULL, }  /*ST16*/,
    {{{0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}},
     (unsigned char *)&eng_Info.Side1PrintExecutionCounterStatus}  /*ST17*/,
    {{{0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}}, NULL}  /*ST18*/,
    {{{0x00, "Regist"}, {0x00, "Exit"}, {0x00, "Fuser"}, {0x00, "Bypass"}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}},
     (unsigned char *)&eng_Info.RemainingPaper}  /*ST19*/,
    {{{0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}}, NULL}  /*ST20*/,
    {{{0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}}, NULL}  /*ST21*/,
    {{{0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}}, NULL}  /*ST22*/,
    {{{0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}}, NULL}  /*ST23*/,
    {{{0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}}, NULL}  /*ST24*/,
    {{{0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}}, NULL}  /*ST25*/,
    {{{0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}}, NULL}  /*ST26*/,
    {{{0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}}, NULL}  /*ST27*/,
    {{{0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}}, NULL}  /*ST28*/,
    {{{0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}}, NULL}  /*ST29*/,
    {{{CMD_SR31, "LifeInfo"}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {CMD_ECC56, "KCartrigeInfo"}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}},
     (unsigned char *)&eng_Info.SupplyInfoStatus}  /*ST30*/,
    {{{CMD_ECC51, "SupplyInfo"}, {0x00, NULL}, {CMD_ECC53, "UnitSupplyInfo"}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}},
     (unsigned char *)&eng_Info.LifeInfoStatus}  /*ST31*/,
    {{{0x00, NULL}, {CMD_EEC34, "MachibneInfo"}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}},
     (unsigned char *)&eng_Info.CommandRequestDetail}  /*ST32*/,
    {{{CMD_SR48, "Cover Open"},
      {CMD_SR49, "Unit Installation Status"},
      {0x00, "Paper Unavailable"},
      {CMD_SR19, "Paper Jam"},
      {0x00, "Other Operator Call"},
      {0x00, NULL},
      {CMD_SR60, "Consumable Supplies Life End"},
      {0x00, NULL}},
     (unsigned char *)&eng_Info.OperatorCall}  /*ST33*/,
    {{{0x00, "Warming-Up"}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, "Overheat"}, {0x00, NULL}},
     (unsigned char *)&eng_Info.WaitDetailStatus}  /*ST34*/,
    {{{CMD_SR37, "Warning Detail Status1"},
      {0x00, NULL},
      {CMD_SR39, "Warning Detail Status3"},
      {0x00, NULL},
      {0x00, NULL},
      {0x00, NULL},
      {CMD_ECC62, "loggingSC"},
      {0x00, NULL}},
     (unsigned char *)&eng_Info.WarningTypeStatus}  /*ST35*/,
    {{{0x00, "Paper Empty NG"},
      {0x00, "Mismatch Paper Size"},
      {0x00, NULL},
      {0x00, NULL},
      {0x00, "Print Executer NG"},
      {0x00, NULL},
      {0x00, NULL},
      {0x00, NULL}},
     (unsigned char *)&eng_Info.MisprintDetailStatus}  /*ST36*/,
    {{{0x00, "Near End of K Toner"}, {0x00, "Near End of K OPC"}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}},
     (unsigned char *)&eng_Info.WarningDetailStatus1}  /*ST37*/,
    {{{0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}}, NULL}  /*ST38*/,
    {{{0x00, "End of K Toner"},
      {0x00, "End of K OPC"},
      {0x00, NULL},
      {0x00, NULL},
      {0x00, "End of Paper Feed Roller Unit"},
      {0x00, "End of Transfer Roller Unit"},
      {0x00, "End of Fuser Unit"},
      {0x00, NULL}},
     (unsigned char *)&eng_Info.WarningDetailStatus3}  /*ST39*/,
    {{{0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}}, NULL}  /*ST40*/,
    {{{0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}}, NULL}  /*ST41*/,
    {{{CMD_SR34, "Wait Detail"},
      {CMD_SR43, "Energy Saving Detail"},
      {0x00, NULL},
      {0x00, NULL},
      {0x00, NULL},
      {0x00, NULL},
      {0x00, "Diagnostic Mode Command"},
      {0x00, NULL}},
     (unsigned char *)&eng_Info.PrintDisableDetailStatus}  /*ST42*/,
    {{{0x00, "Sleep Mode"}, {0x00, "Low Power Mode"}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}},
     (unsigned char *)&eng_Info.EnergySavingDetailStatus}  /*ST43*/,
    {{{0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}}, NULL}  /*ST44*/,
    {{{0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}}, NULL}  /*ST45*/,
    {{{0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}}, NULL}  /*ST46*/,
    {{{0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}}, NULL}  /*ST47*/,
    {{{0x00, NULL}, {0x00, "Cover Open"}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}},
     (unsigned char *)&eng_Info.CoverOpenStatus}  /*ST48*/,
    {{{0x00, "AIO"}, {0x00, "CRUM"}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}},
     (unsigned char *)&eng_Info.UnitInstallationStatus}  /*ST49*/,
    {{{0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}}, NULL}  /*ST50*/,
    {{{0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}}, NULL}  /*ST51*/,
    {{{0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}}, NULL}  /*ST52*/,
    {{{0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}}, NULL}  /*ST53*/,
    {{{0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}}, NULL}  /*ST54*/,
    {{{0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}}, NULL}  /*ST55*/,
    {{{0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}}, NULL}  /*ST56*/,
    {{{0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}}, NULL}  /*ST57*/,
    {{{0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}}, NULL}  /*ST58*/,
    {{{0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}}, NULL}  /*ST59*/,
    {{{0x00, NULL}, {0x00, "End of K-OPC"}, {0x00, NULL}, {0x00, NULL}, {0x00, "End of Waste Toner Box"}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}},
     (unsigned char *)&eng_Info.LifeEndStatus}  /*ST60*/,
    {{{0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}}, NULL}  /*ST61*/,
    {{{0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}}, NULL}  /*ST62*/,
    {{{0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}}, NULL}  /*ST63*/,
    {{{0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}}, NULL}  /*ST64*/,
    {{{0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}}, NULL}  /*ST65*/,
    {{{0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}}, NULL}  /*ST66*/,
    {{{0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}}, NULL}  /*ST67*/,
    {{{0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}}, NULL}  /*ST68*/,
    {{{0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}}, NULL}  /*ST69*/,
    {{{0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}}, NULL}  /*ST70*/,
    {{{0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}}, NULL}  /*ST71*/,
    {{{0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}}, NULL}  /*ST72*/,
    {{{0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}}, NULL}  /*ST73*/,
    {{{0x00, "Result of Diagnostic Mode Function"},
      {0x00, NULL},
      {0x00, NULL},
      {0x00, NULL},
      {0x00, NULL},
      {0x00, NULL},
      {0x00, "Diagnostic Mode Function Status"},
      {0x00, NULL}},
     (unsigned char *)&eng_Info.DiagnosticModeFunctionStatus}  /*ST74*/,
    {{{0x00, "Engine Location"}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}, {0x00, NULL}},
     (unsigned char *)&eng_Info.EngineLocationInfomation}  /*ST75*/
};

/************ ********* ********* ********* ********* ***********
 *              Macro
 ********* ********* ********* ********* ********* ********* **********/
//macros for PageConfig()
#define PRTDRV_GetSuitTopMargin(return_margin, set_margin, margin_limit) \
    if ((return_margin = set_margin + TOP_MARGIN_DEFAULT) > margin_limit) return_margin = TOP_MARGIN_DEFAULT

#define PRTDRV_GetSuitLeftMargin(return_margin, set_margin, margin_default) \
    if ((return_margin = set_margin + margin_default) > LEFT_MARGIN_LIMIT) return_margin = margin_default

#if 0
#define PRTDRV_LOCKFOREVER                                       \
    do {                                                         \
        PRTDRV_PRINTF("DPipe:%s line %d\n", __FILE__, __LINE__); \
        TASKSLEEP_MILLISECONDS(50);                              \
    } while (1)
#endif

/************ ********* ********* ********* ********* ***********
 *              Prototype
 ********* ********* ********* ********* ********* ********* **********/
//void PRTDRV_DMADONE_HISR(int dmaCh);
void   PRTDRV_DMADONE_ISR(tUint64 vector);
Uint32 par_GetRealTonerCntFromFlash(void);
Uint32 par_GetRealDrumCntFromFlash(void);
int    par_UpdateTonerInfo_IncInstallationInHistory(sFLASHSRV_TonerInfo NewToner);
int    par_UpdateDrumInfo_IncInstallationInHistory(sFLASHSRV_TonerInfo NewDrum);

int par_SendRecvCmd(unsigned char *sendCmd, unsigned char *recvCmd, unsigned char numofBytes)
{
    int            rv = 0, i = 0, j = 0, M3CommandLen = 1, numofParameterBytes = numofBytes - 1;
    Uint32         args[30] = {0};
    int            nretargs = 0;
    Uint32         response = 0;
    unsigned char *pSendCmd = sendCmd;

    args[0] = numofParameterBytes;
    args[0] |= (sendCmd[0] << 8);
    args[0] |= M3CommandLen << 16;

    if (numofParameterBytes == 0)
        M3CommandLen = 1;
    else if (numofParameterBytes == 1)
    {
        args[1]      = sendCmd[1];
        M3CommandLen = 2;
    }
    else if (numofParameterBytes > 1)
    {
        if (numofParameterBytes % 4 == 0)
            M3CommandLen += (numofParameterBytes / 4);
        else
            M3CommandLen += (numofParameterBytes / 4) + 1;

        args[0] &= 0xFFFF;
        args[0] |= M3CommandLen << 16;
        pSendCmd++;

        for (i = 1; i < M3CommandLen; i++)
        {
            args[i] = 0;

            for (j = 0; j < 4; j++)
            {
                args[i] |= (*pSendCmd << (j * 8));
                pSendCmd++;
            }
        }
    }

#if 0
    PRTDRV_PRINTF("Sky[A7]: 0x%08x, 0x%08x, M3CommandLen=%d\n", args[0], args[1], M3CommandLen);
#endif

    rv = M3doCommand(PRINT_SENDRECV_COMMNAD, M3CommandLen, &response, &nretargs, args);

    return rv;
}

int prtdrv_M3doCommand(ENGCMD_ID engCmdId, unsigned char *sendCmd, Uint32 *result)
{
    int    rv = 0, M3CommandLen = 1;
    Uint32 args[30] = {0};
    int    nretargs = 0;
    Uint32 response = 0;

    args[0] = engCmdId;

    switch (engCmdId)
    {
        case ENGCMD_PFC:
            assert(sendCmd);
            args[0] |= (sendCmd[0] << 8);  //sheet
            args[0] |= (sendCmd[1] << 16); //tray
            break;
        case ENGCMD_RESMISFED:
            break;
        case ENGCMD_RSELMAP:
            assert(sendCmd);
            args[0] |= (sendCmd[0] << 8); //resolution
            break;
        case ENGCMD_SETUSIZE:
            assert(sendCmd);
            args[0] |= (sendCmd[0] << 8);  //sizeID
            args[0] |= (sendCmd[1] << 16); //tray

            //imageWidth
            args[1] |= sendCmd[2];
            args[1] |= (sendCmd[3] << 8);
            //imageLength
            args[1] |= (sendCmd[4] << 16);
            args[1] |= (sendCmd[5] << 24);

            M3CommandLen = 2;

            break;
        case ENGCMD_SETPSIZE:
            assert(sendCmd);
            args[0] |= (sendCmd[0] << 8);  //tray
            args[0] |= (sendCmd[1] << 16); //sizeID
            break;
        case ENGCMD_SELMED:
            assert(sendCmd);
            args[0] |= (sendCmd[0] << 8); //mediaID
            break;
        case ENGCMD_JAMCANCELSETTING:
            break;
        case ENGCMD_GetCurrentTonerPageCounter:
        case ENGCMD_GetCurrentTonerDotCounter:
        case ENGCMD_GetCurrentOPCPageCounter:
        case ENGCMD_GetCurrentOPCDotCounter:
        case ENGCMD_GetCurrentTonerLifeCnt:
        case ENGCMD_GetCurrentDrumLifeCnt:
            break;
        case ENGCMD_WarmingUpCMD2Engine:
            assert(sendCmd);
            args[0] |= (sendCmd[0] << 8);  //size
            args[0] |= (sendCmd[1] << 16); //type
            break;
        case ENGCMD_SLEEP_STATE:
            assert(sendCmd);
            args[0] |= (sendCmd[0] << 8);
            break;
        case ENGCMD_ENGINEMARGINACQUIRE:
            break;
        case ENGCMD_ENGINE_RESET:
            break;
        case ENGCMD_OPC_Clean:
            break;
        case ENGCMD_TONERSTATUS_RESET:
        case ENGCMD_OPC_RESET:
            break;
        case ENGCMD_GetLeadingEdgeRegForUser:
        case ENGCMD_GetSide2SideReg:
        case ENGCMD_GetSide2SideRegForUser:
        case ENGCMD_GetImageDensity:
        case ENGCMD_GetLowHumidityMode:
        case ENGCMD_GetPlateControlMode:
        case ENGCMD_GetPrimaryCoolingMode:
        case ENGCMD_GetLeadingEdgeRegPlain:
        case ENGCMD_GetLeadingEdgeRegThick:
        case ENGCMD_GetLeadingEdgeRegThin:
        case ENGCMD_GetFusingTemperPlain:
        case ENGCMD_GetFusingTemperThick:
        case ENGCMD_GetFusingTemperThin:
        case ENGCMD_GetFusingTemperRecycled:
        case ENGCMD_GetSC559Detection:
        case ENGCMD_GetSubScanMag:
        case ENGCMD_GetTransferRollerBias:
        case ENGCMD_GetDestinationCode:
        case ENGCMD_GetACPowerUnstableTimes:
        case ENGCMD_GetTotalCounter:
        case ENGCMD_GetInput_FrontCover:
        case ENGCMD_GetInput_MainMotorLock:
        case ENGCMD_GetInput_PolygonMotorLock:
        case ENGCMD_GetInput_FanLock:
        case ENGCMD_GetInput_LDXDETPCHECK:
        case ENGCMD_GetInput_LDError:
        case ENGCMD_GetInput_HVPError:
        case ENGCMD_GetInput_FuserHighTemp:
        case ENGCMD_GetInput_RegistSens:
        case ENGCMD_GetInput_ExitSens:
        case ENGCMD_GetInput_FuserThermistor:
        case ENGCMD_GetInput_VideoThermistor:
        case ENGCMD_GetInput_AIO_ID_Chip:
        case ENGCMD_GetInput_ACLowVoltage:
        case ENGCMD_GetInput_PlateSensor:
            break;
        case ENGCMD_SetLeadingEdgeReg:
        case ENGCMD_SetSide2SideReg:
        case ENGCMD_SetSide2SideRegForUser:
        case ENGCMD_SetImageDensity:
        case ENGCMD_SetLowHumidityMode:
        case ENGCMD_SetPlateControlMode:
        case ENGCMD_SetPrimaryCoolingMode:
        case ENGCMD_SetLeadingEdgeRegPlain:
        case ENGCMD_SetLeadingEdgeRegThick:
        case ENGCMD_SetLeadingEdgeRegThin:
        case ENGCMD_SetFusingTemperPlain:
        case ENGCMD_SetFusingTemperThick:
        case ENGCMD_SetFusingTemperThin:
        case ENGCMD_SetFusingTemperRecycled:
        case ENGCMD_SetSC559Detection:
        case ENGCMD_SetSubScanMag:
        case ENGCMD_SetTransferRollerBias:
        case ENGCMD_SetDestinationCode:
        case ENGCMD_SetOutput_MainMotor:
        case ENGCMD_SetOutput_FeedCluth:
        case ENGCMD_SetOutput_PlateClutch:
        case ENGCMD_SetOutput_FanHighSpeed:
        case ENGCMD_SetOutput_FanLowSpeed:
        case ENGCMD_SetOutput_LDHeateOn:
        case ENGCMD_SetOutput_FuserHeater:
        case ENGCMD_SetOutput_ChargeBias:
        case ENGCMD_SetOutput_DevelopBias:
        case ENGCMD_SetOutput_TransCurrent:
        case ENGCMD_SetOutput_PolygonMotor:
        case ENGCMD_SetOutput_LD1HeateOn:
        case ENGCMD_SetOutput_LD2HeateOn:
        case ENGCMD_RESETUSB:
            assert(sendCmd);
            args[0] |= (sendCmd[0] << 8);
            break;
        case ENGCMD_FusingSCReset:
            break;
        case ENGCMD_INSLEEP_MODE:
        case ENGCMD_RESTORE_PLL:
        case ENGCMD_ENTER_SLEEP:
        case ENGCMD_EXIT_SLEEP:
            break;
        default:
            PRTDRV_PRINTF("ttt: unknown engCmdId=%d\n", engCmdId);
            break;
    }

    if (engCmdId == ENGCMD_PFC)
    {
        PRTDRV_PRINTF("ttt: will send EC3 command engCmdId=%d\n", engCmdId);
       
    }
    else
    {
        PRTDRV_PRINTF("---- will sendcommand engCmdId=%d\n", engCmdId);
    }
	
    rv = M3doCommand(PRINT_ENG_COMMNAD, M3CommandLen, &response, &nretargs, args);

    if (result) *result = response;

    return rv;
}

unsigned long par_GetTonerInfo(int option)
{
    Uint32 FW_counter = 0;

    switch (option)
    {
        case 1: // 当前碳粉盒打印的纸张数
            FW_counter = par_GetCurrentTonerPageCounter(tTRUE);
            break;
        case 2: // 当前碳粉盒打印使用的碳粉量
            FW_counter = par_GetCurrentTonerDotCounter(tFALSE);
            break;
        case 3: // 当前打印机已安装的碳粉盒数量
            FW_counter = par_GetRealTonerCntFromFlash();
            break;
        case 4: // 当前墨粉盒的使用量（基于“ISO/IEC 19752”标准估算）
            FW_counter = par_GetCurrentTonerDotCounter(tFALSE);
            FW_counter /= 1740;
            break;
        case 5: //前打印机已安装的鼓盒数量(OPC更换次数)
            FW_counter = par_GetRealDrumCntFromFlash();
            break;
        case 6: //当前OPC打印使用的碳粉量
            FW_counter = par_GetCurrentOPCDotCounter(tFALSE);
            break;
        case 7: //当前OPC的使用量（基于“ISO/IEC 19752”标准估算）
            FW_counter = par_GetCurrentOPCDotCounter(tFALSE);
            FW_counter /= 1740;
            break;
        case 8: // 当前OPC打印的纸张数
            FW_counter = par_GetCurrentOPCPageCounter(tTRUE);
            break;
        case 9: // 当前OPC打印的纸张数
            FW_counter = par_GetCurrentOPCPageCounter(tFALSE);
            break;
        default:
            FW_counter = 0xFFFFFFFF;
            break;
    }

    return FW_counter;
}

Uint8 par_WasteTonerBoxFull(void)
{
    Uint32 result      = 0;
    Uint8  tonerstatus = 0;

    prtdrv_M3doCommand(ENGCMD_WasteTonerBoxFull, NULL, &result);
    tonerstatus = result;

    return tonerstatus;
}

Uint32 tonerPageCnt = 0;
Uint32 par_GetCurrentTonerPageCounter(tBool needUpdate)
{
    Uint8 data = 0;

    StatusServ_GetEngStatus(STATUSSERV_EngKTonerInf, &data);
    if (((data & STATUSSERV_ENG_TONER_MISS) != 0) || ((data & STATUSSERV_ENG_TONER_CRUM_ID_WARNING) != 0)) return 0;

    if (needUpdate)
    {
        stDrvMsgCommRespBuf commRespMSG = {0};
        prtdrv_M3doCommand(ENGCMD_GetCurrentTonerPageCounter, NULL, NULL);
        msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_TonerPageCnt, 0);

        tonerPageCnt = commRespMSG.mBuf[1];

    }

    return tonerPageCnt;
}

Uint32 tonerDotCnt = 0;
Uint32 par_GetCurrentTonerDotCounter(tBool needUpdate)
{
    Uint8 data = 0;

    StatusServ_GetEngStatus(STATUSSERV_EngKTonerInf, &data);
    if (((data & STATUSSERV_ENG_TONER_MISS) != 0) || ((data & STATUSSERV_ENG_TONER_CRUM_ID_WARNING) != 0)) return 0;

    if (needUpdate)
    {

        stDrvMsgCommRespBuf commRespMSG = {0};

        prtdrv_M3doCommand(ENGCMD_GetCurrentTonerDotCounter, NULL, NULL);
        msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_TonerDotCnt, 0);

        tonerDotCnt = commRespMSG.mBuf[1];
    }

    return tonerDotCnt;
}

Uint32 OPCPageCnt = 0;
Uint32 par_GetCurrentOPCPageCounter(tBool needUpdate)
{
    if (needUpdate)
    {

        stDrvMsgCommRespBuf commRespMSG = {0};
        prtdrv_M3doCommand(ENGCMD_GetCurrentOPCPageCounter, NULL, NULL);
        msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_OPCPageCnt, 0);

        OPCPageCnt = commRespMSG.mBuf[1];

    }

    return OPCPageCnt;
}

Uint32 OPCDotCnt = 0;
Uint32 par_GetCurrentOPCDotCounter(tBool needUpdate)
{
    if (needUpdate)
    {

        stDrvMsgCommRespBuf commRespMSG = {0};
        prtdrv_M3doCommand(ENGCMD_GetCurrentOPCDotCounter, NULL, NULL);
        msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_OPCDotCnt, 0);

        OPCDotCnt = commRespMSG.mBuf[1];

    }

    return OPCDotCnt;
}

Uint32 par_GetCurrentTonerLifeCnt(tBool needUpdate)
{
    Uint32 EngKTonerLifeCnt = 0;
    if (needUpdate)
    {

        stDrvMsgCommRespBuf commRespMSG = {0};
        prtdrv_M3doCommand(ENGCMD_GetCurrentTonerLifeCnt, NULL, NULL);
        msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_TonerLifeCnt, 0);

        EngKTonerLifeCnt = commRespMSG.mBuf[1];

        //PRTDRV_PRINTF("Get TonerLifeCnt = %d\n", EngKTonerLifeCnt);
        StatusServ_SetEngStatus(STATUSSERV_EngKTonerLifeCnt, &EngKTonerLifeCnt);

    }
    else
    {
        StatusServ_GetEngStatus(STATUSSERV_EngKTonerLifeCnt, &EngKTonerLifeCnt);
    }

    return EngKTonerLifeCnt;
}

Uint32 par_GetCurrentDrumLifeCnt(tBool needUpdate)
{
    Uint32 EngDrumLifeCnt = 0;
    if (needUpdate)
    {
        stDrvMsgCommRespBuf commRespMSG = {0};
        prtdrv_M3doCommand(ENGCMD_GetCurrentDrumLifeCnt, NULL, NULL);
        msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_DrumLifeCnt, 0);

        EngDrumLifeCnt = commRespMSG.mBuf[1];

        PRTDRV_PRINTF("Get DrumLifeCnt = %d\n", EngDrumLifeCnt);
        StatusServ_SetEngStatus(STATUSSERV_EngDrumLifeCnt, &EngDrumLifeCnt);

    }
    else
    {
        StatusServ_GetEngStatus(STATUSSERV_EngDrumLifeCnt, &EngDrumLifeCnt);
    }

    return EngDrumLifeCnt;
}

void EngineGetSerialNo_Random(Uint8 *serialno)
{
    char  serialNumber[21];
    char *pConfigOEMID = CONFIG_OEM_ID;

    memset(serialNumber, 0, sizeof(serialNumber));

    sprintf(serialNumber, "%s_%s", (char *)CONFIG_PRODUCT_SHORT_NAME, (char *)(pConfigOEMID + 4));
    memcpy(serialno, serialNumber, sizeof(serialNumber));
}

tStatus Engine_serialNO_get(Uint8 serialno[])
{
    Uint8 tmp_serial[32] = {0};

    if (PrintManager.virtualMode /* || !PrintManager.initOK*/)
    {
        memcpy(serialno, "virtual M", 10);
        return tERROR;
    }

    if (FlashSrv_ReadSysParam_MachineSN((Uint8 *)&tmp_serial[0]) == tOK)
    {
        if ((tmp_serial[0] == 0xFF) || (tmp_serial[0] == 0) || (tmp_serial[0] == 0x20))
        {
            EngineGetSerialNo_Random(serialno);
        }
        else
        {
            memcpy(serialno, tmp_serial, 20);
        }
    }
    else
    {
        PRTDRV_PRINTF("tRead Serial Number Error!\n");
    }

    serialno[21] = '\0';

    memcpy(serialnoArray, serialno, 21);
    return tOK;
}

Uint8 par_GetWasteTonerFullStatus(tBool needUpdate)
{
    unsigned char sendCmd = needUpdate;
    Uint32        result  = 0;

    prtdrv_M3doCommand(ENGCMD_GetWasteTonerFullStatus, &sendCmd, &result);

    return result;
}

int PRTDRV2_Send_RESMISFED(void)
{
    return prtdrv_M3doCommand(ENGCMD_RESMISFED, NULL, NULL);
}

int PRTDRV2_Send_SELMAP(unsigned char resolution)
{
    return prtdrv_M3doCommand(ENGCMD_RSELMAP, &resolution, NULL);
}

tStatus Engine_Reset(void)
{
    unsigned char sendCmd[2] = {0};
    // "END" store in block ID 1, offset 377
    Uint32        end_offset  = 377;
    unsigned char end_data[3] = {0x00, 0x00, 0x00};

    ifc2e_WriteParam(C2E_PARAM_COMMON, end_offset, end_data, 3);
    PRTDRV_PRINTF("[eng]reset END offset=%d,size=%d\n", end_offset, sizeof(end_data));

    // read "END" again
    memset(end_data, 0xFE, 3);
    ifc2e_ReadParam(C2E_PARAM_COMMON, end_offset, end_data, 3);
    PRTDRV_PRINTF("[eng]read reset END 0x%x 0x%x 0x%x\n", end_data[0], end_data[1], end_data[2]);

    prtdrv_M3doCommand(ENGCMD_ENGINE_RESET, sendCmd, NULL);

    return tOK;
}

#if 1 //CFG_Authorization
tStatus OPC_Clean(void)
{
    prtdrv_M3doCommand(ENGCMD_OPC_Clean, NULL, NULL);
    return tOK;
}
#endif

int PRTDRV2_Send_PFC(char sheet, char tray)
{
    unsigned char sendCmd[2];
    sendCmd[0] = sheet;
    sendCmd[1] = tray;

    int ret = prtdrv_M3doCommand(ENGCMD_PFC, sendCmd, NULL);

    return ret;
}

int PRTDRV2_Send_SETUSIZE(char sizeID, char tray, tUint16 imageWidth, tUint16 imageLength)
{
    unsigned char sendCmd[7];
    int           ret = 0;

    sendCmd[0] = sizeID;
    sendCmd[1] = tray;
    sendCmd[2] = imageWidth & 0xFF;
    sendCmd[3] = imageWidth >> 8;
    sendCmd[4] = imageLength & 0xFF;
    sendCmd[5] = imageLength >> 8;

    ret = prtdrv_M3doCommand(ENGCMD_SETUSIZE, sendCmd, NULL);

    return ret;
}

int PRTDRV2_Send_SETPSIZE(char tray, char sizeID)
{
    unsigned char sendCmd[2];
    int           ret = 0;

    sendCmd[0] = tray;
    sendCmd[1] = sizeID;

    ret = prtdrv_M3doCommand(ENGCMD_SETPSIZE, sendCmd, NULL);

    return ret;
}

int PRTDRV2_Send_SELMED(char mediaID)
{
    unsigned char sendCmd = 0;
    int           ret     = 0;

    sendCmd = mediaID;
    ret     = prtdrv_M3doCommand(ENGCMD_SELMED, &sendCmd, NULL);

    return ret;
}

tBool ParSendWarmingUpCMD2Engine(tUint8 size, tUint8 type)
{
    unsigned char sendCmd[2];
    int           ret = 0;

    sendCmd[0] = size;
    sendCmd[1] = type;

    ret = prtdrv_M3doCommand(ENGCMD_WarmingUpCMD2Engine, sendCmd, NULL);

    return ret;
}

#define _TGENPO32      (1L) //(1L <<  0)
#define _TGENPO34      (1L << 2)
#define SPU3_DEEPSLEEP _TGENPO32 || _TGENPO34
extern void     SetTGIOStatus(Uint32 reg, Uint32 bits);
extern void     ClrTGIOStatus(Uint32 reg, Uint32 bits);
void            SwitchVideoPll(int mode);
eJobMgr_JobType PrtDrv_GetRunningJobType(void);

extern tUint16 StatusServ_ClrEngStatusBit(eStatusServ_EngId statusId, tUint16 value);
extern void    StartIDCardCopyMode(void);
extern void    StartDuplexCopyMode(void);
extern tBool   ParSendWarmingUpCMD2Engine(tUint8 size, tUint8 type);
extern int     sprintf(char *buffer, const char *format, ...);
extern tBool   par_CancellationMisprint(void);

//extern void LPH_POWER_OFF(void);
extern Uint32 TaskGetSysFrequency(void);

extern int FlashSrv_ReadTonerInfomation(Uint8 *buf);
extern int FlashSrv_ReadDrumInfomation(Uint8 *buf);
extern int FlashSrv_WriteTonerInfomation(Uint8 *buf);
extern int FlashSrv_WriteDrumInfomation(Uint8 *buf);

extern void RptMgr_SaveErrorInHistory(tUint8 en);
// =============================================================================
// =============================================================================
//refer from icu.c
/**
 * @fn      unsigned long ICU_InterruptRead(InterruptVector vector)
 * @brief   This function is used to read ICU register.
 * @param   InterruptVector vector
 * @return  verctor's value.
 */
#define EXBASE1   EXMSK1A
#define EXSPREAD1 (EXMSK1B - EXMSK1A)
/// Translate a vector number in the range 0-95 into an index,
/// { [0..31] => 0, [32..63] => 1, [64..95] => 2 }.
#define VECTOR_REG_INDEX(vec) ((vec) >> 5)
/// 32-bit interrupt mask for a given interrupt vector.
#define VECTOR_INT_BIT(vec) (1UL << ((vec)&0x1F))
#define IRQ_MECR_REG(v)     (MECRA + VECTOR_REG_INDEX(v) * (MECRA - MECRB)) //to read Main Exception Cause Register.
#define IRQ_MASK_REG(v)     (EXBASE1 + VECTOR_REG_INDEX(v) * EXSPREAD1)
#define CLEAR_REG(v)        (EXCLRA + VECTOR_REG_INDEX(v) * (EXCLRB - EXCLRA))

#if 0
int lastDotCnt = 0, dotCnt2Time = 0;
HHWTIMER fgateTimer = (-1);

// BSP@2018
// as we cannot use HW FGATE, so we use HW timer callback instead.
// Noted, the SYT callback I wrote in SZ is not good for formal product
// because it can be impacted seriously if CPU is busy
static void _PrtDrv_FgateTimerCB(int irq)
{
    int    rv;
    Uint32 args[30] = {0};
    int    nretargs;
    Uint32 response;
PRTDRV_PRINTF("ttt: _PrtDrv_FgateTimerCB\n");

    rv = M3doCommand(
      PRINT_START_VIDEO,
      1,
      &response,
      &nretargs,
      args
      );

    BIOSintClear(irq);
    BIOStimerClear(fgateTimer);
}

void _PrtDrv_FgateTimerStart(void)
{
    //Uint32 us = BIOSgetSYSfrequency() / 1000000;
    Uint32 us = TaskGetSysFrequency() / 1000000;

    if (lastDotCnt != dotCnt2Time)
    {
        lastDotCnt = dotCnt2Time;
		BIOStimerSet(fgateTimer, dotCnt2Time * us, 1, _PrtDrv_FgateTimerCB);
    }
    else
    {
    	BIOStimerSet(fgateTimer, dotCnt2Time * us, 1, _PrtDrv_FgateTimerCB);
    }
}

unsigned long ICU_InterruptRead(InterruptVector vector)
{
    Uint32 ints = BIOSintsOff(); // Guard read/modify/write operation
    Uint32 mask = IO_READ32(IRQ_MECR_REG(vector));

    mask &= VECTOR_INT_BIT(vector);

    BIOSintsRestore(ints);

    return mask;
}
#endif
//=========[ Subfunctions for print driver]============================================

/*
   @note this function will change the RTC formate date to FX's date formate
   --   bit31....bit0[RTC formate]    -->  bit15...bit0 [FX formate]
   <31:9>YYYY <8:5>MMMM <4:0>DDDD      <15:9>YYYY <8:5>MMMM <4:0>DDDD

*/
tUint16 PRTDRV_ReturnRtcData(tVoid)
{
    tUint32 rtc_date = 0, tmp32 = 0;
    tUint16 ret_date = 0, tmp16 = 0;

    rtc_date = Rtc_GetDate();

    if (rtc_date == 0xffffFFFF) return 0xffff;

    //get MMMM DDDDD
    //get YYYY - 2000 (Because FX's formate start from 2000)
    ret_date = (tUint16)rtc_date & 0x000001ff; //set MM DD
    tmp32    = ((rtc_date >> 9) & 0x007fFFFF);

    if (tmp32 < 2000)
        tmp32 = 0;
    else
        tmp32 -= 2000;

    tmp16 = (tUint16)tmp32;
    ret_date |= (tmp16 << 9) & 0xfe00; //set YY
    return ret_date;
}

void PRTDRV_SendPageScriptToPushBand(PM_ScriptDscp *pPageScript)
{
    stPageBandTaskMsg taskMsg;

    taskMsg.mtype                   = 1;
    taskMsg.pageBandMsg.msgId       = PAGEBAND_NEWPAGESCRIPT;
    taskMsg.pageBandMsg.pPageScript = pPageScript;

    msgsnd(msgQPushPageBand, &taskMsg, sizeof(stPageBandMsg), 0);
}

void PRTDRV_SendFlushPageDone(int pBand)
{
    stFlushPageTaskMsg taskMsg;

    taskMsg.mtype              = 1;
    taskMsg.flushPageMsg.msgId = FLUSHPAGE_PAGEDONE;
    //taskMsg.flushPageMsg.pBand = pBand;

    msgsnd(msgQFlushPage, &taskMsg, sizeof(stFlushPageMsg), 0);
}

PM_PageScriptOrderList *PRTDRV_AddPageScriptOrderList(PM_ScriptDscp *pPageScript)
{
    tUint32                 i                    = 0;
    PM_PageScriptOrderList *pPageScriptOrderList = PrintManager.pPageScriptOrderList_Header, *pPageScriptOrderList_Tail = NULL;

#if 0

    while (pPageScriptOrderList->pPageScript != NULL)
        pPageScriptOrderList = pPageScriptOrderList->pPageScriptOrderList_Next;

#endif

    if (PrintManager.pPageScriptOrderList_Header == tNULL)
    {
        pPageScriptOrderList = PrintManager.pPageScriptOrderList_Header = &PrintManager.pageScriptOrderList[0];
        pPageScriptOrderList->pPageScriptOrderList_Prev                 = NULL;
        pPageScriptOrderList->pPageScriptOrderList_Next                 = NULL;
    }
    else
    {
        pPageScriptOrderList = PrintManager.pPageScriptOrderList_Header;

        while (pPageScriptOrderList->pPageScriptOrderList_Next != tNULL)
        {
            pPageScriptOrderList = pPageScriptOrderList->pPageScriptOrderList_Next;
        }

        pPageScriptOrderList_Tail = pPageScriptOrderList;

        for (i = 0; i < MAX_PAGE_NUM; i++)
        {
            if (PrintManager.pageScriptOrderList[i].pPageScript == tNULL)
            {
                pPageScriptOrderList = &PrintManager.pageScriptOrderList[i];
                break;
            }
        }

        COMMON_Assert(i < MAX_PAGE_NUM);

        pPageScriptOrderList_Tail->pPageScriptOrderList_Next = pPageScriptOrderList;
        pPageScriptOrderList->pPageScriptOrderList_Prev      = pPageScriptOrderList_Tail;
        pPageScriptOrderList->pPageScriptOrderList_Next      = NULL;
    }

    pPageScript->subId++;
    pPageScriptOrderList->pPageScript = pPageScript;
    pPageScriptOrderList->subId       = pPageScript->subId;

    return pPageScriptOrderList;
}

tPrivate PM_PageScriptOrderList *PRTDRV_GetNextPageScriptInOrderList(PrtMgr_Cmd toDo, tUint8 dmaCh)
{
    PM_PageScriptOrderList *pPageScriptOrderList = PrintManager.pPageScriptOrderList_Header;

    switch (toDo)
    {
        case PAGE_CFGDMA:

            while (pPageScriptOrderList && pPageScriptOrderList->cfgDMA[dmaCh])
                pPageScriptOrderList = pPageScriptOrderList->pPageScriptOrderList_Next;

            break;
        case PAGE_PLANEDONE:

            while (pPageScriptOrderList && pPageScriptOrderList->planeDone[colorMapping_ChannelToPrtMgr[dmaCh]])
                pPageScriptOrderList = pPageScriptOrderList->pPageScriptOrderList_Next;

            break;
        case PAGE_FEED:

            while (pPageScriptOrderList && pPageScriptOrderList->fed) pPageScriptOrderList = pPageScriptOrderList->pPageScriptOrderList_Next;

            break;
        case PAGE_PPOUT:

            while (pPageScriptOrderList && (pPageScriptOrderList->paperOut || pPageScriptOrderList->pPageScript->sizeErrorPaperOut))
                pPageScriptOrderList = pPageScriptOrderList->pPageScriptOrderList_Next;

            break;
    }

    return pPageScriptOrderList;
}

tPrivate tVoid PRTDRV_RemovePageScriptFromOrderList(PM_ScriptDscp *pPageScript)
{
    tUint32                 i                    = 0;
    PM_PageScriptOrderList *pPageScriptOrderList = PrintManager.pPageScriptOrderList_Header;

    if (pPageScript->dispatchedCnt == 0)
    {
#if 1 //log for debug
        stJobMgr_JobDscp *pJob = JOBMGR_GetPageParent(pPageScript->pPage);
        PRTDRV_PRINTF("PPipe[PRTDRV_RemovePageScriptFromOrderList-Error]:(J%d-p%d-%d) 0x%x\n", pJob->JobId, JOBMGR_GetPageId(pPageScript->pPage),
                      pPageScriptOrderList->subId, pPageScript);
#endif
        return;
    }

    assert(pPageScriptOrderList);

    assert(PrintManager.pPageScriptOrderList_Header->pPageScript == pPageScript); //The removed one should be the header
#if 0                                                                             //log for debug
    {
        stJobMgr_JobDscp   *pJob = JOBMGR_GetPageParent(pPageScript->pPage);
        PRTDRV_PRINTF("PPipe: PRTDRV_RemovePageScriptFromOrderList(J%d-p%d-%d) 0x%x\n", pJob->JobId, JOBMGR_GetPageId(pPageScript->pPage), pPageScriptOrderList->subId, pPageScript);
    }
#endif
    PrintManager.pPageScriptOrderList_Header->pPageScript = NULL;
    PrintManager.pPageScriptOrderList_Header->fed         = tFALSE;
    PrintManager.pPageScriptOrderList_Header->paperOut    = tFALSE;
    PrintManager.pPageScriptOrderList_Header->subId       = 0;

    for (i = 0; i < JOBMGR_MAXCOLORID; i++)
    {
        PrintManager.pPageScriptOrderList_Header->cfgDMA[i]    = tFALSE;
        PrintManager.pPageScriptOrderList_Header->planeDone[i] = tFALSE;
    }

    PrintManager.pPageScriptOrderList_Header        = PrintManager.pPageScriptOrderList_Header->pPageScriptOrderList_Next;
    pPageScriptOrderList->pPageScriptOrderList_Prev = NULL;
    pPageScriptOrderList->pPageScriptOrderList_Next = NULL;

    if (PrintManager.pPageScriptOrderList_Header) PrintManager.pPageScriptOrderList_Header->pPageScriptOrderList_Prev = NULL;
}

#if 0
/**
 * @fn       static tInt32 PRTDRV_ConfigDMA(BANDINFO* band,tUint8 isFirstBand)
 * @brief    Each DMA band setup needs this function which will give DMA band address and band data size.
 * @param    BANDINFO* -band
 * @param    tBool   isFirstBand : [1] means this is the first band.
 * @param    tUint8  dmaCh : To select a DMA channel to set-up.
 * @return   int -> PRTDRV_OK
 * @note     [Internal function]
 */

tInt32 PRTDRV_ConfigDMA(BANDINFO* pBand, tBool isFirstBand, tUint8 dmaCh)
{
    int    rv;
    Uint32 args[30];
    int    nretargs;
    Uint32 response;
    Uint32 *bandAddress = NULL, *pPBandStart = NULL;
    Uint8  endian = 0;

#if PURE_VIRTUAL_MODE
    if (PrintManager.virtualMode)
        return PRTDRV_OK;
#endif 

    tUint8 *pVBandBuf = (tUint8 *)MemServ_GetWorkingBufPtr(MemServ_PrintBuf);  
	  tUint8 *pPBandBuf = (tUint8 *)MemServ_GetPWorkingBufPtr(MemServ_PrintBuf);   
    
    if ((JOBMGR_GetPageCodec(pBand->pPageScript->pPage) == JOBMGR_RAW && PrintManager.rawModeRAW) ||
        (!(JOBMGR_IsSecurePrintJob(pBand->pPageScript->pPage->pParent)) && JOBMGR_GetPageCodec(pBand->pPageScript->pPage) == JOBMGR_PDL_DISPLAYLIST && PrintManager.rawModePDL))
        bandAddress = (Uint32 *)*pBand->bandStart;
    else
        bandAddress = pBand->bandStart;

#if 0
    memset((tUint8 *)bandAddress, 0xAA, pBand->dataSizeBytes);
#endif

    if (((tUint8 *)bandAddress >= pVBandBuf) && ((tUint8 *)bandAddress < pVBandBuf + MemServ_GetWorkingBufSize(MemServ_PrintBuf)))
    {
    	pPBandStart = (tUint32 *)(pPBandBuf + ((tUint8 *)bandAddress - pVBandBuf));
    }
    else
    {
    	assert(0);
    }

    endian = (JOBMGR_GetPageCodec(pBand->pPageScript->pPage) == JOBMGR_PDL_DISPLAYLIST) ? 0x1F : 0x07;

    args[0] = (endian << 24) | (isFirstBand << 16) | (pBand->isLastBand << 8) | dmaCh;
    args[1] = (Uint32)pPBandStart;
    args[2] = pBand->dataSizeBytes / pBand->dataLines; //bytes per line
    args[2] |= pBand->dataLines << 16;

#if 0
    Log(2, "PRINT_NEW_BAND\n");
#endif
    rv = M3doCommand(
        PRINT_NEW_BAND,
        3,
        &response,
        &nretargs,
        args
        );

    if (! rv)
    {
        if (args[0] != PRINT_RSP_OK)
        {
            Error("New Band, Failed, ret=%d\n", args[0]);
            rv = -1;
        }
    }

#if 0
    Uint32 tmpreg = 0x80; //Laser Enable
    Uint32 repeat = 0;
#endif

#if 0
    //Check DMA status
    while ((IO_READ32(IDASTAT0 + PRT_IDBASE_PRE_SIZE * dmaCh) & 0x07) >= 6); //TS field is not 6 or 7.

    //LPRI ENSTA register set-up
    if (isFirstBand)
        tmpreg += 0x40; //First Band Enable

    IO_WRITE32(LPRI0LPENSTA + PRT_LPRI_PRE_SIZE * dmaCh, (tmpreg << 24)); //Laser Enable(bit31), First Band Enable(bit30)
    //IO_WRITE32( LPRI0LPVERS+PRT_LPRI_PRE_SIZE*dmaCh,(Uint32) pBand->dataLines);
    assert(pBand->dataSizeBytes % 4 == 0); //This is a must

    IO_WRITE32(IDCNT0 + PRT_IDBASE_PRE_SIZE * dmaCh, pBand->dataSizeBytes / 4); //count unit by word

    if ((JOBMGR_GetPageCodec(pBand->pPageScript->pPage) == JOBMGR_RAW && PrintManager.rawModeRAW) ||
        (!(JOBMGR_IsSecurePrintJob(pBand->pPageScript->pPage->pParent)) && JOBMGR_GetPageCodec(pBand->pPageScript->pPage) == JOBMGR_PDL_DISPLAYLIST && PrintManager.rawModePDL))
        IO_WRITE32(IDBASE0 + PRT_IDBASE_PRE_SIZE * dmaCh, (Uint32)*pBand->bandStart);
    else
        IO_WRITE32(IDBASE0 + PRT_IDBASE_PRE_SIZE * dmaCh, (Uint32)pBand->bandStart);

    IO_WRITE32(LDRPT0 + PRT_IDBASE_PRE_SIZE * dmaCh, repeat & 0x00FFFFFF);

    IO_WRITE32(IDSTART0 + PRT_IDBASE_PRE_SIZE * dmaCh, (Uint32)1);
#endif
    return PRTDRV_OK;
}
#endif

#if (PROJ_3IN1_ADF || PROJ_3IN1) //Nin1 Margin zhoawei 2020.8.24
extern tBool ScanApp_CheckNin1Active(tVoid);
#endif

#if  0 //NXDEMO
tInt32 PRTDRV_PageConfig_Dma_If(stJobMgr_PageDscp *pPage)
{
    int     rv;
    Uint32  args[30] = {0};
    int     nretargs;
    Uint32  response;
    tUint32 width, height;
    if (pPage)
    {

        width  = JOBMGR_GetPageImgWidth(pPage);
        height = JOBMGR_GetPageImgLength(pPage);
        if ((JOBMGR_JBIG == JOBMGR_GetPageCodec(pPage)) && (height % CodecServ_GenernalStripeHeight))
        {
            height = (height / CodecServ_GenernalStripeHeight + 1) * CodecServ_GenernalStripeHeight;
        }
        PRTDRV_PRINTF("----PRINT_PAGECONFIG_DMA: send w=%d h=%d lineperband=%d----\n", width, height, CodecServ_GenernalStripeHeight);
        args[0] = 0;
        args[1] = 0;
        args[2] = 0;
        args[2] |= width;
        args[2] |= height << 16;

        args[3] = CodecServ_GenernalStripeHeight;
        rv      = M3doCommand(PRINT_PAGECONFIG_DMA, 4, &response, &nretargs, args);
    }
    else
    {
        PRTDRV_PRINTF("PRINT_PAGECONFIG_DMA: error no page info\n");
    }
    return PRTDRV_OK;
}
tInt32 PRTDRV_Page_If(stJobMgr_PageDscp *pPage)
{
	int     rv;
    Uint32  args[30] = {0};
    int     nretargs;
    Uint32  response;
    if (pPage)
    {
        PRTDRV_PRINTF("PRTDRV_Page_If: set tray!\n");
        args[0] = 0;
        args[1] = 0;
        rv      = M3doCommand(PRINT_SET_PAGEINFO, 2, &response, &nretargs, args);
    }
    else
    {
        PRTDRV_PRINTF("PRTDRV_Page_If: error no page info\n");
    }
    return PRTDRV_OK;

}
void PRTDRV_Job_Start()
{
	int    rv;
    Uint32 args[30];
    int    nretargs;
    Uint32 response;
#if NXDEMO
		PRTDRV_PRINTF("PRTDRV_Job_Start: send PRINT_START_JOB\n");
        rv = M3doCommand(PRINT_START_JOB, 1, &response, &nretargs, args);
		
#else
        rv = M3doCommand(PRINT_START_JOB, 1, &response, &nretargs, args);
#endif

}
#endif
tInt32 PRTDRV_PageConfig_DmaIf(tUint32 width, tUint32 height, tInt32 top_margin, tInt32 left_margin, eJobMgr_PaperSize paperSize, tUint8 dmaCh,
                               tUint8 videoOutputMode, stJobMgr_PageDscp *pPage)
{
    int    rv;
    Uint32 args[30] = {0};
    int    nretargs;
    Uint32 response;

    //tInt32 top_margin_to_eng = 96;
    tInt32 top_margin_to_eng = 0;

    //tInt32 top_margin_to_eng = 55;
    tInt32            left_margin_to_eng = 100 /*, lm = 16, tm = 0*/;
    eJobMgr_PaperSize size               = JOBMGR_A4L;
    tInt32            paperWidth = 0, dotDiff = 0;
    tInt32            marginDots      = 265;
    tInt32            LeftMargin_init = 100 /*100 means 4.2mm*/ /*, TopMargin_init = 40*/;
#if CFG_ippManager
    stJobMgr_JobDscp *pJob = tNULL;
#endif

#if YUANKA_INFERNO
    tUint8 isYuankaPDF = 0;
#endif

#if 0 //GM266DNS
   marginDots = 872;
#else
    if ((CONFIG_USB_PID == 0x0027) /*G336DN*/ || (CONFIG_USB_PID == 0x002E) /*G3300DN*/ || (CONFIG_USB_PID == 0x0029) /*GM337DN*/
        || (CONFIG_USB_PID == 0x002F) /*GM8300DN*/)
    {
        marginDots = 212;
    }
    else if ((CONFIG_USB_PID == 0x001E) /*M7360DNA*/)
    {
        marginDots = 255;
    }
    else if ((CONFIG_USB_PID == 0x56CA) /*GM268DNAS*/)
    {
        marginDots = 250;
    }
    else
    {
        //marginDots = 212;
        //marginDots = 240;
        marginDots = 150;
    }
#endif

    if (PrtDrv_GetRunningJobType() == JOBMGR_CopyJob)
    {
        LeftMargin_init = 80;    //90
        top_margin_to_eng -= 35; //25
        if (pPage->duplex == BACK_SIDE_PAGE)
        {
            tInt32 leftmargin = JOBMGR_GetPageLeftMargin(pPage);
            LeftMargin_init   = LeftMargin_init + leftmargin - 10;
            top_margin_to_eng -= 10; //60,40
        }
    }
#if CFG_ippManager

    pJob = JOBMGR_GetPageParent(pPage);

    if (pJob && JOBMGR_GetJobSubSrcType(pJob) == JOBMGR_SubSrcIPP
        && (JOBMGR_GetPageCodec(pPage) == JOBMGR_PDL_DISPLAYLIST)) //Kim: When Airprint Job, operate EG LM value.
    {
        LeftMargin_init = 83; //4.0mm airprint job
        LeftMargin_init += JOBMGR_GetPageLeftMargin(pPage);

        LeftMargin_init = LeftMargin_init > 0 ? LeftMargin_init : 0;
    }

#endif

    if (JOBMGR_GetPageInResolution(pPage) == JOBMGR_DPI1200x1200) //fix 1200dpi left margin error issue.
        LeftMargin_init = 100 * 2;                                //4.2mm in 1200dpi

    left_margin_to_eng = left_margin + LeftMargin_init;

    if (top_margin_to_eng < 0) top_margin_to_eng = 1;

    if (paperSize == JOBMGR_FreeSize)
        paperWidth = pPage->PaperWidth;
    else
    {
        for (size = JOBMGR_A3L; size < JOBMGR_FreeSize; size++)
        {
            if (size_width_length[size].paperSize == paperSize)
            {
                paperWidth = size_width_length[size].width;
                break;
            }
        }
    }

    assert(paperWidth);
    dotDiff = abs(size_width_length[JOBMGR_A4L].width - paperWidth) * 600 / 254 / 2;

    if (paperWidth < size_width_length[JOBMGR_A4L].width)
        marginDots += dotDiff;
    else
        marginDots -= dotDiff;

    assert(marginDots > 0);

    if (JOBMGR_GetPageInResolution(pPage) == JOBMGR_DPI1200x1200) marginDots = marginDots * 2;

        //PRTDRV_PRINTF("tmp: paperWidth=%d, marginDots=%d, dotDiff=%d, Resolution=%d, LM=%d, TM=%d\n", paperWidth, marginDots, dotDiff, JOBMGR_GetPageInResolution(pPage), left_margin_to_eng, top_margin_to_eng);

#if DUAL_BEAM

    if (dmaCh == 0)
        left_margin_to_eng += (marginDots);
    else
        left_margin_to_eng += (marginDots + 2); //fix one pixel double image shadow issue.

#else

    if (dmaCh == 0)
        left_margin_to_eng += (marginDots);
    else
        left_margin_to_eng += (marginDots);

#endif

#if 0
    PRTDRV_PRINTF("DMA margin: left_margin_to_eng=%d, left_margin=%d, LeftMargin_init=%d, PageLeftMargin=%d, marginDots=%d\n", left_margin_to_eng, left_margin, LeftMargin_init, JOBMGR_GetPageLeftMargin(pPage), marginDots);
#endif

#if YUANKA_INFERNO
    if ((NULL != pPage) && (JOBMGR_PDL_DISPLAYLIST == JOBMGR_GetPageCodec(pPage)))
    {
        isYuankaPDF = 1;
    }
#endif

#if 1
    if ((JOBMGR_JBIG == JOBMGR_GetPageCodec(pPage)) && (height % CodecServ_GenernalStripeHeight))
    {
        height = (height / CodecServ_GenernalStripeHeight + 1) * CodecServ_GenernalStripeHeight;
    }
#endif
    PRTDRV_PRINTF("DMA margin: pid=%d left_margin_to_eng=%d, top_margin_to_eng=%d, width=%d, height=%d\n", JOBMGR_GetPageLeftMargin(pPage),left_margin_to_eng, top_margin_to_eng, width, height);
    args[0] = dmaCh;
    args[0] |= videoOutputMode << 8;
#if YUANKA_INFERNO
    args[0] |= isYuankaPDF << 16;
#endif

    args[1] = 0;
    args[1] |= left_margin_to_eng;
    args[1] |= top_margin_to_eng << 16;
    args[2] = 0;
    args[2] |= width;
    args[2] |= height << 16;

#if 0 //PRINTDEBUGINFO
    Log(2, "PRINT_PAGECONFIG_DMA\n");
#endif
    rv = M3doCommand(PRINT_PAGECONFIG_DMA, 3, &response, &nretargs, args);

    return PRTDRV_OK;
}

/**
 * @fn       tVoid PRTDRV_PageConfig_SettingRead(stJobMgr_PageDscp *pPage,tUint32 *pAmp, tUint32 *pLeftMargShift)
 * @brief    To get page config parameters from page descriptor.
 * @param    N/A.
 * @return   None.
 * @note     [Internal function]
 */
tVoid PRTDRV_PageConfig_SettingRead(PM_ScriptDscp *pPageScript, tUint32 *pAmp)
{
    *pAmp                        = 1;
    pPageScript->videoOutputMode = 1;
}

/**
 * @fn       tVoid PRTDRV_ResetPif_DmaIF(tUint8 dmaCh)
 * @brief    Reset the dma channel when we want to print a new page.
 * @param    tUint8 -- Indicate the dma channel to do the reset.
 * @return   None.
 */
tVoid PRTDRV_ResetPif_DmaIF(tUint8 dmaCh, tUint8 videoOutputMode)
{
#if 0
    Uint32 tmpreg = 0, tmpreg2 = 0, dmaStart = 0, dmaEnd = 0;
    tBool  resetOK = tFALSE;
    tUint8 hw_ver = 0x11;

    if (dmaCh >= JOBMGR_MAXCOLORID)
    {
        dmaStart = 0;
        dmaEnd = JOBMGR_MAXCOLORID - 1;
    }
    else
    {
        dmaStart = dmaCh;
        dmaEnd = dmaCh;
    }

    for (dmaCh = dmaStart; dmaCh <= dmaEnd; dmaCh++)
    {
        do
        {
            //Stop DMA0 here -->to force DMA stop
            IO_WRITE32(IDSTOP0 + PRT_IDBASE_PRE_SIZE * dmaCh, (Uint32)1);
            TASKSLEEP_MILLISECONDS(2);

            //step 1.  LPMODE.LSR(set all states to idle) & LPMODE.BC(clear the DMA buffer)
            if ((hw_ver == 0x00) || (hw_ver >= 0x10))
            {
                if (videoOutputMode == 1)
                    IO_WRITE32(LPRI0LPMODE + PRT_LPRI_PRE_SIZE * dmaCh, 0x000D805/*0x0000D805*/);
                else
                    IO_WRITE32(LPRI0LPMODE + PRT_LPRI_PRE_SIZE * dmaCh, 0x000D005/*0x0000D805*/);
            }
            else
            {
                if (videoOutputMode == 1) //one bit serial data mode, Vsync/Lsync are used in pulse mode.
                    IO_WRITE32(LPRI0LPMODE + PRT_LPRI_PRE_SIZE * dmaCh, 0x0000DC05);//EVT1 open clone bit for all DMA channel
                else // 2 bit serial data mode, Vsync/Lsync are used in pulse mode.
                    IO_WRITE32(LPRI0LPMODE + PRT_LPRI_PRE_SIZE * dmaCh, 0x0000D405);
            }

            //step 2. disable LPENSTA.LE and clear the errors by writting 1s to the error bits
            IO_WRITE32(LPRI0LPENSTA + PRT_LPRI_PRE_SIZE * dmaCh, (Uint32)0x000000FC);

            //step 3. clear the error in ICU register
#if INFERNO53XX
            //Inferno, offset of IRQ in 53xx is 3 not 1
            ICU_InterruptDisable(IRQ_PDD0 + dmaCh * 3);
            ICU_InterruptClear(IRQ_PDD0 + dmaCh * 3);
#else
            ICU_InterruptDisable(IRQ_PDD0 + dmaCh);
            ICU_InterruptClear(IRQ_PDD0 + dmaCh);
#endif
            TASKSLEEP_MILLISECONDS(2);
            //To check reset is successful or not.
            tmpreg = IO_READ32(LPRI0LPENSTA + PRT_LPRI_PRE_SIZE * dmaCh);
            tmpreg = tmpreg & 0x00000014; //check VFE(bit4) and FE(bit2)
#if INFERNO53XX
            tmpreg2 = ICU_InterruptRead(IRQ_PDD0 + dmaCh * 3); //Inferno, offset of IRQ in 53xx is 3 not 1
#else
            tmpreg2 = ICU_InterruptRead(IRQ_PDD0 + dmaCh);
#endif

            //All of them are zero,which means no errors.
            if ((tmpreg == 0) && (tmpreg2 == 0)) //LPRInLPENSTA && IRQ_PDDn
                resetOK = tTRUE;

            if (tmpreg)
                PRTDRV_PRINTF("DPipe: waiting error to reset\n");

            if (tmpreg2)
                PRTDRV_PRINTF("DPipe: waiting DMA ISR to clear\n");
        }
        while (resetOK == tFALSE);

        //step 4. reset LPMODE.LSR and LPMODE.BC
        tmpreg = IO_READ32(LPRI0LPMODE + PRT_LPRI_PRE_SIZE * dmaCh);
        tmpreg &= (Uint32)(~0x0000C000);
        IO_WRITE32(LPRI0LPMODE + PRT_LPRI_PRE_SIZE * dmaCh, tmpreg);
    }
#endif

    int    rv;
    Uint32 args[30] = {0};
    int    nretargs;
    Uint32 response;

    args[0] = dmaCh;
#if 0 //PRINTDEBUGINFO
    PRTDRV_PRINTF("PRINT_RESET_DMA\n");
#endif
    rv = M3doCommand(PRINT_RESET_DMA, 1, &response, &nretargs, args);
}

/**
 * @fn       tVoid PRTDRV_SetupPif_DmaTriggerIF(tVoid);
 * @brief    Setup how to control dam trigger source when job start up.
 * @param    N/A.
 * @return   None.
 * @note     [Internal function]
 * @note
 */
tVoid PRTDRV_SetupPif_DmaTriggerIF(tVoid)
{
#if 0
        IO_WRITE32(LPSBCFG0, 0x0); //LPRIVSYNC0
        IO_WRITE32(LPSBCFG1, 0x1); //LPRIVSYNC1
        IO_WRITE32(LPSBCFG2, 0x2); //LPRIVSYNC2
        IO_WRITE32(LPSBCFG3, 0x3); //LPRIVSYNC3
#endif

    int    rv;
    Uint32 args[30] = {0};
    int    nretargs;
    Uint32 response;

    rv = M3doCommand(PRINT_TRIGGER_DMA, 1, &response, &nretargs, args);
}

/**
 * @fn       tVoid PRTDRV_SetupPif_DmaIF(tUint8 dmaCh);
 * @brief    To set up the Dma working mode.
 * @param    tUint8 dmaCh -- To indicate the dma channel.
 * @return   None.
 * @note     [Internal function]
 */
tVoid PRTDRV_SetupPif_DmaIF(tUint8 dmaCh, PM_ScriptDscp *pPageScript)
{
    int    rv;
    Uint32 args[30];
    int    nretargs;
    Uint32 response;

    unsigned char swapD           = 0;
    int           val_LPRI0LPMODE = 0;

    if (pPageScript->videoOutputMode == 1) //one bit serial data mode, Vsync/Lsync are used in pulse mode.
    {
        //val_LPRI0LPMODE =  0x00001C05;
#if 1
        val_LPRI0LPMODE = 0x00001C05;
#else
        val_LPRI0LPMODE = 0x00001800;
#endif
    }
    else // 2 bit serial data mode, Vsync/Lsync are used in pulse mode.
    {
        val_LPRI0LPMODE = 0x00001405;
    }

    if (PrintManager.virtualMode)
    {
        //val_LPRI0LPMODE = 0xFFFFFFFA;
        val_LPRI0LPMODE = 0x00001C00;
    }

    // FIFO data swap, from [7:0] to [0:7]
    swapD = (JOBMGR_GetPageCodec(pPageScript->pPage) == JOBMGR_PDL_DISPLAYLIST) ? 0x1F : 0x07;

    //if ((hw_ver == 0x01) && ((1 == dmaCh) || (3 == dmaCh)))
    //    swapD &= (~0x01);

    //IO_WRITE8(FR_ENDIAN0 + PRT_ENDIAN_PRE_SIZE * dmaCh, swapD);
    args[0] = dmaCh;
    args[1] = val_LPRI0LPMODE;
#if 1
    PRTDRV_PRINTF("A7:PRINT_SETUP_DMA\n");
#endif
    rv = M3doCommand(PRINT_SETUP_DMA, 2, &response, &nretargs, args);
}

#if DUAL_BEAM
tVoid PRTDRV_M3SetFirstDMA(Uint32 dmach)
{
    int    rv;
    Uint32 args[30];
    int    nretargs;
    Uint32 response;

    args[0] = dmach;

    rv = M3doCommand(PRINT_SETFIRST_DMA, 1, &response, &nretargs, args);
}
#else
tVoid PRTDRV_M3SetFirstDMA(void)
{
    int    rv;
    Uint32 args[30];
    int    nretargs;
    Uint32 response;
#if 1
    PRTDRV_PRINTF("Main Core:PRINT_SETFIRST_DMA\n");
#endif
    args[0] = 0;

    rv = M3doCommand(PRINT_SETFIRST_DMA, 1, &response, &nretargs, args);
}
#endif

int PRTDRV_VirtualSendCmd(char *buf, int length)
{
    stPrtDrvVirtualMsgBuf MSG = {0};
    assert(length <= msgENGERR_SIZE);

    MSG.mtype = 1;
    memcpy(MSG.virtualBuf, buf, length);
    assert(msgsnd(msgVirtualEng, &MSG, sizeof(MSG.virtualBuf), 0) == 0);
    return tOK;
}

/**
 * @fn       int PRTDRV_SetFirstDMAData()
 * @brief    To link first two dma bands to DMA register in PrtDrv_Driver.
 * @param
 * @return   None.
 * @note     [Internal function]
 */
int PRTDRV_SetFirstDMAData(PM_PageScriptOrderList *pPageScriptOrderList, tUint8 dmaCh)
{
#if DUAL_BEAM
    if (dmaCh >= MAXDMACNT) return 0;
#endif
    stJobMgr_PageDscp *pPage       = NULL;
    PM_ScriptDscp     *pPageScript = NULL;
    eJobMgr_CMYK       colorId     = colorMapping_ChannelToPrtMgr[dmaCh];
    tInt32             res         = 0 /*, waitTimes = 0*/;
    tUint32            amp         = 0; //A rate to amplify the data size.
    //BANDINFO*          band1 = tNULL, *band2 = tNULL;
    //PRT_MSG            prtMsg = {0};

#if PRINTDEBUGINFO
//    char               colorChar[JOBMGR_MAXCOLORID] = {'C', 'M', 'Y', 'K'};
#endif
    if (pPageScriptOrderList == NULL) return PRTDRV_OK;

    pPageScript = pPageScriptOrderList->pPageScript;
    if (pPageScript == NULL) return PRTDRV_OK;

    pPage = pPageScript->pPage;

#if PURE_VIRTUAL_MODE
    if (PrintManager.virtualMode)
    {
        pPageScript->setFirstDMACnt[colorId]++;
        return PRTDRV_OK;
    }
#endif

#if DUAL_BEAM

    if (colorId != JOBMGR_Cyan && colorId != JOBMGR_Magenta) return PRTDRV_OK;

#else

    if (JOBMGR_GetPageColorSpace(pPage) != JOBMGR_Color && colorId != JOBMGR_Black) return PRTDRV_OK;

#endif

#if !PRINT_ALWAYS_TEST
    if (pPageScript->setFirstDMACnt[colorId] == pPageScript->pPage->Copies) return PRTDRV_OK;
#endif

    if (pPageScript->setFirstDMACnt[colorId] >= pPageScript->fedCnt) return PRTDRV_OK;

#if DUAL_BEAM
    if (PrintManager.dmaWorking[dmaCh])
    {
        return 0;
    }
    if (dmaCh == 1)
    {
        return PRTDRV_OK;
    }
#else
    if (PrintManager.dmaWorking)
    {
        return 0;
    }
#endif

#if DUAL_BEAM

    if (pPageScript->dataLength % PIS_STD_BANDLINES)
    {
        if (pPageScript->readyBandCnt[colorId] == ((pPageScript->dataLength / 2 / PIS_STD_BANDLINES) + 1) * pPageScript->paperOutCnt)
            return PRTDRV_OK;
    }
    else
    {
        if (pPageScript->readyBandCnt[colorId] == (pPageScript->dataLength / 2 / PIS_STD_BANDLINES) * pPageScript->paperOutCnt) return PRTDRV_OK;
    }

#else

    if (pPageScript->dataLength % PIS_STD_BANDLINES)
    {
        if (pPageScript->readyBandCnt[colorId] == ((pPageScript->dataLength / PIS_STD_BANDLINES) + 1) * pPageScript->paperOutCnt) return PRTDRV_OK;
    }
    else
    {
        if (pPageScript->readyBandCnt[colorId] == (pPageScript->dataLength / PIS_STD_BANDLINES) * pPageScript->paperOutCnt) return PRTDRV_OK;
    }

#endif

    if (JOBMGR_GetPageColorSpace(pPage) == JOBMGR_Color)
    {
        //Yellow is first color, if yellow is not configured, do not config other color
        if (colorId != JOBMGR_Yellow && pPageScript->setFirstDMACnt[colorId] >= pPageScript->setFirstDMACnt[JOBMGR_Yellow])
        {
            return PRTDRV_OK;
        }
    }

    PRTDRV_PageConfig_SettingRead(pPageScript, &amp);
#if 0
    PRTDRV_PRINTF("Sky: amp=%d, videoOutputMode=%d\n", amp, pPageScript->videoOutputMode);
#endif
    PRTDRV_ResetPif_DmaIF(dmaCh, pPageScript->videoOutputMode);
    PRTDRV_SetupPif_DmaIF(dmaCh, pPageScript);

    if (pPageScript->dataWidth == 0)
    {
        pPageScript->dataWidth  = JOBMGR_GetPageImgWidth(pPage);
        pPageScript->dataLength = JOBMGR_GetPageImgLength(pPage);
    }
#if 0 //NXDEMO
#else

    res = PRTDRV_PageConfig_DmaIf(amp * pPageScript->dataWidth,
#if DUAL_BEAM
                                  pPageScript->dataLength / 2,
#else
                                  pPageScript->dataLength,
#endif
                                  JOBMGR_GetPageTopMargin(pPage), LMargin_E, (eJobMgr_PaperSize)JOBMGR_GetPagePaperSize(pPage), dmaCh,
                                  pPageScript->videoOutputMode, pPage);
#endif
#if 0
    ICU_InterruptEnable(IRQ_PDD0 + dmaCh * 3);
#endif

#if DUAL_BEAM
    PRTDRV_M3SetFirstDMA(dmaCh);
    PrintManager.dmaWorking[dmaCh] = 1;
#else
    PRTDRV_M3SetFirstDMA();
    PrintManager.dmaWorking = 1;
#endif
    pPageScript->setFirstDMACnt[colorId]++;
    pPageScriptOrderList->cfgDMA[dmaCh] = tTRUE;
#if DUAL_BEAM
    PrintManager.dmaDoneCnt[dmaCh] = 0;
#else
    PrintManager.dmaDoneCnt = 0;
#endif
    //PRTDRV_PRINTF("DPipe: Config DMA %c for (J%d-p%d-%d) dataSizeBytes=%d,%d\n", colorChar[colorId], pPageScript->pPage->pParent->JobId, JOBMGR_GetPageId(pPageScript->pPage), pPageScriptOrderList->subId, band1->dataSizeBytes, band2 ? band2->dataSizeBytes : 0);

    return PRTDRV_OK;
}

//=========[ Driver Tasks]============================================

tBool PRTDRV_SetNewJobStatus(stJobMgr_JobDscp *pJob)
{
    tUint8          status             = 0;
    tUint32         defaultStatusValue = STATUSSERV_PRINT_FROM_NONE;
    eJobMgr_JobType jobType            = JOBMGR_UnknowJob;

    if (pJob == PrintManager.pRunningSecureJob && PrintMgr_GetFirstJob() != tNULL) return tFALSE;

    if (PrintManager.pRunningSecureJob == tNULL && PrintMgr_GetFirstJob() == tNULL)
    {
        StatusServ_ClrPrnStatusBit(STATUSSERV_PrintStatus, STATUSSERV_PRINT_CANCELING);
    }

    if (pJob == NULL)
    {
        status = STATUSSERV_PRINT_FROM_NONE;
        StatusServ_SetPrnStatus(STATUSSERV_PrintSource, &status);
        status = STATUSSERV_PRINT_NO_RECEIVING;
        StatusServ_SetPrnStatus(STATUSSERV_PrintRxStatus, &status);
        status = STATUSSERV_PRT_PJL_NO_ERROR;
        StatusServ_SetPrnStatus(STATUSSERV_PrintParserError, &status);

        StatusServ_ClearPrnStatus(STATUSSERV_PrintDocuName, &defaultStatusValue);

        return tTRUE;
    }

    jobType = (eJobMgr_JobType)JOBMGR_GetJobType(pJob);
    //PRTDRV_PRINTF("tmp: PRTDRV_SetNewJobStatus type=%d, port=%d, jobId=%d, del=%d\n", jobType, JOBMGR_GetJobSubSrcType(pJob), pJob->JobId, JOBMGR_IsJobDelete(pJob));

    StatusServ_SetPrnStatusBit(STATUSSERV_PrintRxStatus, STATUSSERV_PRINT_RECEIVING);

    switch (jobType)
    {
        case JOBMGR_CopyJob:
        case JOBMGR_USBPrintJob:
            status = STATUSSERV_PRINT_FROM_NONE;
            StatusServ_SetPrnStatus(STATUSSERV_PrintSource, &status);
            status = STATUSSERV_PRINT_RX_PROCESSING;
            StatusServ_SetPrnStatus(STATUSSERV_PrintRxSrc, &status);
            break;

        default:

            switch (JOBMGR_GetJobSubSrcType(pJob))
            {
                case JOBMGR_SubSrcLPD:
                    status = STATUSSERV_PRINT_FROM_LPD;
                    StatusServ_SetPrnStatus(STATUSSERV_PrintSource, &status);
                    status = STATUSSERV_PRINT_RX_FROM_LPD;
                    StatusServ_SetPrnStatus(STATUSSERV_PrintRxSrc, &status);
                    break;
                case JOBMGR_SubSrcP9100:
                    status = STATUSSERV_PRINT_FROM_P9100;
                    StatusServ_SetPrnStatus(STATUSSERV_PrintSource, &status);
                    status = STATUSSERV_PRINT_RX_FROM_P9100;
                    StatusServ_SetPrnStatus(STATUSSERV_PrintRxSrc, &status);
                    break;
                case JOBMGR_SubSrcIPP:
                    status = STATUSSERV_PRINT_FROM_IPP;
                    StatusServ_SetPrnStatus(STATUSSERV_PrintSource, &status);
                    status = STATUSSERV_PRINT_RX_FROM_IPP;
                    StatusServ_SetPrnStatus(STATUSSERV_PrintRxSrc, &status);
                    break;
                case JOBMGR_SubSrcWSD:
                    status = STATUSSERV_PRINT_FROM_WSD;
                    StatusServ_SetPrnStatus(STATUSSERV_PrintSource, &status);
                    status = STATUSSERV_PRINT_RX_FROM_WSD;
                    StatusServ_SetPrnStatus(STATUSSERV_PrintRxSrc, &status);
                    break;
                case JOBMGR_SubSrcReport:
                    status = STATUSSERV_PRINT_FROM_REPORT;
                    StatusServ_SetPrnStatus(STATUSSERV_PrintSource, &status);
                    status = STATUSSERV_PRINT_RX_FROM_REPORT;
                    StatusServ_SetPrnStatus(STATUSSERV_PrintRxSrc, &status);
                    break;
                case JOBMGR_SubSrcGCP:
                    status = STATUSSERV_PRINT_FROM_GCP;
                    StatusServ_SetPrnStatus(STATUSSERV_PrintSource, &status);
                    status = STATUSSERV_PRINT_RX_FROM_GCP;
                    StatusServ_SetPrnStatus(STATUSSERV_PrintRxSrc, &status);
                    break;
                case JOBMGR_SubSrcAirFax:
                    status = STATUSSERV_PRINT_FROM_IPP;
                    StatusServ_SetPrnStatus(STATUSSERV_PrintSource, &status);
                    status = STATUSSERV_PRINT_RX_FROM_AIRFAX;
                    StatusServ_SetPrnStatus(STATUSSERV_PrintRxSrc, &status);
                    break;
                case JOBMGR_SubSrcUSB:

                default:

                    status = STATUSSERV_PRINT_FROM_PC;

                    StatusServ_SetPrnStatus(STATUSSERV_PrintSource, &status);

                    status = STATUSSERV_PRINT_RX_FROM_PC;
                    StatusServ_SetPrnStatus(STATUSSERV_PrintRxSrc, &status);
                    break;
            }
    }

    StatusServ_SetPrnStatus(STATUSSERV_PrintDocuName, JOBMGR_GetJobDocName(pJob));

    return tTRUE;
}

tBool PRTDRV_CheckSizeType(PM_ScriptDscp *pPageScript)
{
    tUint8             status_ManualStart = 0;
    tUint16            jobTotalPage       = 0;
    eJobMgr_Resolution resIn_type         = JOBMGR_Standard;
    stJobMgr_PageDscp *pPage              = pPageScript->pPage;

    pPageScript->InputTray = 0; //ENG_TRAY_MSI;
    //force set output res to 600x600x1 without 600x600x2(PC/NW JBIG printing)
    resIn_type = (eJobMgr_Resolution)JOBMGR_GetPageInResolution(pPage);

    switch (resIn_type)
    {
        case JOBMGR_DPI600x600:
        case JOBMGR_DPI600x600x2bit:
        case JOBMGR_DPI600x600x4bit:
        case JOBMGR_DPI1200x1200:
            JOBMGR_SetPagePrtResolution(pPage, resIn_type);
            break;
        default:
            JOBMGR_SetPagePrtResolution(pPage, JOBMGR_DPI600x600);
    }

    jobTotalPage = pPageScript->pPage->pParent->jobTotalPage;

    if ((pPageScript->pPage->pParent->pPrt->PrtFunc & JOBMGR_ManualDuplex) && (pPageScript->pPage->pParent->jobTotalPage % 2)) jobTotalPage++;

        //PRTDRV_PRINTF("tmp: prtFunc=%d, pageId=%d, flip=%d, pageid=%d, jobTotalPage=%d,qty=%d \n", pPageScript->pPage->pParent->pPrt->PrtFunc, pPageScript->pPage->PageId, pPageScript->pPage->pParent->pPrt->FlipPage, pPageScript->pPage->PageId, jobTotalPage, pPageScript->pPage->pParent->pPrt->CopyQty);
#if 0
    if ((pPageScript->pPage->pParent->pPrt->PrtFunc & JOBMGR_ManualDuplex)
        && (pPageScript->pPage->pParent->pPrt->FlipPage == pPageScript->pPage->PageId))
#endif
    if ((pPageScript->pPage->pParent->pPrt->PrtFunc & JOBMGR_ManualDuplex) && (pPageScript->pPage->pParent->pPrt->FlipPage >= 2)
        && ((pPageScript->pPage->pParent->pPrt->FlipPage == pPageScript->pPage->PageId)
            || (pPageScript->pPage->pParent->pPrt->FlipPage
                == pPageScript->pPage->PageId % (jobTotalPage / pPageScript->pPage->pParent->pPrt->CopyQty)))
        && (pPageScript->subId == 0))
    {
        if (PrintManager.manualDuplexStop)
        {
            if (StatusServ_GetPrnStatusBit(STATUSSERV_PrintStatus, STATUSSERV_PRINT_NEED_MANUAL_START)) return tFALSE;

            PrintManager.manualDuplexStop = tFALSE;
        }
        else
        {
            if (pPageScript == PrintMgr_GetFirstPageScript())
            {
                status_ManualStart = STATUSSERV_PRINT_NEED_MANUAL_START;
                StatusServ_SetPrnStatus(STATUSSERV_PrintStatus, &status_ManualStart);
                PrintManager.manualDuplexStop                  = tTRUE;
                pPageScript->pPage->pParent->pPrt->manualStart = tTRUE;
                PrintManager.ManualDuplexCmd                   = tTRUE;
            }

            return tFALSE;
        }
    }

    return tTRUE;
}

void PRTDRV_SendEngParameters(struct PrtMgr_ScriptDscp *pPageScript)
{
    tUint16            tmpWidth = 0, tmpLength = 0;
    eJobMgr_Resolution resolution;
    static tBool       ManualDuplexCmd = tFALSE;

    unsigned char SData[2] =
        {
            0,
        },
                  RData[2] = {
                      0,
                  };
    PRTDRV_PRINTF("PRTDRV_SendEngParameters: enter!\n");
    if (PrintManager.duplex != pPageScript->pPage->duplex)
    {
		PRTDRV_PRINTF("PRTDRV_SendEngParameters: duplex!CMD_EC4\n");
        //EC4
        SData[0] = CMD_EC4;

        if (pPageScript->pPage->duplex == SIMPLEX_PAGE)
            SData[1] = 0;
        else if (pPageScript->pPage->duplex == BACK_SIDE_PAGE)
            SData[1] = 8;
        else
            SData[1] = 2;

        par_SendRecvCmd(SData, RData, 2);

        PrintManager.duplex = pPageScript->pPage->duplex;
    }

    resolution = JOBMGR_GetPagePrtResolution(pPageScript->pPage);
    if (PrintManager.resolution != resolution)
    {
		PRTDRV_PRINTF("PRTDRV_SendEngParameters: resolution!CMD_EC5\n");
        //EC5
        PRTDRV2_Send_SELMAP(resolution);
        PrintManager.resolution = resolution;
        if (resolution == JOBMGR_DPI600x600)
        {
            SwitchVideoPll(0);
        }
        else
        {
            SwitchVideoPll(1);
        }
    }

    if (PrintManager.mediaType != JOBMGR_GetPageMediaType(pPageScript->pPage))
    {
		
        PRTDRV2_Send_SELMED((char)JOBMGR_GetPageMediaType(pPageScript->pPage));
        PrintManager.mediaType = JOBMGR_GetPageMediaType(pPageScript->pPage);
		PRTDRV_PRINTF("PRTDRV_SendEngParameters: mediaType=%d\n",PrintManager.mediaType);
    }

    //EC10
    if (PrintManager.paperSize != JOBMGR_GetPagePaperSize(pPageScript->pPage))
    {
        PRTDRV2_Send_SETPSIZE(pPageScript->InputTray, (char)JOBMGR_GetPagePaperSize(pPageScript->pPage));
        if (JOBMGR_FreeSize == JOBMGR_GetPagePaperSize(pPageScript->pPage))
        {
            tmpWidth  = JOBMGR_GetPagePaperWidth(pPageScript->pPage);  //unit is 0.1mm
            tmpLength = JOBMGR_GetPagePaperLength(pPageScript->pPage); //unit is 0.1mm
            //EC11
            PRTDRV2_Send_SETUSIZE((char)JOBMGR_GetPagePaperSize(pPageScript->pPage), pPageScript->InputTray, tmpWidth, tmpLength);
            PrintManager.width  = tmpWidth;
            PrintManager.length = tmpLength;
        }

        PrintManager.paperSize = JOBMGR_GetPagePaperSize(pPageScript->pPage);
		PRTDRV_PRINTF("PRTDRV_SendEngParameters: paperSize=%d\n",PrintManager.paperSize );
    }
    else if (JOBMGR_FreeSize == JOBMGR_GetPagePaperSize(pPageScript->pPage))
    {
        tmpWidth  = JOBMGR_GetPagePaperWidth(pPageScript->pPage);  //unit is 0.1mm
        tmpLength = JOBMGR_GetPagePaperLength(pPageScript->pPage); //unit is 0.1mm
        if (PrintManager.width != tmpWidth || PrintManager.length != tmpLength)
        {
            PRTDRV2_Send_SETUSIZE((char)JOBMGR_GetPagePaperSize(pPageScript->pPage), pPageScript->InputTray, tmpWidth, tmpLength);
            PrintManager.width  = tmpWidth;
            PrintManager.length = tmpLength;
        }
		PRTDRV_PRINTF("PRTDRV_SendEngParameters: JOBMGR_FreeSize\n" );
    }

    //EC22
    if (PrintManager.ManualDuplexCmd != ManualDuplexCmd)
    {
        SData[0] = CMD_EC22;
        if (PrintManager.ManualDuplexCmd == tFALSE)
            SData[1] = 0;
        else
            SData[1] = 1;

        par_SendRecvCmd(SData, RData, 2);
        ManualDuplexCmd = PrintManager.ManualDuplexCmd;
		PRTDRV_PRINTF("PRTDRV_SendEngParameters: ManualDuplexCmd CMD_EC22\n" );
    }
}

extern tUint32 PrtMgr_BitPerPixel(stJobMgr_PageDscp *pPage);

tVoid PRTDRV_ConsumeM3PageData(PM_ScriptDscp *pPageScript)
{
    int    rv;
    Uint32 args[30] = {0};
    int    nretargs;
    Uint32 response;
    Uint32 M3CommandLen = 1;

    tInt32             actual_size = 0;
    stFlushPageTaskMsg taskMsg;

    rv = M3doCommand(PRINT_CONSUME_M3PAGEDATA, M3CommandLen, &response, &nretargs, args);

    //Waiting till M3 flushed all bands of its first page.
    actual_size = msgrcv(msgQFlushPage, &taskMsg, sizeof(stFlushPageMsg), 0, 0);

    //flushPageMsg = taskMsg.flushPageMsg;
}

tVoid PRTDRV_ConsumePageData(PM_ScriptDscp *pPageScript)
{
    eJobMgr_CMYK colorId = 0, colorStartId = 0, colorEndId = JOBMGR_MAXCOLORID - 1;
    tUint16      consumedBandCnt[JOBMGR_MAXCOLORID] = {0, 0, 0, 0}, planeDoneCnt_Min = 0xffff, planeDoneCnt_Max = 0;
    BANDINFO    *pBand                  = NULL;
    tBool isLastBand[JOBMGR_MAXCOLORID] = {tFALSE, tFALSE, tFALSE, tFALSE}, consumeThisPlane[JOBMGR_MAXCOLORID] = {tFALSE, tFALSE, tFALSE, tFALSE};

    if (JOBMGR_GetPageColorSpace(pPageScript->pPage) != JOBMGR_Color && JOBMGR_GetPageColorSpace(pPageScript->pPage) != JOBMGR_Color2RGB)
    {
        colorStartId = colorEndId = JOBMGR_Black;
        //.pPageScript->planeDoneCnt[JOBMGR_Yellow] = pPageScript->planeDoneCnt[JOBMGR_Magenta] = pPageScript->planeDoneCnt[JOBMGR_Cyan] = pPageScript->planeDoneCnt[JOBMGR_Black];
#if DUAL_BEAM
        colorStartId              = JOBMGR_Cyan;
        colorEndId                = JOBMGR_Magenta;
        isLastBand[JOBMGR_Yellow] = isLastBand[JOBMGR_Black] = tTRUE;
        //.pPageScript->planeDoneCnt[JOBMGR_Yellow] = pPageScript->planeDoneCnt[JOBMGR_Black] = pPageScript->dispatchedCnt;
#else
        isLastBand[JOBMGR_Cyan] = isLastBand[JOBMGR_Magenta] = isLastBand[JOBMGR_Yellow] = tTRUE;
        colorStartId = colorEndId = JOBMGR_Black;
        //.pPageScript->planeDoneCnt[JOBMGR_Yellow] = pPageScript->planeDoneCnt[JOBMGR_Magenta] = pPageScript->planeDoneCnt[JOBMGR_Cyan] = pPageScript->dispatchedCnt;
#endif
    }

    //To know min and max planeDoneCnt
    for (colorId = colorStartId; colorId <= colorEndId; colorId++)
    {
        if (planeDoneCnt_Min > pPageScript->planeDoneCnt[colorId]) planeDoneCnt_Min = pPageScript->planeDoneCnt[colorId];

        if (planeDoneCnt_Max < pPageScript->planeDoneCnt[colorId]) planeDoneCnt_Max = pPageScript->planeDoneCnt[colorId];
    }

    //To confirm which plane should be consume
    for (colorId = colorStartId; colorId <= colorEndId; colorId++)
    {
        if (planeDoneCnt_Min == planeDoneCnt_Max)
        {
            if (pPageScript->planeDoneCnt[colorId] == pPageScript->dispatchedCnt)
                consumeThisPlane[colorId] = tFALSE;
            else
                consumeThisPlane[colorId] = tTRUE;
        }
        else if (pPageScript->planeDoneCnt[colorId] < planeDoneCnt_Max)
            consumeThisPlane[colorId] = tTRUE;
        else
            consumeThisPlane[colorId] = tFALSE;

        if (consumeThisPlane[colorId] == tFALSE) isLastBand[colorId] = tTRUE;
    }

    while (isLastBand[JOBMGR_Yellow] == tFALSE || isLastBand[JOBMGR_Magenta] == tFALSE || isLastBand[JOBMGR_Cyan] == tFALSE
           || isLastBand[JOBMGR_Black] == tFALSE)
    {
        for (colorId = colorStartId; colorId <= colorEndId; colorId++)
        {
            if (consumeThisPlane[colorId] && isLastBand[colorId] == tFALSE)
            {
                pBand = bufB_DataGet(colorId, pPageScript->pPrintBuf);

                assert(pBand);
                isLastBand[colorId] = pBand->isLastBand;
                bufB_DataUsed(pBand);
                consumedBandCnt[colorId]++;
            }
        }
    }

    //if (consumedBandCnt[0] ||consumedBandCnt[1] || consumedBandCnt[2] || consumedBandCnt[3])
    {
        PRTDRV_PRINTF("DPipe: PRTDRV_ConsumePageData(jobId = %d, pageId = %d), consumedBandCnt = %d,%d,%d,%d\n", pPageScript->pPage->pParent->JobId,
                      pPageScript->pPage->PageId, consumedBandCnt[0], consumedBandCnt[1], consumedBandCnt[2], consumedBandCnt[3]);
    }
}
#if 1 //CFG_Authorization
void PRTDRV_ClearMem(void)
{
    tUint32 *pBuf = MemServ_GetWorkingBufPtr(MemServ_PrintBuf);

    PRTDRV_PRINTF("Clear Print Memory:\n");
    memset(pBuf, 1, MEMSERV_PrintBufSize);
    PRTDRV_PRINTF("memset to 1\n");
    memset(pBuf, 0, MEMSERV_PrintBufSize);
    PRTDRV_PRINTF("memset to 0\n");
}
#endif
void PRTDRV_TryToRemoveJob(stJobMgr_JobDscp *pJob)
{
    tUint8            status     = STATUSSERV_RPT_IDLE;
    float             ppm        = 0;
    stJobMgr_JobDscp *pNextJob   = NULL;
    PM_JobScript     *pJobScript = NULL, *pJobScript_Tmp = NULL;
    eJobMgr_JobType   jobType = JOBMGR_UnknowJob;

    if (pJob == NULL) return;

    if (JOBMGR_IsJobWriting(pJob) || JobMgr_GetJobHeadPrintPage(pJob)) return;

#if 1
    assert(semaphore_p(PrintManager.mutex_lock) == 1);
#else
    TSmutexWait(PrintManager.mutex_lock);
#endif

    pJobScript = PrintMgr_GetJobScript(pJob);

    if (pJobScript == NULL)
    {
        PRTDRV_PRINTF("DPipe: PRTDRV_TryToRemoveJob, pJobScript == NULL\n");
#if 1
        assert(semaphore_v(PrintManager.mutex_lock) == 1);
#else
        TSmutexSignal(PrintManager.mutex_lock);
#endif

        return;
    }

    if (pJobScript->endInputJob == tFALSE)
    {
        PRTDRV_PRINTF("DPipe: endInputJob is false\n");
#if 1
        assert(semaphore_v(PrintManager.mutex_lock) == 1);
#else
        TSmutexSignal(PrintManager.mutex_lock);
#endif
        return;
    }

#if INCLUDED_CLOUD //
    if (JOBMGR_GetJobBarcodeMode(pJob))
    {
        //extern char QR_Code_Print;
        //QR_Code_Print = 0;
        if (JOBMGR_GetJobTotalPage(pJob) != 0)
        {
            int refresh = 0;
            SetServ_GetCurrentNetSetting(SETSERV_NET_QRCodeRefreshFlag, &refresh);
            if (refresh == 1)
            {
                refresh = 0;
                SetServ_SetCurrentNetSetting(SETSERV_NET_QRCodeRefreshFlag, &refresh);
            }
        }
    }
#endif

    pJobScript_Tmp = TAILQ_FIRST(&PrintManager.jobList);

    if (pJobScript == pJobScript_Tmp)
    {
        pJobScript_Tmp = TAILQ_NEXT(pJobScript_Tmp, entry);

        if (pJobScript_Tmp) pNextJob = pJobScript_Tmp->pJob;
    }

    if (pNextJob == tNULL || !pNextJob->pPrt->sizeError) StatusServ_ClrPrnStatusBit(STATUSSERV_PrintErr, STATUSSERV_PRINT_SIZE_ERROR);

    if (pNextJob == tNULL || !pNextJob->pPrt->sizeUnmatch) StatusServ_ClrPrnStatusBit(STATUSSERV_PrintErr, STATUSSERV_PRINT_MEDIA_SIZE_UNMATCH);

    if (pNextJob == tNULL || !pNextJob->pPrt->typeUnmatch) StatusServ_ClrPrnStatusBit(STATUSSERV_PrintErr, STATUSSERV_PRINT_MEDIA_TYPE_UNMATCH);

    StatusServ_ClrPrnStatusBit(STATUSSERV_PrintStatus, STATUSSERV_PRINT_NEED_MANUAL_START);
    StatusServ_ClrPrnStatusBit(STATUSSERV_PrintErr, STATUSSERV_PRINT_PIS_ERR);

    if (pNextJob == NULL)
    {
        StatusServ_ClrPrnStatusBit(STATUSSERV_PrintStatus, STATUSSERV_PRINT_BUSY);
        StatusServ_ClrPrnStatusBit(STATUSSERV_PrintRxStatus, STATUSSERV_PRINT_RECEIVING);
        StatusServ_SetPrnStatusBit(STATUSSERV_PrintStatus, STATUSSERV_PRINT_IDLE);
        //PRTDRV_PRINTF("tmp: clear STATUSSERV_PRINT_BUSY\n");
    }

    if (pNextJob == NULL || !JOBMGR_IsJobDelete(pNextJob))
    {
        if (!JOBMGR_IsJobDelete(PrintManager.pRunningSecureJob))
        {
            StatusServ_ClrPrnStatusBit(STATUSSERV_PrintStatus, STATUSSERV_PRINT_CANCELING);
        }
    }

    jobType = (eJobMgr_JobType)JOBMGR_GetJobType(pJob);

    if (jobType == JOBMGR_ReportJob)
    {
        status = STATUSSERV_RPT_IDLE;
        StatusServ_SetReportStatus(STATUSSERV_RptStatus, &status);
        status = STATUSSERV_RPT_NO_ERROR;
        StatusServ_SetReportStatus(STATUSSERV_RptError, &status);
    }

    if (JOBMGR_IsJobDelete(pJob)) JobMgr_PrtMgrAck(pJob);

    if (PrintManager.paperOutCnt > 1 && (PrintManager.currentPaperOutTime - PrintManager.firstPaperOutTime > 0))
        ppm = (float)((PrintManager.paperOutCnt - 1) * 60000) / (float)(PrintManager.currentPaperOutTime - PrintManager.firstPaperOutTime);
    else if (PrintManager.currentPaperOutTime != PrintManager.feedTime && (PrintManager.currentPaperOutTime - PrintManager.feedTime > 0))
        ppm = 60000 / (PrintManager.currentPaperOutTime - PrintManager.feedTime);

#if PRINTDEBUGINFO
    PRTDRV_PRINTF("DPipe: Job(%d) print out pages = %d, PPM = %f, jobErrCnt = %d\n", pJob->JobId, PrintManager.paperOutCnt, ppm,
                  PrintManager.jobErrCnt);
#else
    PRTDRV_PRINTF("DPipe: Job complete, JobId = %d, print out pages = %d, PPM = %f\n", pJob->JobId, PrintManager.paperOutCnt, ppm);
#endif

#if CFG_ScanManager
    if ((JobMgr_GetSystemMode() == JOBMGR_IDCardCopyMode) || (JobMgr_GetSystemMode() == JOBMGR_DuplexCopyMode))
    {
        if (pJob->JobStatus & JOBMGR_JobDelete)
            JobMgr_InitSystemMode(JOBMGR_NormalMode);
        else
        {
            if (JobMgr_GetSystemMode() == JOBMGR_IDCardCopyMode)
            {
                StartIDCardCopyMode();
            }
            else
            {
                StartDuplexCopyMode();
            }
        }
    }
#endif

#if 0 //CFG_Authorization
    if (jobType == JOBMGR_PrintJob
            || jobType == JOBMGR_CopyJob
            || jobType == JOBMGR_ReportJob)
    {
    	//extern int DataSave_Log_Print_Add(void*data ,int len);
    	extern int DataSave_Log_Print_Delete_Bef(int index);
    	extern int DataSave_Log_Print_DeleteAll(void);
    	extern int DataSave_Log_Print_TotalNum(void);
    	extern int Secure_DataGet_PrintLog(int index ,void* data, int len);
    	char printLog[480] = {0};
    	

    	//DataSave_Log_Print_Add((void *)printLog , 480);
#if 0
    	DataSave_Log_Print_DeleteAll();
    	PRTDRV_PRINTF("Sky: DataSave_Log_Print_DeleteAll\n");
#else
    {
    	int TotalNum = 0, i = 0;
    	TotalNum = DataSave_Log_Print_TotalNum();
    	PRTDRV_PRINTF("Sky: 1 total %d records\n", TotalNum);
			    for (i = 1; i <= TotalNum; i++)
  		    {
            Secure_DataGet_PrintLog(i, (void *)printLog, 480);
            PRTDRV_PRINTF("Read: %d-%s\n", i, (printLog));
            TASKSLEEP_MILLISECONDS(1);            
  		    }
  	      DataSave_Log_Print_DeleteAll();
  	}
#endif
    	if (0)
    	{
    		tUint32 i = 0;
    		for (i = 0; i < 512; i++)
    		{    			
    			sprintf(printLog, "Sky test %d\n", i);
    			DataSave_Log_Print_Add((void *)printLog , 480);
    			TASKSLEEP_MILLISECONDS(10);
    		}
    		
    		PRTDRV_PRINTF("Sky: 2 total %d records\n", DataSave_Log_Print_TotalNum());
				
				
    		//Sky test
    		if (1)
    		{
    		    char printLogData[480] = {0};
    		    tUint32 j = DataSave_Log_Print_TotalNum();
    		    for (i = 0; i < j; i++)
    		    {
#if 1
    		    	//Need check withzhangzhu
#if 1
    		    		DataSave_Log_Print_Read(1 , (void *)printLogData, 480);
	            	DataSave_Log_Print_Delete_Bef(1);
#else
    		    		//DataSave_Log_Print_Read(0 , (void *)printLogData, 480);
	            	//DataSave_Log_Print_Delete_Bef(1);
#endif
#else
	            DataSave_Log_Print_Read(i, (void *)printLogData, 480);
#endif
	            //if (i % 100 == 0)
	            {
	            	PRTDRV_PRINTF("Read: %d-%s\n", i, printLogData);
	            	TASKSLEEP_MILLISECONDS(1);
	            }
    		    }

    		    PRTDRV_PRINTF("Sky: 3 total %d records\n", DataSave_Log_Print_TotalNum());
    		}
    	}
    }
#endif
#if CFG_Authorization
    if (jobType == JOBMGR_PrintJob || jobType == JOBMGR_CopyJob || jobType == JOBMGR_ReportJob)
    {
        extern char *systemFwVersionGet(void);
        extern int   DataSave_Log_PrintW_Add(void *data, int len);
        extern int   DataSave_Log_PrintW_TotalNum(void);
        char         printLog[480] = {0};
        char        *ptmp          = printLog;
        tUint16      printLogLen   = 0;
        tUint32      year, month, day, hour, minute, second;
        //char hostname[65] = "1234567890123456789012345678901234567890123456789012345678901234";
        //char username[33] = "12345678901234567890123456789012";
        //char docuname[129]= "12345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678";
        tUint8 MACAddress[10] = {0};
        tUint8 ethEnable      = 0;

        if (jobType == JOBMGR_PrintJob)
        {
            //DATE
            year  = (pJob->Date >> 9) & 0x7FFFFF;
            month = (pJob->Date >> 5) & 0x0F;
            day   = pJob->Date & 0x1F;
            sprintf(ptmp, "DATE:%d%d%d%d/%d%d/%d%d;", year / 1000, (year / 100) % 10, (year % 100) / 10, (year % 100) % 10, month / 10, month % 10,
                    day / 10, day % 10);
            printLogLen = strlen(ptmp);

            //TIME
            hour   = (pJob->Time >> 12) & 0x1F;
            minute = (pJob->Time >> 6) & 0x3F;
            second = pJob->Time & 0x3F;
            sprintf((ptmp + printLogLen), "TIME:%d%d:%d%d:%d%d;", hour / 10, hour % 10, minute / 10, minute % 10, second / 10, second % 10);
            printLogLen = strlen(ptmp);

            //HOST
            sprintf((ptmp + printLogLen), "HOST:%s;USER:%s;DOC:%s;", (char *)(pJob->pPrt->HostName), (char *)(pJob->pPrt->UserName),
                    (char *)(pJob->pPrt->DocName));
            //sprintf((ptmp + printLogLen), "HOST:%s;USER:%s;DOC:%s;", hostname, username, docuname);
            printLogLen = strlen(ptmp);
        }
        else
        {
            sprintf(ptmp, "DATE:-;TIME:-;HOST:-;USER:-;DOC:-;");
            printLogLen = strlen(ptmp);
        }

        //job type
        if (pJob->JobType == JOBMGR_PrintJob)
        {
            sprintf((ptmp + printLogLen), "TYPE:Print;");
        }
        else if (pJob->JobType == JOBMGR_CopyJob)
        {
            sprintf((ptmp + printLogLen), "TYPE:Copy;");
        }
        else
        {
            sprintf((ptmp + printLogLen), "TYPE:Report;");
        }
        printLogLen = strlen(ptmp);

        //Size
        switch (pJob->pPrt->jobPaperSize)
        {
            case JOBMGR_A4L:
                sprintf((ptmp + printLogLen), "SIZE:A4;");
                break;
            case JOBMGR_LetterL:
                sprintf((ptmp + printLogLen), "SIZE:Letter;");
                break;
            case JOBMGR_16KL:
                sprintf((ptmp + printLogLen), "SIZE:16K;");
                break;
            case JOBMGR_A5L:
                sprintf((ptmp + printLogLen), "SIZE:A5;");
                break;
            case JOBMGR_A5C:
                sprintf((ptmp + printLogLen), "SIZE:A5 L;");
                break;
            case JOBMGR_A6L:
                sprintf((ptmp + printLogLen), "SIZE:A6;");
                break;
            case JOBMGR_B5L:
                sprintf((ptmp + printLogLen), "SIZE:B5;");
                break;
            case JOBMGR_B6L:
                sprintf((ptmp + printLogLen), "SIZE:B6;");
                break;
            case JOBMGR_B6C:
                sprintf((ptmp + printLogLen), "SIZE:B6 L;");
                break;
            case JOBMGR_Executive:
                sprintf((ptmp + printLogLen), "SIZE:Executive;");
                break;
            case JOBMGR_FreeSize:
                sprintf((ptmp + printLogLen), "SIZE:CUSTOM;");
                break;
            default:
                sprintf((ptmp + printLogLen), "SIZE:A4;");
                break;
        }
        printLogLen = strlen(ptmp);

        //Pages
        sprintf((ptmp + printLogLen), "PAGES:%d;", pJobScript->totalPageCnt);
        printLogLen = strlen(ptmp);

        //Result
        if (JOBMGR_IsJobDelete(pJob) == tTRUE)
        {
            sprintf((ptmp + printLogLen), "RESULT:%s;", "Cancelled");
        }
        else
        {
            sprintf((ptmp + printLogLen), "RESULT:%s;", "Completed");
        }
        printLogLen = strlen(ptmp);

        //FW version
        sprintf((ptmp + printLogLen), "FW:%s;", systemFwVersionGet());
        printLogLen = strlen(ptmp);

        SetServ_GetCurrentNetSetting(SETSERV_NET_PortEnableFlag, (void *)&ethEnable);
        if (ethEnable)
        {
            //MAC Address
            FlashSrv_ReadSysParam_WiredMAC(&(MACAddress[0]));
            sprintf((ptmp + printLogLen), "MAC:%02X:%02X:%02X:%02X:%02X:%02X;", MACAddress[0], MACAddress[1], MACAddress[2], MACAddress[3],
                    MACAddress[4], MACAddress[5]);
            printLogLen = strlen(ptmp);

            //IP
            {
                extern unsigned int lni_GetIPv4AddrSys(char *ifname, int Option, int selection);
                unsigned int        u32Data = 0, ipmode = 0;
                char                Tmp[4] = {0};
                SetServ_GetCurrentNetSetting(SETSERV_NET_IPAddrSource, (void *)&ipmode);
                if (ipmode == 1)
                {
                    SetServ_GetCurrentNetSetting(SETSERV_NET_TcpIPAddress, &u32Data);
                }
                else
                {
                    u32Data = lni_GetIPv4AddrSys("eth0", 1, 1);
                }
                memcpy(Tmp, &u32Data, 4);
                sprintf((ptmp + printLogLen), "IP:%d.%d.%d.%d;", Tmp[0], Tmp[1], Tmp[2], Tmp[3]);
            }
            printLogLen = strlen(ptmp);

            //Protocol
            sprintf((ptmp + printLogLen), "Protocol:HTTPS,LPD,RAW,Bonjour;");
            printLogLen = strlen(ptmp);
        }

#if TONER_WITH_CHIP
        //Toner SN
        if (!PrintManager.virtualMode)
        {
            extern tStatus CRM_Value_Get(int option, void *value);
            Uint8          tonerSN[21] = {0};
            CRM_Value_Get(3, tonerSN);
            sprintf((ptmp + printLogLen), "Toner:%s;", tonerSN);
        }
        else
        {
            sprintf((ptmp + printLogLen), "Toner:UP00160635UP19090306;");
        }
        printLogLen = strlen(ptmp);
#endif

        ptmp[printLogLen] = '\0';
#if 0	
    	PRTDRV_PRINTF("Sky: Print Log-%s\n", ptmp);
#endif
        DataSave_Log_PrintW_Add((void *)printLog, 480);
#if 0
    	PRTDRV_PRINTF("Sky: total %d records\n", DataSave_Log_PrintW_TotalNum());
#endif
#if 0
    	if (0)
    	{
    		tUint32 i = 0;
    		for (i = 0; i < 10000; i++)
    		{
    			PRTDRV_PRINTF("Write: %d \n", i);
    			DataSave_Log_Print_Add((void *)printLog , 480);
    			TASKSLEEP_MILLISECONDS(10);
    		}
    		PRTDRV_PRINTF("Sky: 1 total %d records\n", DataSave_Log_Print_TotalNum());			
    		//Sky test
    		if (0)
    		{
    		    char printLogData[480] = {0};
    		    tUint32 j = DataSave_Log_Print_TotalNum();
    		    for (i = 1; i <= j; i++)
    		    {
    		    	DataSave_Log_Print_Read(1 , (void *)printLogData, 480);
	            DataSave_Log_Print_Delete_Bef(1);
	            if (i % 100 == 0)
	            {
	            	PRTDRV_PRINTF("Read: %d-%s\n", i, printLogData);
	            	TASKSLEEP_MILLISECONDS(1);
	            }
    		    }

    		    PRTDRV_PRINTF("Sky: 2 total %d records\n", DataSave_Log_Print_TotalNum());
    		}
    	}
#endif
    }
#endif

    JobMgr_EndOutputJob(pJob);

    TAILQ_REMOVE(&PrintManager.jobList, pJobScript, entry);
    MemServ_ReleaseMem(MemServ_JobScript, (tUint32 *)pJobScript);

    PrintManager.ManualDuplexCmd = tFALSE;

    if (pNextJob == NULL || !pNextJob->pPrt->manualStart) PrintManager.manualDuplexStop = tFALSE;

    if (pNextJob == tNULL)
    {
        if (TAILQ_EMPTY(&PrintManager.jobList))
        {
            PrintManager.firstPaperOutTime = PrintManager.currentPaperOutTime = 0;
            PrintManager.paperOutCnt                                          = 0;
            PrintManager.jobErrCnt                                            = 0;
            PrintManager.recoveralbelTime                                     = 0;

            PRTDRV_ResetPif_DmaIF(0, 1);

#if CFG_Authorization
            PRTDRV_ClearMem();
#endif
            PrintManager.resolution = JOBMGR_DPIMAX;
            PrintManager.duplex     = 0xFF;
            PrintManager.paperSize  = 0xFF;
            PrintManager.mediaType  = 0xFF;
            PrintManager.width      = 0xFFFF;
            PrintManager.length     = 0xFFFF;
        }
    }

    PRTDRV_SetNewJobStatus(PrintMgr_GetFirstJob());

#if 1
    assert(semaphore_v(PrintManager.mutex_lock) == 1);
#else
    TSmutexSignal(PrintManager.mutex_lock);
#endif
}

tBool PRTDRV_TryToRemovePageScript(PM_ScriptDscp *pPageScript, tBool deleteJob)
{
    stJobMgr_JobDscp  *pJob                 = JOBMGR_GetPageParent(pPageScript->pPage);
    PM_JobScript      *pJobScript           = NULL;
    eJobMgr_ColorSpace colorSpace           = JOBMGR_GetPageColorSpace(pPageScript->pPage);
    PM_ScriptDscp     *pPageScript_BackSide = NULL;
    eJobMgr_CMYK       colorId = JOBMGR_Cyan, colorStartId = JOBMGR_Cyan, colorEndId = JOBMGR_Black;

    if (pPageScript == NULL) return tFALSE;

    if (!deleteJob)
    {
        if (colorSpace == JOBMGR_Color)
        {
            if (pPageScript->planeDoneCnt[JOBMGR_Yellow] != pPageScript->pPage->Copies
                || pPageScript->planeDoneCnt[JOBMGR_Magenta] != pPageScript->pPage->Copies
                || pPageScript->planeDoneCnt[JOBMGR_Cyan] != pPageScript->pPage->Copies
                || pPageScript->planeDoneCnt[JOBMGR_Black] != pPageScript->pPage->Copies)
                return tFALSE;
        }
        else
        {
#if DUAL_BEAM

            if (pPageScript->planeDoneCnt[JOBMGR_Magenta] != pPageScript->pPage->Copies
                || pPageScript->planeDoneCnt[JOBMGR_Cyan] != pPageScript->pPage->Copies)
                return tFALSE;

#else
#if PRINT_ALWAYS_TEST
            return tFALSE;
#else
            if (pPageScript->planeDoneCnt[JOBMGR_Black] != pPageScript->pPage->Copies)
            {
                return tFALSE;
            }
#endif
#endif
        }
#if PRINT_ALWAYS_TEST
        return tFALSE;
#else
        if (pPageScript->paperOutCnt != pPageScript->pPage->Copies)
        {
            return tFALSE;
        }
#endif
    }

    if (JOBMGR_GetPageCodec(pPageScript->pPage) == JOBMGR_PDL_DISPLAYLIST && PrintManager.rawModePDL)
    {
        if (JOBMGR_GetPageColorSpace(pPageScript->pPage) != JOBMGR_Color && JOBMGR_GetPageColorSpace(pPageScript->pPage) != JOBMGR_Color2RGB)
            colorStartId = colorEndId = JOBMGR_Black;
        else
        {
            colorStartId = JOBMGR_Cyan;
            colorEndId   = (eJobMgr_CMYK)(JOBMGR_MAXCOLORID - 1);
        }

        for (colorId = colorStartId; colorId <= colorEndId; colorId++) bufB_DataUsed(pPageScript->pRawModeBuf[colorId]);
    }
    if (pPageScript->faxSplitPageId == 0)
    {
        JobMgr_EndOutputPage(pPageScript->pPage);
    }
    else
    {
        JobCtrler_EndOutputPage_FaxSplitPage(pPageScript->pPage);
        if (pPageScript->faxSplitPageId == pPageScript->pFaxPageScript->faxSplitPageCnt)
        {
            JobMgr_EndOutputPage(pPageScript->pFaxPageScript->pPage);
            MemServ_ReleaseMem(MemServ_PageScript, (tUint32 *)pPageScript->pFaxPageScript);
        }
    }

#if 1
    assert(semaphore_p(PrintManager.mutex_lock) == 1);
#else
    TSmutexWait(PrintManager.mutex_lock);
#endif

    if (!deleteJob && pPageScript->pPage->duplex == FRONT_SIDE_PAGE) pPageScript_BackSide = TAILQ_NEXT(pPageScript, entry);

#if 0
#if PRINTDEBUGINFO
    PRTDRV_PRINTF("DPipe: TAILQ_REMOVE(J%d-p%d).\n", pPageScript->pPage->pParent->JobId, JOBMGR_GetPageId(pPageScript->pPage));
#else
    PRTDRV_PRINTF("DPipe: TIMESTAMP page complete. JobId = %d, pageId = %d.\n", pPageScript->pPage->pParent->JobId, JOBMGR_GetPageId(pPageScript->pPage));
#endif
#endif

    PRTDRV_PRINTF("TIMESTAMP: Paper Out, JobId=%d, pageId=%d\n", pPageScript->pPage->pParent->JobId, JOBMGR_GetPageId(pPageScript->pPage));

    pJobScript = PrintMgr_GetPageScriptInJobList(pPageScript);
    TAILQ_REMOVE(&pJobScript->psList, pPageScript, entry);
    MemServ_ReleaseMem(MemServ_PageScript, (tUint32 *)pPageScript);

    if (pPageScript_BackSide)
    {
        pPageScript = pPageScript_BackSide;

        if (pPageScript->faxSplitPageId == 0)
        {
            JobMgr_EndOutputPage(pPageScript->pPage);
        }
        else
        {
            JobCtrler_EndOutputPage_FaxSplitPage(pPageScript->pPage);
            if (pPageScript->faxSplitPageId == pPageScript->pFaxPageScript->faxSplitPageCnt)
            {
                JobMgr_EndOutputPage(pPageScript->pFaxPageScript->pPage);
                MemServ_ReleaseMem(MemServ_PageScript, (tUint32 *)pPageScript->pFaxPageScript);
            }
        }
#if 0
#if PRINTDEBUGINFO
        PRTDRV_PRINTF("DPipe: TAILQ_REMOVE(J%d-p%d)\n", pPageScript->pPage->pParent->JobId, JOBMGR_GetPageId(pPageScript->pPage));
#else
        PRTDRV_PRINTF("DPipe: paper exit. JobId=%d, pageId=%d)\n", pPageScript->pPage->pParent->JobId, JOBMGR_GetPageId(pPageScript->pPage));
#endif
#endif
        PRTDRV_PRINTF("TIMESTAMP: Paper Out, JobId=%d, pageId=%d)\n", pPageScript->pPage->pParent->JobId, JOBMGR_GetPageId(pPageScript->pPage));

        pJobScript = PrintMgr_GetPageScriptInJobList(pPageScript);
        TAILQ_REMOVE(&pJobScript->psList, pPageScript, entry);
        MemServ_ReleaseMem(MemServ_PageScript, (tUint32 *)pPageScript);
    }

#if 1
    assert(semaphore_v(PrintManager.mutex_lock) == 1);
#else
    TSmutexSignal(PrintManager.mutex_lock);
#endif

    if (PrintManager.fedSheets == 0) StatusServ_ClrPrnStatusBit(STATUSSERV_PrintStatus, STATUSSERV_PRINT_BUSY);

    if (!deleteJob && !JOBMGR_IsJobDelete(pJob)) PRTDRV_TryToRemoveJob(pJob);

    return tTRUE;
}

tPrivate tVoid PRTDRV_RemoveJobPageScriptFromOrderList(PM_JobScript *pJobScript)
{
    PM_PageScriptOrderList *pPageScriptOrderList = PrintManager.pPageScriptOrderList_Header;
    PM_ScriptDscp          *pPageScript          = NULL;
    //PRTDRV_PRINTF("tmp: PRTDRV_RemoveJobPageScriptFromOrderList, pJobScript->pJob->JobId=%d\n", pJobScript->pJob->JobId);

    while (pPageScriptOrderList != NULL)
    {
        if (pPageScriptOrderList->pPageScript->pPage->pParent != pJobScript->pJob) break;

        pPageScript = pPageScriptOrderList->pPageScript;

        PRTDRV_ConsumePageData(pPageScript);

        pPageScriptOrderList = pPageScriptOrderList->pPageScriptOrderList_Next;

        PRTDRV_RemovePageScriptFromOrderList(pPageScript);
    }
}

void PRTDRV_TryToRemovePageScriptFromOrderList(PM_PageScriptOrderList *pPageScriptOrderList)
{
    PM_ScriptDscp     *pPageScript     = pPageScriptOrderList->pPageScript;
    eJobMgr_ColorSpace colorSpace      = JOBMGR_Color;
    tBool              removeFromOrder = tFALSE;

    if (!pPageScriptOrderList->paperOut)
    {
        return;
    }

    assert(pPageScript);

    colorSpace = JOBMGR_GetPageColorSpace(pPageScript->pPage);

    if (colorSpace == JOBMGR_Color)
    {
        if (pPageScriptOrderList->planeDone[JOBMGR_Yellow] && pPageScriptOrderList->planeDone[JOBMGR_Magenta]
            && pPageScriptOrderList->planeDone[JOBMGR_Cyan] && pPageScriptOrderList->planeDone[JOBMGR_Black])
            removeFromOrder = tTRUE;
    }
    else
    {
#if DUAL_BEAM
        if (pPageScriptOrderList->planeDone[JOBMGR_Cyan] && pPageScriptOrderList->planeDone[JOBMGR_Magenta])
        {
            removeFromOrder = tTRUE;
        }
#else
        if (pPageScriptOrderList->planeDone[JOBMGR_Black]) removeFromOrder = tTRUE;
#endif
    }

    if (PrintManager.virtualMode)
    {
        removeFromOrder = tTRUE;
    }

    if (removeFromOrder)
    {
        PRTDRV_RemovePageScriptFromOrderList(pPageScript);
    }
}

tVoid PRTDRV_SetMismatch(tUint8 sheetNo, tUint8 short_Long)
{
    PM_ScriptDscp *pPageScript          = NULL;
    tBool          setErrorSuccessfully = tFALSE;
    PM_JobScript  *pJobScript           = PrintMgr_GetFirstJobScript();

    //tx_mutex_get(&PrintManager.mutex_lock, TX_WAIT_FOREVER);
    if (pJobScript)
    {
        TAILQ_FOREACH(pPageScript, &pJobScript->psList, entry)
        {
            if (pPageScript->sheet == sheetNo)
            {
                pPageScript->drvSizeError                    = short_Long;
                pPageScript->pPage->pParent->pPrt->sizeError = tTRUE;
                setErrorSuccessfully                         = tTRUE;
                break;
            }
        }
    }

    //tx_mutex_put(&PrintManager.mutex_lock);

    if (!setErrorSuccessfully)
    {
        pPageScript = PrintMgr_GetFirstPageScript();

        if (pPageScript)
        {
            pPageScript->drvSizeError                    = short_Long;
            pPageScript->pPage->pParent->pPrt->sizeError = tTRUE;
        }
        else
            PRTDRV_PRINTF("DPipe: SetMismatch fail.\n");
    }
}

tBool PRTDRV_DuplexPaperShort(void)
{
    PM_ScriptDscp *pPageScript = tNULL;

    pPageScript = PrintMgr_GetFirstPageScript();

    if (!pPageScript) return tFALSE;

    if (pPageScript->pPage->duplex != BACK_SIDE_PAGE) return tFALSE;

    if (pPageScript->drvPTA == tTRUE && pPageScript->drvSizeError == 1) return tTRUE;

    return tFALSE;
}

#if 0
/*Restore band who is configed but not consumed by DMA*/
void PRTDRV_ConsumeDmaBandData(void)
{
#if 0
    eJobMgr_CMYK colorId = JOBMGR_Cyan;
    tUint32      dataUsedCnt[JOBMGR_MAXCOLORID] = {0, 0, 0, 0};
    BANDINFO*    pBand = tNULL;

    for (colorId = JOBMGR_Cyan; colorId < JOBMGR_MAXCOLORID; colorId++)
    {
        pBand = gpst4DmaSendingBand1[colorMapping_PrtMgrToChannel[colorId]];

        if (pBand)
        {
            bufB_DataUsed(pBand);
            gpst4DmaSendingBand1[colorMapping_PrtMgrToChannel[colorId]] = NULL;
            dataUsedCnt[colorId]++;
        }

        pBand = gpst4DmaSendingBand2[colorMapping_PrtMgrToChannel[colorId]];

        if (pBand)
        {
            bufB_DataUsed(pBand);
            gpst4DmaSendingBand2[colorMapping_PrtMgrToChannel[colorId]] = NULL;
            dataUsedCnt[colorId]++;
        }
    }

    if (dataUsedCnt[0] || dataUsedCnt[1] || dataUsedCnt[2] || dataUsedCnt[3])
        PRTDRV_PRINTF("DPipe: ConsumeDmaBand(%d, %d, %d, %d)\n", dataUsedCnt[0], dataUsedCnt[1], dataUsedCnt[2], dataUsedCnt[3]);
#endif
    int    rv;
    Uint32 args[30];
    int    nretargs;
    Uint32 response;
    Uint32 M3CommandLen = 1;

    args[0] = 0;

    rv = M3doCommand(
        PRINT_CONSUME_M3DATA,
        M3CommandLen,
        &response,
        &nretargs,
        args
        );
}
#endif

void PRTDRV_DoRecovery(tVoid)
{
    PM_ScriptDscp *pPageScript = NULL;
    eJobMgr_CMYK   colorId = JOBMGR_Cyan, colorStartId = JOBMGR_Cyan, colorEndId = JOBMGR_Black;
    tUint32        recoverPageCnt = 0, jobScriptId = 0, i = 0;
    tBool          restoreJobState = tFALSE, bFound = tFALSE;
    PM_JobScript  *pJobScript = NULL, *consumedJobScript[8] = {
                                         NULL,
                                     };
    PM_PageScriptOrderList *pPageScriptOrderList = PrintManager.pPageScriptOrderList_Header;

    PRTDRV_PRINTF("DPipe: PRTDRV_DoRecovery start @%d\n", BIOSTimerMS());

    PRTDRV_ResetPif_DmaIF(0, 1);

    //PRTDRV_ConsumeDmaBandData();

    /*Recycle dispatched pages*/
#if 1
    assert(semaphore_p(PrintManager.mutex_lock) == 1);
#else
    TSmutexWait(PrintManager.mutex_lock);
#endif

    //to remove all in pPageScriptOrderList
    while (pPageScriptOrderList) //.TAILQ_FOREACH(pPageScript, &pJobScript->psList, entry)
    {
        pPageScript = pPageScriptOrderList->pPageScript;
        assert(pPageScript != NULL);
        pJobScript = PrintMgr_GetJobScript(pPageScript->pPage->pParent);

        bFound = tFALSE;
        for (i = 0; i < jobScriptId; i++) //search whether the pJobScript was saved in array
        {
            if (consumedJobScript[jobScriptId] == pJobScript)
            {
                bFound = tTRUE;
                break;
            }
        }

        if (!bFound) //save pJobScript that is consumed
        {
            assert(jobScriptId < 8);
            consumedJobScript[jobScriptId]         = pJobScript; //This array is used to restore all pages' printing message
            pJobScript->collateJobDispatchedCnt    = 0;
            pJobScript->dispatchDuplexSheetCnt     = 0;
            pJobScript->dispatchDuplexBackSideTime = 0;

            jobScriptId++;
        }

        if (!restoreJobState)
        {
            pPageScript->pPage->pParent->pPrt->sizeError   = tFALSE;
            pPageScript->pPage->pParent->pPrt->sizeUnmatch = tFALSE;
            pPageScript->pPage->pParent->pPrt->typeUnmatch = tFALSE;
            pPageScript->pPage->pParent->pPrt->manualStart = tFALSE;
            restoreJobState                                = tTRUE;
        }

        pPageScript->drvSizeError      = tFALSE;
        pPageScript->drvPTA            = tFALSE;
        pPageScript->sizeErrorPaperOut = tFALSE;

        //.if (pPageScript->dispatchedCnt == pPageScript->paperOutCnt)
        //.    break;

        //PRTDRV_ConsumePageData(pPageScript);
        if (pPageScriptOrderList->planeDone[JOBMGR_Black] == tFALSE)
        {
            PRTDRV_ConsumeM3PageData(pPageScript);
        }

        recoverPageCnt++;

        PRTDRV_RemovePageScriptFromOrderList(pPageScript);
        pPageScriptOrderList = PrintManager.pPageScriptOrderList_Header;
    }

    for (i = 0; i < jobScriptId; i++) // restore all pages' information in each job
    {
        TAILQ_FOREACH(pPageScript, &consumedJobScript[i]->psList, entry)
        {
            pPageScript->dispatchedCnt = pPageScript->fedCnt = pPageScript->subId = pPageScript->paperOutCnt;

            if (pPageScript->dispatchedCnt > consumedJobScript[i]->collateJobDispatchedCnt)
                consumedJobScript[i]->collateJobDispatchedCnt = pPageScript->dispatchedCnt;

            if (JOBMGR_GetPageColorSpace(pPageScript->pPage) != JOBMGR_Color && JOBMGR_GetPageColorSpace(pPageScript->pPage) != JOBMGR_Color2RGB)
            {
#if DUAL_BEAM
                colorStartId = JOBMGR_Cyan;
                colorEndId   = JOBMGR_Magenta;
#else
                colorStartId = colorEndId = JOBMGR_Black;
#endif
            }
            else
            {
                colorStartId = JOBMGR_Cyan;
                colorEndId   = (eJobMgr_CMYK)(JOBMGR_MAXCOLORID - 1);
            }

            for (colorId = colorStartId; colorId <= colorEndId; colorId++)
            {
                pPageScript->setFirstDMACnt[colorId] = pPageScript->planeDoneCnt[colorId] = pPageScript->paperOutCnt;

                if (pPageScript->dataLength % PIS_STD_BANDLINES == 0)
                    pPageScript->readyBandCnt[colorId] = (pPageScript->dataLength / PIS_STD_BANDLINES) * pPageScript->paperOutCnt;
                else
                    pPageScript->readyBandCnt[colorId] = ((pPageScript->dataLength / PIS_STD_BANDLINES) + 1) * pPageScript->paperOutCnt;
            }
        }
    }

#if 1
    assert(semaphore_v(PrintManager.mutex_lock) == 1);
#else
    TSmutexSignal(PrintManager.mutex_lock);
#endif

    PRTDRV_PRINTF("DPipe: PRTDRV_DoRecovery end. recoverPageCnt=%d, @%d\n", recoverPageCnt, BIOSTimerMS());
}

#if PROJ_3IN1
extern int PrinterMarginY;
#endif

void par_JamCancelSetting(void)
{
    prtdrv_M3doCommand(ENGCMD_JAMCANCELSETTING, NULL, NULL);
}

void PRTDRV_EngineMarginAcquire(void)
{

    stDrvMsgCommRespBuf commRespMSG = {0};

    prtdrv_M3doCommand(ENGCMD_ENGINEMARGINACQUIRE, NULL, NULL);
    msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_MarginAcquire, 0);

    if ((commRespMSG.mBuf[0] >> 8) & 0xFF)
    {
        LMargin_E = -commRespMSG.mBuf[1];
    }
    else
    {
        LMargin_E = commRespMSG.mBuf[1];
    }

}

void PRTDRV_EngineNotifySignal(unsigned char signal, unsigned char state)
{
    int    rv;
    Uint32 args[30];
    int    nretargs;
    Uint32 response;
    Uint32 M3CommandLen = 1;

    args[0] = state;
    args[0] |= signal << 8;
    PRTDRV_PRINTF("ttt: PRTDRV_EngineNotifySignal, args[0]=0x%x\n", args[0]);

    rv = M3doCommand(PRINT_NOTIFY_SIGNAL, M3CommandLen, &response, &nretargs, args);
}
#if 0
void PRTDRV_SetFGateTime(PM_ScriptDscp*     pPageScript, int dotCnt)
{
    if (JOBMGR_GetJobType(pPageScript->pPage->pParent) == JOBMGR_CopyJob)
    {
        dotCnt2Time = 1600;

        if (dotCnt != 0)//26?33
        {
#if defined GM337DN || defined G336DN
            dotCnt2Time += 223 * dotCnt;//33ppm
#else
            dotCnt2Time += 288 * dotCnt;//26ppm
#endif
        }
    }
    else
        dotCnt2Time = 1500; //26PPM:28500; 33PPM:22112

#if CFG_ippManager

    if (JOBMGR_GetJobSubSrcType(pPageScript->pPage->pParent) == JOBMGR_SubSrcIPP && (JOBMGR_GetPageCodec(pPageScript->pPage) == JOBMGR_PDL_DISPLAYLIST)) // When Airprint Job, reduce left margin by 10
    {
        dotCnt2Time = 21777;//EG 4.0mm

        dotCnt2Time += 323 * JOBMGR_GetPageTopMargin(pPageScript->pPage);

        dotCnt2Time = dotCnt2Time > 0 ? dotCnt2Time : 10;
    }

#endif
}
#endif
PM_ScriptDscp *PRTDRV_FeedPage(PM_PageScriptOrderList *pPageScriptOrderList, tBool *bNeedRecovery)
{
    eJobMgr_ColorSpace colorSpace  = JOBMGR_BW;
    unsigned char      status      = 0;
    PM_ScriptDscp     *pPageScript = NULL;

    if (pPageScriptOrderList == NULL) return NULL;

    pPageScript = pPageScriptOrderList->pPageScript;

    if (pPageScript == NULL) return NULL;

    if (PrintManager.engineSupportAltoDuplex && pPageScript->pPage->duplex == FRONT_SIDE_PAGE) return NULL;

    StatusServ_SetPrnStatus(STATUSSERV_PrintMediaSize, &pPageScript->pPage->PaperSize);

    if (pPageScript->pPage->duplex != FRONT_SIDE_PAGE)
    {
        if (StatusServ_GetPrnStatusBit(STATUSSERV_PrintStatus, STATUSSERV_PRINT_CANCELING)) return NULL;

        if (JOBMGR_IsJobDelete(pPageScript->pPage->pParent)) return NULL;
    }

#if !PRINT_ALWAYS_TEST
    if (pPageScript->fedCnt == pPageScript->pPage->Copies) return NULL;
#endif

    if (!PrintManager.EngineSendEC3)
    {
        StatusServ_GetEngStatus(STATUSSERV_EngError, &status);

        if (status & STATUSSERV_ENG_ERR_COOLDOWN) return NULL;
    }

    if (pPageScript->pPage->duplex == SIMPLEX_PAGE)
    {
        if (pPageScript->pPage->PageId == 1 && pPageScript->subId == 1 && PrintManager.virtualMode == tFALSE)
        {
            PRTDRV_EngineMarginAcquire();
        }
    }
    else
    {
        if (pPageScript->pPage->PageId == 2 && pPageScript->subId == 1 && PrintManager.virtualMode == tFALSE)
        {
            PRTDRV_EngineMarginAcquire();
        }
    }

    colorSpace = (eJobMgr_ColorSpace)JOBMGR_GetPageColorSpace(pPageScript->pPage);

    if (PrintManager.tonerError || (PrintManager.tonerBWMode && colorSpace == JOBMGR_Color))
    {
        *bNeedRecovery = tTRUE;
        return NULL;
    }
#if  0//NXDEMO
    PRTDRV_Page_If(pPageScript->pPage);
#endif
    if (!PrintManager.EngineSendEC3)
    {
        if (PrintManager.fedSheets == 0)
        {
            unsigned char sendData = 0, recvData = 0;
            sendData = CMD_EC2;
            par_SendRecvCmd(&sendData, &recvData, 1);
#if 0
            PRTDRV_PRINTF("TIMESTAMP: Preparing Print\n");
#endif
        }
    }

#if 1
    if (!PrintManager.EngineSendEC3) PRTDRV_SendEngParameters(pPageScript);
#endif
    //.pPageScript->sheet = pPageScript->pPage->PageId % 255;
    pPageScript->InputTray = ENG_TRAY_MSI;
    pPageScript->fedCnt++;

#if 0
#if PRINTDEBUGINFO
    PRTDRV_PRINTF("DPipe: Designate Printing(J%d-p%d-%d) SheetNo=%d, t=%d, %d\n",
             pPageScript->pPage->pParent->JobId, JOBMGR_GetPageId(pPageScript->pPage), pPageScript->fedCnt, pPageScript->sheet,
             BIOSTimerMS(), PrintManager.EngineSendEC3);
#else
    PRTDRV_PRINTF("DPipe: TIMESTAMP Feed Page, jobId = %d, pageId = %d.\n", pPageScript->pPage->pParent->JobId, JOBMGR_GetPageId(pPageScript->pPage));
#endif
#endif

    PRTDRV_PRINTF("TIMESTAMP: Page Feed, jobId = %d, pageId = %d.\n", pPageScript->pPage->pParent->JobId, JOBMGR_GetPageId(pPageScript->pPage));

    if (!PrintManager.EngineSendEC3)
    {
        PRTDRV2_Send_PFC(pPageScript->sheet, pPageScript->InputTray);
        //PRTDRV_EngineNotifySignal(C2E_SIGNAL_PRREQ, SIGNAL_VALID);
    }
    else
    {
        PrintManager.EngineSendEC3 = tFALSE;
        //PRTDRV_EngineNotifySignal(C2E_SIGNAL_PRREQ, SIGNAL_VALID);
    }

    if (PrintManager.fedSheets == 0)
    {
        tUint8 status = STATUSSERV_RPT_Processing;

        if (JOBMGR_GetJobType(pPageScript->pPage->pParent) == JOBMGR_ReportJob)
            StatusServ_SetReportStatus(STATUSSERV_RptStatus, &status);
        else
            StatusServ_SetPrnStatusBit(STATUSSERV_PrintStatus, STATUSSERV_PRINT_BUSY);
    }

    PrintManager.fedSheets++;

    pPageScriptOrderList->fed = tTRUE;

    if (JOBMGR_GetPageCodec(pPageScript->pPage) == JOBMGR_PDL_DISPLAYLIST)
    {
#if CFG_NetManager
        extern void network_job_printing_report(int currentPage);
        network_job_printing_report(JOBMGR_GetPageId(pPageScript->pPage));
#endif
    }

#if PURE_VIRTUAL_MODE
    if (PrintManager.virtualMode)
    {
        char buf[msgENGERR_SIZE] = {0};

        buf[0] = 0xF5;
        buf[1] = CONSUMEPAGE;
        memcpy((void *)&buf[2], &pPageScript, sizeof(PM_ScriptDscp *));
        PRTDRV_VirtualSendCmd(buf, 4 + sizeof(pPageScript));
    }
#endif

    if (JOBMGR_GetPageColorSpace(pPageScript->pPage) != JOBMGR_Color && JOBMGR_GetPageColorSpace(pPageScript->pPage) != JOBMGR_Color2RGB)
    {
#if DUAL_BEAM
        PRTDRV_SetFirstDMAData(pPageScriptOrderList, colorMapping_PrtMgrToChannel[JOBMGR_Cyan]);
        PRTDRV_SetFirstDMAData(pPageScriptOrderList, colorMapping_PrtMgrToChannel[JOBMGR_Magenta]);
#else
        PRTDRV_SetFirstDMAData(pPageScriptOrderList, colorMapping_PrtMgrToChannel[JOBMGR_Black]);
#endif
    }
    else
    {
        PRTDRV_SetFirstDMAData(pPageScriptOrderList, colorMapping_PrtMgrToChannel[JOBMGR_Yellow]);
        PRTDRV_SetFirstDMAData(pPageScriptOrderList, colorMapping_PrtMgrToChannel[JOBMGR_Magenta]);
        PRTDRV_SetFirstDMAData(pPageScriptOrderList, colorMapping_PrtMgrToChannel[JOBMGR_Cyan]);
        PRTDRV_SetFirstDMAData(pPageScriptOrderList, colorMapping_PrtMgrToChannel[JOBMGR_Black]);
    }

    return pPageScript;
}

tBool PRTDRV_PrintStopedforTonerError(void)
{
    tBool          ret                            = tFALSE;
    PM_ScriptDscp *pPageScript                    = NULL;
    tUint8         tonerStatus[JOBMGR_MAXCOLORID] = {0, 0, 0, 0};

    pPageScript = PrintMgr_GetFirstPageScript();

    if (PrintManager.tonerBWMode)
    {
        if (pPageScript != NULL)
        {
            if (JOBMGR_GetPageColorSpace(pPageScript->pPage) == JOBMGR_Color) ret = tTRUE;
        }
    }
    else if (PrintManager.tonerError)
    {
        StatusServ_GetEngStatus(STATUSSERV_EngYTonerInf, &tonerStatus[JOBMGR_Yellow]);
        StatusServ_GetEngStatus(STATUSSERV_EngMTonerInf, &tonerStatus[JOBMGR_Magenta]);
        StatusServ_GetEngStatus(STATUSSERV_EngCTonerInf, &tonerStatus[JOBMGR_Cyan]);
        StatusServ_GetEngStatus(STATUSSERV_EngKTonerInf, &tonerStatus[JOBMGR_Black]);

        if (pPageScript != NULL
            && ((tonerStatus[JOBMGR_Yellow] | tonerStatus[JOBMGR_Magenta] | tonerStatus[JOBMGR_Cyan] | tonerStatus[JOBMGR_Black])
                & STATUSSERV_ENG_TONER_EMPTY))
            ret = tTRUE;
    }

    return ret;
}

/*
   Routine returns value means needing recover or not.
   If cartridge life end, set toner error status and return true.
   If any of toner crum error, set toner error status and return true.
   If K toner life end, any job cannot print, set toner error status and return true.
   Otherwise, othough Y,M,C is empty, but K is not end, BW mode is available, set warning status
   2013.7.7
   */
tBool PRTDRV_SetErrorStatusByToner(tVoid)
{
    tUint8 status = 0, tonerStatus[JOBMGR_MAXCOLORID] = {0, 0, 0, 0}, data8 = 0;
    tBool  ret = tFALSE, cartridgeError = tFALSE, tonerError[JOBMGR_MAXCOLORID] = {tFALSE};
    tUint8 tonerErrorBit = (STATUSSERV_ENG_TONER_CRUM_ID_WARNING | STATUSSERV_ENG_TONER_UNDEFINED | STATUSSERV_ENG_TONER_MISS);

    StatusServ_GetEngStatus(STATUSSERV_EngDrumInf, &status);
    StatusServ_GetEngStatus(STATUSSERV_EngYTonerInf, &tonerStatus[JOBMGR_Yellow]);
    StatusServ_GetEngStatus(STATUSSERV_EngMTonerInf, &tonerStatus[JOBMGR_Magenta]);
    StatusServ_GetEngStatus(STATUSSERV_EngCTonerInf, &tonerStatus[JOBMGR_Cyan]);
    StatusServ_GetEngStatus(STATUSSERV_EngKTonerInf, &tonerStatus[JOBMGR_Black]);

    if (status & STATUSSERV_ENG_DRUM_CARTRIDGE_LIFE_END) /*Xero life over*/
        cartridgeError = tTRUE;
    else if (status & STATUSSERV_ENG_DRUM_CARTRIDGE_PREPARE_REPLACEMENT)
    {
        SetServ_GetCurrentMachineSetting(SETSERV_MACHINE_MachineLife, &data8);
        if (data8 == 0) cartridgeError = tTRUE;
    }

    if (tonerStatus[JOBMGR_Black] & tonerErrorBit) tonerError[JOBMGR_Black] = tTRUE;

    if (tonerStatus[JOBMGR_Yellow] & tonerErrorBit) tonerError[JOBMGR_Yellow] = tTRUE;

    if (tonerStatus[JOBMGR_Magenta] & tonerErrorBit) tonerError[JOBMGR_Magenta] = tTRUE;

    if (tonerStatus[JOBMGR_Cyan] & tonerErrorBit) tonerError[JOBMGR_Cyan] = tTRUE;

    if (cartridgeError || tonerError[JOBMGR_Black] || tonerError[JOBMGR_Yellow] || tonerError[JOBMGR_Magenta] || tonerError[JOBMGR_Cyan]
        || (tonerStatus[JOBMGR_Black] & STATUSSERV_ENG_TONER_EMPTY))
    {
        //StatusServ_SetEngStatusBit(STATUSSERV_EngError, STATUSSERV_ENG_ERR_TONER);

        PrintManager.tonerError  = tTRUE;
        PrintManager.tonerBWMode = tFALSE;
        ret                      = tTRUE;
    }
    else if ((tonerStatus[JOBMGR_Yellow] & STATUSSERV_ENG_TONER_EMPTY) || (tonerStatus[JOBMGR_Magenta] & STATUSSERV_ENG_TONER_EMPTY)
             || (tonerStatus[JOBMGR_Cyan] & STATUSSERV_ENG_TONER_EMPTY))
    {
        //StatusServ_SetEngStatusBit(STATUSSERV_EngError, STATUSSERV_ENG_ERR_TONER);

        PrintManager.tonerError  = tFALSE;
        PrintManager.tonerBWMode = tTRUE;
        ret                      = tTRUE;
    }
    else
    {
        //StatusServ_ClrEngStatusBit(STATUSSERV_EngError, STATUSSERV_ENG_ERR_TONER);

        PrintManager.tonerError  = tFALSE;
        PrintManager.tonerBWMode = tFALSE;
        ret                      = tFALSE;
    }

    if (ret) PRTDRV_PRINTF("tmp: ret=%d\n", ret);

    return ret;
}
#define LOGSHOWMAX  50
int logshowcnt=LOGSHOWMAX;

eStatusServ_PrnStatus PRTDRV_GetError(void)
{
    tUint16 status = 0;
    //1: ready 0: Not ready
#define CheckEngREADY(status_tmp) \
    ((status_tmp & STATUSSERV_ENG_READY) == STATUSSERV_ENG_READY && (status_tmp & STATUSSERV_IREADY) == STATUSSERV_IREADY)
    //#define CheckEngREADY(status_tmp) ((status_tmp & STATUSSERV_ENG_READY) == STATUSSERV_ENG_READY)


    if (PrintManager.virtualMode)
    {
        //PRTDRV_PRINTF("virtualMode Error\n");
        return 0;
    }

    StatusServ_GetEngStatus(STATUSSERV_EngReady, &status);

    if (!CheckEngREADY(status))
    {
		logshowcnt++;
#if 1
        if ((status & STATUSSERV_ENG_READY) != STATUSSERV_ENG_READY)
        {
			if (logshowcnt>=LOGSHOWMAX)
			{
				logshowcnt=0;
               PRTDRV_PRINTF("ENG NOT READY status=%x\n",status);
			}
        }

        if ((status & STATUSSERV_IREADY) != STATUSSERV_IREADY)
        {
			if (logshowcnt>=LOGSHOWMAX)
			{
				logshowcnt=0;
               PRTDRV_PRINTF("IREADY NOT READY status=%x\n",status);
			}
        }
#endif
        return STATUSSERV_PRINT_FATAL_ERROR;
    }

    StatusServ_GetEngStatus(STATUSSERV_EngError, &status);
    if (status & STATUSSERV_ENG_ERROR)
    {
		logshowcnt++;
	    if (logshowcnt>=LOGSHOWMAX)
	    {
			logshowcnt=0;
            PRTDRV_PRINTF("STATUSSERV_ENG_ERROR status=%x\n",status);
		}
        return STATUSSERV_PRINT_FATAL_ERROR;
    }

    //    if (status & STATUSSERV_ENG_ERR_TONER)
    //        return STATUSSERV_PRINT_FATAL_ERROR;

    StatusServ_GetEngStatus(STATUSSERV_EngHWError, &status);
    if (status != 0)
    {
		logshowcnt++;
	    if (logshowcnt>=LOGSHOWMAX)
	    {
			logshowcnt=0;
		
           PRTDRV_PRINTF("EngHWError status=%x\n",status);
		}
        return STATUSSERV_PRINT_FATAL_ERROR;
    }

    StatusServ_GetEngStatus(STATUSSERV_EngTonerSensorError, &status);
    if (status != 0)
    {
		logshowcnt++;
	    if (logshowcnt>=LOGSHOWMAX)
	    {
			logshowcnt=0;
		
            PRTDRV_PRINTF("EngTonerSensorError status=%x\n",status);
		}
        return STATUSSERV_PRINT_FATAL_ERROR;
    }
#if CFG_Authorization
    StatusServ_GetSysErrStatus(STATUSSERV_MachinePOSTErr, &status);
    if (status == STATUSSERV_SYS_RAM_CHECK_FAIL)
    {
		logshowcnt++;
	    if (logshowcnt>=LOGSHOWMAX)
	    {
			logshowcnt=0;
		
            PRTDRV_PRINTF("MachinePOSTErr status=%x\n",status);
		}
        return STATUSSERV_PRINT_FATAL_ERROR;
    }
#endif

    StatusServ_GetEngStatus(STATUSSERV_EngCoverOpen, &status);

    if (status == STATUSSERV_ENG_SIDE1)
    {
		logshowcnt++;
	    if (logshowcnt>=LOGSHOWMAX)
	    {
			logshowcnt=0;
		
            PRTDRV_PRINTF("STATUSSERV_ENG_SIDE1 status=%x\n",status);
		}
        return STATUSSERV_PRINT_USER_RECOVERABLE_ERROR;
    }

    StatusServ_GetEngStatus(STATUSSERV_EngJam, &status);
    if (status != STATUSSERV_ENG_NO_JAM)
    {
		logshowcnt++;
	    if (logshowcnt>=LOGSHOWMAX)
	    {
			logshowcnt=0;
		
           PRTDRV_PRINTF("STATUSSERV_ENG_NO_JAM status=%x\n",status);
		}
        return STATUSSERV_PRINT_USER_RECOVERABLE_ERROR;
    }

    StatusServ_GetEngStatus(STATUSSERV_EngPaperEmpty, &status);

    if ((status & STATUSSERV_ENG_PAPER_EMPTY) != 0)
    {
		logshowcnt++;
	    if (logshowcnt>=LOGSHOWMAX)
	    {
			logshowcnt=0;
		
           PRTDRV_PRINTF("STATUSSERV_ENG_PAPER_EMPTY status=%x\n",status);
		}
        return STATUSSERV_PRINT_USER_RECOVERABLE_ERROR;
    }

    StatusServ_GetEngStatus(STATUSSERV_EngTrayExist, &status);

    if ((status & STATUSSERV_ENG_TRAY1) == 0)
    {
		logshowcnt++;
	    if (logshowcnt>=LOGSHOWMAX)
	    {
			logshowcnt=0;
            PRTDRV_PRINTF("STATUSSERV_EngTrayExist status=%x\n",status);
		}
        return STATUSSERV_PRINT_USER_RECOVERABLE_ERROR;
    }

    StatusServ_GetPrnStatus(STATUSSERV_PrintErr, &status);

    if (status & STATUSSERV_PRINT_SIZE_ERROR)
    {
		logshowcnt++;
	    if (logshowcnt>=LOGSHOWMAX)
	    {
			logshowcnt=0;
		
            PRTDRV_PRINTF("STATUSSERV_PRINT_SIZE_ERROR status=%x\n",status);
		}
        return STATUSSERV_PRINT_USER_RECOVERABLE_ERROR;
    }
    else if (status & STATUSSERV_PRINT_PIS_ERR)
    {
		logshowcnt++;
	    if (logshowcnt>=LOGSHOWMAX)
	    {
			logshowcnt=0;
		
            PRTDRV_PRINTF("STATUSSERV_PRINT_PIS_ERR status=%x\n",status);
		}
        return STATUSSERV_PRINT_USER_RECOVERABLE_ERROR;
    }

    StatusServ_GetPrnStatus(STATUSSERV_PDLPrintError, &status);
    if (status & STATUSSERV_PRINT_PDL_MEM_OVERFLOW)
    {
		logshowcnt++;
	    if (logshowcnt>=LOGSHOWMAX)
	    {
			logshowcnt=0;
		
            PRTDRV_PRINTF("STATUSSERV_PRINT_USER_RECOVERABLE_ERROR  status=%x\n",status);
		}
        return STATUSSERV_PRINT_USER_RECOVERABLE_ERROR;
    }

    StatusServ_GetEngStatus(STATUSSERV_EngError, &status);
    if (status & STATUSSERV_ENG_ERR_COOLDOWN)
    {
		logshowcnt++;
	    if (logshowcnt>=LOGSHOWMAX)
	    {
			logshowcnt=0;
		
		
            PRTDRV_PRINTF("STATUSSERV_ENG_ERR_COOLDOWN status=%x\n",status);
		}
        return STATUSSERV_PRINT_USER_RECOVERABLE_ERROR;
    }
    logshowcnt =0;
    return 0;
}
#if CFG_Authorization
void PRTDRV_NotifyDramCheckFail()
{
    stPrintMgrMsgBuf MSG = {0};
    MSG.mtype            = 1;
    MSG.msg.msgId        = PRT_DRAMCHECKFail;
    assert(msgsnd(msgQDriver, &MSG, sizeof(PRT_MSG), 0) == 0);
}
#endif

tBool PRTDRV_IOTError(DRV_MSGID errorMsgId, tUint32 para)
{
    tUint8         status = 0, i = 0;
    tBool          ret = tTRUE, SetInvaild = tTRUE, unrecoveralbeError = tFALSE;
    PM_ScriptDscp *pPageScript   = NULL;
    PM_JobScript  *pJobScript    = NULL;
    Uint8          jobAutoCancel = 0;
    switch (errorMsgId)
    {
        case PRT_BAND_ERROR:
            StatusServ_SetPrnStatusBit(STATUSSERV_PrintErr, STATUSSERV_PRINT_PIS_ERR);
            //StatusServ_SetPrnStatusBit(STATUSSERV_PDLPrintError, STATUSSERV_PRINT_PDL_MEM_OVERFLOW);
            SetInvaild         = tFALSE;
            unrecoveralbeError = tTRUE;

            break;
        case PRT_PFNA:
        case PRT_PTA:
            StatusServ_ClrPrnStatusBit(STATUSSERV_PrintStatus, STATUSSERV_PRINT_BUSY);

            if (PrintManager.fedSheets >= 1)
            {
                PrintManager.fedSheets--;
            }

            break;
        case PRT_JAM:
            StatusServ_ClrPrnStatusBit(STATUSSERV_PrintStatus, STATUSSERV_PRINT_BUSY);

            StatusServ_GetEngStatus(STATUSSERV_EngJam, &status);

            //if (status != STATUSSERV_ENG_JAM_MISFEED)
            PrintManager.fedSheets = 0;

            break;
        case PRT_COVEROPEN:
            StatusServ_ClrPrnStatusBit(STATUSSERV_PrintStatus, STATUSSERV_PRINT_BUSY);
            PrintManager.fedSheets = 0;

            break;
        case PRT_ENGINE_ERROR:
            StatusServ_ClrPrnStatusBit(STATUSSERV_PrintStatus, STATUSSERV_PRINT_BUSY);
            PrintManager.fedSheets = 0;
            unrecoveralbeError     = tTRUE;

            break;
        case PRT_DMA_ISR_ERROR:
            StatusServ_ClrPrnStatusBit(STATUSSERV_PrintStatus, STATUSSERV_PRINT_BUSY);
            PrintManager.fedSheets = 0;

            break;
        case PRT_SIZEERROR:
            StatusServ_GetPrnStatus(STATUSSERV_PrintErr, &status);
            status &= ~STATUSSERV_PRINT_MEDIA_SIZE_UNMATCH;
            status &= ~STATUSSERV_PRINT_MEDIA_TYPE_UNMATCH;
            status |= STATUSSERV_PRINT_SIZE_ERROR;
            StatusServ_SetPrnStatus(STATUSSERV_PrintErr, &status);
            StatusServ_GetPrnStatus(STATUSSERV_PrintStatus, &status);
            status &= ~STATUSSERV_PRINT_NEED_MANUAL_START;
            StatusServ_SetPrnStatus(STATUSSERV_PrintStatus, &status);

            StatusServ_ClrPrnStatusBit(STATUSSERV_PrintStatus, STATUSSERV_PRINT_BUSY);
            PrintManager.manualDuplexStop = tFALSE;

            pJobScript = PrintMgr_GetFirstJobScript();

            TAILQ_FOREACH(pPageScript, &pJobScript->psList, entry)
            {
                pPageScript->pPage->pParent->pPrt->manualStart = tFALSE;

                if (i++ > 3) break;
            }

            break;

        case PRT_PAPER_EMPTY:
            StatusServ_ClrPrnStatusBit(STATUSSERV_PrintStatus, STATUSSERV_PRINT_BUSY);
            break;

        case PRT_TONEREVENT:

            if (para == CMD_SR60) //waste toner box is full.
            {
                SetInvaild = tTRUE;
                break;
            }

            SetInvaild = tFALSE;

            break;

        default:
            SetInvaild = tFALSE;
            break;
    }

    if (SetInvaild == tTRUE)
    {
        //PRTDRV_PRINTF("tmp: set C2E_SIGNAL_PRREQ = SIGNAL_INVALID and clr EC3\n");
        PrintManager.EngineSendEC3 = tFALSE;
        //PRTDRV_EngineNotifySignal(C2E_SIGNAL_PRREQ, SIGNAL_INVALID);
    }

    if (ret && PrintMgr_GetFirstJob())
    {
        PrintManager.jobErrCnt++;
    }

    SetServ_GetCurrentPrnSetting(SETSERV_PRN_Layout, (void *)&jobAutoCancel);
#if defined(GM266DNS) || defined(GM268DNAS) || defined(G338DNS) || defined(GM339DNS) || defined(G263DNS)
    if (!jobAutoCancel)
    {
        //jobAutoCancel =1;
        //SetServ_SetCurrentPrnSetting(SETSERV_PRN_Layout, (void *)&jobAutoCancel);
        //PRTDRV_PRINTF("TCM type engine: Set jobAutoCancel= %d\n",jobAutoCancel);
        PRTDRV_PRINTF("TCM type engine: 1 Get jobAutoCancel= %d\n", jobAutoCancel);
    }
#endif
    if (jobAutoCancel) //if (unrecoveralbeError)
    {
        pJobScript = PrintMgr_GetFirstJobScript();
        if (unrecoveralbeError)
        {
            if (pJobScript && pJobScript->pJob && !(pJobScript->pJob->JobStatus & JOBMGR_JobDelete))
            {
                PRTDRV_PRINTF("DPipe: Fatal Error and job is cancelled %d\n", errorMsgId);
                JobMgr_CancelJob(pJobScript->pJob);
            }
        }
        else
        {
            if (pJobScript && pJobScript->pJob)
            {
                if (SetInvaild)
                {
                    PrintManager.recoveralbelTime = BIOSTimerMS();
                    PRTDRV_PRINTF("DPipe: 1 other Error and job wait timeout to cancel %d time=%d\n", errorMsgId, PrintManager.recoveralbelTime);
                }
            }
        }
    }
    else
    {
        PrintManager.recoveralbelTime = 0;
    }

    return ret;
}

PM_ScriptDscp *PRTDRV_DispatchPageScript()
{
    tUint8          status_ManualStart = 0, nsheet = 1;
    tBool           dispatch = tFALSE, deleteJob = tFALSE;
    PM_ScriptDscp  *pPageScript = NULL, *pPageScript_DuplexFront = NULL;
    PM_JobScript   *pJobScript = NULL;
    eJobMgr_JobType jobType = JOBMGR_JobTypeMax, jobTypebak = JOBMGR_JobTypeMax;
    tUint16         jobCopyQty = 1, dispatchedCnt = 0, fedCnt = 0, duplexTime = 1000;

    StatusServ_GetPrnStatus(STATUSSERV_PrintStatus, &status_ManualStart);

    if (status_ManualStart & STATUSSERV_PRINT_NEED_MANUAL_START)
    {
        return NULL;
    }

#if 1
    assert(semaphore_p(PrintManager.mutex_lock) == 1);
#else
    TSmutexWait(PrintManager.mutex_lock);
#endif

    TAILQ_FOREACH(pJobScript, &PrintManager.jobList, entry)
    {
        jobCopyQty = JOBMGR_GetJobCopyQty(pJobScript->pJob);
        jobTypebak = jobType;
        jobType    = JOBMGR_GetJobType(pJobScript->pJob);

        if ((jobTypebak == JOBMGR_CopyJob) && (jobType != JOBMGR_CopyJob))
        {
            break;
        }

        TAILQ_FOREACH(pPageScript, &pJobScript->psList, entry)
        {
            dispatchedCnt += pPageScript->dispatchedCnt - pPageScript->paperOutCnt;
            fedCnt += pPageScript->fedCnt - pPageScript->paperOutCnt;

            if (JOBMGR_IsJobDelete(pJobScript->pJob) && pPageScript->pPage->duplex != FRONT_SIDE_PAGE)
            {
                deleteJob = tTRUE;
                break;
            }

            //if (PrintManager.rawMode && pPageScript->readyBandCnt[JOBMGR_Black] < pPageScript->readyBandCntThreshold)
            //    break;

            //PRTDRV_PRINTF("tmp: dispatchedCnt=%d,pPageScript->dispatched=%d, Copies=%d\n",
            //    dispatchedCnt, pPageScript->dispatchedCnt, pPageScript->pPage->Copies);

            if (dispatchedCnt - fedCnt > 2)
            {
                break;
            }

            if (jobCopyQty > 1 && pPageScript->dispatchedCnt > pJobScript->collateJobDispatchedCnt) //collate job (e.g. copy)
            {
                continue;
            }

#if !PRINT_ALWAYS_TEST
            if (pPageScript->dispatchedCnt == pPageScript->pPage->Copies)
            {
                continue;
            }
#endif

#if DUAL_BEAM

            if (pPageScript->setFirstDMACnt[JOBMGR_Cyan] < pPageScript->dispatchedCnt) break;

#else

            if (pPageScript->setFirstDMACnt[JOBMGR_MAXCOLORID - 1] < pPageScript->dispatchedCnt)
            {
                break;
            }

#endif

            if (PrintManager.tonerBWMode && JOBMGR_GetPageColorSpace(pPageScript->pPage) == JOBMGR_Color
                && pPageScript->pPage->duplex != FRONT_SIDE_PAGE)
            {
                break;
            }

            if (pPageScript->dispatchedCnt == 0 && !PRTDRV_CheckSizeType(pPageScript))
            {
                break;
            }

            if (PrintManager.pPageScriptOrderList_Header && pPageScript != PrintManager.pPageScriptOrderList_Header->pPageScript)
            {
                eJobMgr_Resolution resolution1 = JOBMGR_GetPageInResolution(pPageScript->pPage);
                eJobMgr_Resolution resolution2 = JOBMGR_GetPageInResolution(PrintManager.pPageScriptOrderList_Header->pPageScript->pPage);
                if (resolution1 != resolution2)
                {
                    break;
                }
            }

            if (pPageScript->pPage->duplex == FRONT_SIDE_PAGE)
            {
                if (pPageScript_DuplexFront == NULL) pPageScript_DuplexFront = pPageScript; //Dispatch this front after enough back side dispatched

#if 1
                if (pPageScript->pPage->pNextPage == tNULL && JOBMGR_IsJobWriting(pJobScript->pJob) == tFALSE)
                {
                    PRTDRV_PRINTF("PRTDRV_DispatchPageScript: SmartDuplex(J%d-p%d) move to SIMPLEX\n", pJobScript->pJob->JobId,
                                  JOBMGR_GetPageId(pPageScript->pPage));
                    pPageScript->pPage->duplex = SIMPLEX_PAGE;
                }
                else
                {
                    if (pJobScript->dispatchDuplexSheetCnt < nsheet) //continue to searech back side
                    {
                        continue;
                    }
                }
#else
                if (pJobScript->dispatchDuplexSheetCnt < nsheet) //continue to searech back side
                    continue;
#endif
            }

            if (jobCopyQty > 1)
            {
                if (pPageScript->pPage->pNextPage == NULL && JOBMGR_IsJobWriting(pJobScript->pJob) == tFALSE) //update when last page
                    pJobScript->collateJobDispatchedCnt++;
            }

            dispatch = tTRUE;

            break;
        }

        if (dispatch == tFALSE && pPageScript == NULL && pJobScript->dispatchDuplexSheetCnt > 0 && pPageScript_DuplexFront != NULL
            && BIOSTimerMS() - pJobScript->dispatchDuplexBackSideTime >= duplexTime)
        {
            pPageScript = pPageScript_DuplexFront; //Dispatch the front
            dispatch    = tTRUE;
        }

        if (dispatch)
        {
            if (pPageScript->pPage->duplex == BACK_SIDE_PAGE)
            {
                pJobScript->dispatchDuplexSheetCnt++;
                pJobScript->dispatchDuplexBackSideTime = BIOSTimerMS();
            }
            else if (pPageScript->pPage->duplex == FRONT_SIDE_PAGE)
            {
                pJobScript->dispatchDuplexSheetCnt--;
                pJobScript->dispatchDuplexBackSideTime = 0;
            }

            if (pPageScript->pPage->duplex != SIMPLEX_PAGE)
            {
                if (!(JOBMGR_GetJobPrtFunc(pJobScript->pJob) & JOBMGR_DuplexPrt))
                {
                    JOBMGR_SetJobPrtFunc(pJobScript->pJob, JOBMGR_DuplexPrt);
                }
            }

            PIS_Dispatch(pPageScript);

#if !PURE_VIRTUAL_MODE
            PRTDRV_SendPageScriptToPushBand(pPageScript);
#endif
            break;
        }

        if (deleteJob)
        {
            break;
        }

        if (!PrintManager.jobContinuePrint)
        {
            break;
        }

        if (pPageScript != NULL) //This page of current job has not been dispatched, don't switch next job
        {
            break;
        }

        if (!pJobScript->endInputJob) //Current job is not finished, don't switch next job
        {
            break;
        }
    }

#if 1
    assert(semaphore_v(PrintManager.mutex_lock) == 1);
#else
    TSmutexSignal(PrintManager.mutex_lock);
#endif

    if (!dispatch) pPageScript = NULL;

    return pPageScript;
}

tVoid PRTDRV_AddPageScript(PM_ScriptDscp *pPageScript)
{
    PM_JobScript *pJobScript = NULL;

    assert(pPageScript);
#if 1
    assert(semaphore_p(PrintManager.mutex_lock) == 1);
#else
    TSmutexWait(PrintManager.mutex_lock);
#endif

    TAILQ_FOREACH(pJobScript, &PrintManager.jobList, entry)
    {
        if (pJobScript->pJob == pPageScript->pPage->pParent) break;
    }

    assert(pJobScript);

    if (TAILQ_EMPTY(&pJobScript->psList))
        TAILQ_INSERT_HEAD(&pJobScript->psList, pPageScript, entry);
    else
        TAILQ_INSERT_TAIL(&pJobScript->psList, pPageScript, entry);

#if 1
    assert(semaphore_v(PrintManager.mutex_lock) == 1);
#else
    TSmutexSignal(PrintManager.mutex_lock);
#endif
}

void PRTDRV_NewJob(stJobMgr_JobDscp *pJob)
{
    assert(pJob);
    assert(pJob->pPrt);
    PRTDRV_PRINTF("DPipe: NewJob jobId = %d, jobType = %d\n", JOBMGR_GetJobId(pJob), JOBMGR_GetJobType(pJob));

#if 1
    assert(semaphore_p(PrintManager.mutex_lock) == 1);
#else
    TSmutexWait(PrintManager.mutex_lock);
#endif

    if (PrintMgr_GetFirstJob() == pJob)
    {
        PRTDRV_SetNewJobStatus(pJob);
    }

#if 1
    assert(semaphore_v(PrintManager.mutex_lock) == 1);
#else
    TSmutexSignal(PrintManager.mutex_lock);
#endif
}

tBool PRTDRV_DecodeBandCntisEnough(PM_ScriptDscp *pPageScript)
{
#if 1
    return tFALSE;
#else
    tBool        ret        = tFALSE;
    eJobMgr_CMYK colorId    = JOBMGR_Yellow;
    tUint16      dataLength = 0;

    if (pPageScript == NULL)
    {
        return tFALSE;
    }
    else
    {
        if (JOBMGR_GetPageCodec(pPageScript->pPage) == JOBMGR_PDL_DISPLAYLIST && PrintManager.rawModePDL)
        {
            if (pPageScript->readyBandCnt[JOBMGR_Black])
            {
                return tTRUE;
            }
            else
            {
                return tFALSE;
            }
        }
#if 0	
    	else
    	{    
				return tTRUE;	  
    	}
#endif
    }

    if (JOBMGR_GetPageColorSpace(pPageScript->pPage) == JOBMGR_Color)
    {
        colorId = JOBMGR_Yellow;
    }
    else
    {
        colorId = JOBMGR_Black;
    }
    dataLength = pPageScript->dataLength;
#if DUAL_BEAM
    colorId    = JOBMGR_Cyan;
    dataLength = dataLength / 2;
#endif

    if (JOBMGR_GetPageCodec(pPageScript->pPage) == JOBMGR_RAW)
        ret = tTRUE;
    else
    {
        {
            //dataLength = pPageScript->dataLength;
            if (dataLength % PIS_STD_BANDLINES != 0)
            {
                if (pPageScript->readyBandCnt[colorId]
                    >= ((dataLength / PIS_STD_BANDLINES) + 1) * pPageScript->paperOutCnt + pPageScript->readyBandCntThreshold)
                    ret = tTRUE;
            }
            else
            {
                if (pPageScript->readyBandCnt[colorId]
                    >= (dataLength / PIS_STD_BANDLINES) * pPageScript->paperOutCnt + pPageScript->readyBandCntThreshold)
                    ret = tTRUE;
            }
#if 0    
						if (pPageScript->readyBandCnt[colorId] >= pPageScript->readyBandCntThreshold)
						        ret = tTRUE;
#endif
        }
    }

    return ret;
#endif
}

/*
   When to config DMA:
   At DMA Done: If current colorId is ready and current DMA is idle;
   At Feeding: for 4 colors, if whose DMA is idle.
   */
void PRTDRV_PageDMADone(PM_ScriptDscp *pPageScript, tUint8 dmaCh)
{
    PM_ScriptDscp          *pPageScriptbak       = NULL;
    PM_PageScriptOrderList *pPageScriptOrderList = NULL;
    eJobMgr_CMYK            colorId              = colorMapping_ChannelToPrtMgr[dmaCh];

    assert(pPageScript->pPage);

#if 1
    if (pPageScript->drvSizeError) return;
#else
    if (pPageScript->drvSizeError && JOBMGR_GetJobIgnorePaperErr(JOBMGR_GetPageParent(pPageScript->pPage)) != JOBMGR_ShowSizeError_Off) return;
#endif

    pPageScriptOrderList = PRTDRV_GetNextPageScriptInOrderList(PAGE_PLANEDONE, dmaCh);
    assert(pPageScriptOrderList);
    pPageScriptOrderList->planeDone[colorId] = tTRUE;
#if DUAL_BEAM
    if (dmaCh == PRT_DMADONE_0)
#endif
    {
        pPageScriptbak = pPageScriptOrderList->pPageScript;
        PRTDRV_TryToRemovePageScriptFromOrderList(pPageScriptOrderList);
    }

    if (pPageScript->pPage->duplex != BACK_SIDE_PAGE)
    {
#if DUAL_BEAM
        if (dmaCh == PRT_DMADONE_0)
#endif
            PRTDRV_TryToRemovePageScript(pPageScriptbak, tFALSE);
    }

    pPageScriptOrderList = PRTDRV_GetNextPageScriptInOrderList(PAGE_CFGDMA, dmaCh); //TAILQ_NEXT(pPageScript, entry);

    if (pPageScriptOrderList)
    {
        PRTDRV_SetFirstDMAData(pPageScriptOrderList, dmaCh);
    }
}

void PRTDRV_FlashPrintMeter(PM_ScriptDscp *pPageScript)
{
    eJobMgr_JobType    jobType    = JOBMGR_PrintJob;
    eJobMgr_ColorSpace colorSpace = JOBMGR_Color;
    tUint16            repId      = 0;
    eJobMgr_PaperSize  paperSize  = JOBMGR_A4L;
    eJobMgr_MediaType  paperType  = JOBMGR_Plain;
    stJobMgr_JobDscp  *pJob_Tmp   = NULL;
    DRV_ProcessMsg     processMsg = {0};
    tBool              writeFlash = tFALSE;
    tUint8             duplex     = 0;

    pJob_Tmp = JOBMGR_GetPageParent(pPageScript->pPage);

    processMsg.para1 = jobType = JOBMGR_GetJobType(pJob_Tmp);
    processMsg.para2 = colorSpace = JOBMGR_GetPageColorSpace(pPageScript->pPage);
    processMsg.para3 = repId = JOBMGR_GetJobRepId(pJob_Tmp);
    processMsg.para4 = paperSize = JOBMGR_GetPagePaperSize(pPageScript->pPage);
    processMsg.para5 = paperType = (eJobMgr_MediaType)JOBMGR_GetPageMediaType(pPageScript->pPage);
    processMsg.para6 = duplex = pPageScript->pPage->duplex;
    processMsg.msgId          = PRTDRV_RECORDPAGE;

    if (!(JOBMGR_GetJobPrtFunc(pJob_Tmp) & JOBMGR_ManualDuplex)
        || ((JOBMGR_GetJobPrtFunc(pJob_Tmp) & JOBMGR_ManualDuplex) && ((pJob_Tmp->jobTotalPage % 2) == 0))
        || ((JOBMGR_GetJobPrtFunc(pJob_Tmp) & JOBMGR_ManualDuplex)
            && (((JOBMGR_GetPageId(pPageScript->pPage) % (pJob_Tmp->jobTotalPage + 1)) > ((pJob_Tmp->jobTotalPage + 1) / 2))
                || ((JOBMGR_GetPageId(pPageScript->pPage) % (pJob_Tmp->jobTotalPage + 1)) == 0))))
    {
        writeFlash = tTRUE;
    }
    else
    {
        if (pJob_Tmp->jobTotalPage > 1)
        {
            if (!((JOBMGR_GetPageId(pPageScript->pPage) % ((pJob_Tmp->jobTotalPage + 1) / 2)) == 1))
            {
                writeFlash = tTRUE;
            }
        }
    }

    if (writeFlash)
    {
        //Osif_MsgQSend(msgQServant, (tInt32*)&processMsg, sizeof(DRV_ProcessMsg), OSIF_Suspend);
        PrtMgr_FlashPrintMeter(jobType, colorSpace, repId, paperSize, paperType, pPageScript->pPage->duplex);
    }
}

void PRTDRV_PaperOut(tUint32 sheet)
{
    PM_JobScript           *pJobScript  = NULL;
    PM_ScriptDscp          *pPageScript = NULL, *pPageScript_BackSide = NULL;
    PM_PageScriptOrderList *pPageScriptOrderList = NULL;

    if (PrintManager.fedSheets >= 1)
    {
        PrintManager.fedSheets--;
    }
    else
    {
        PRTDRV_PRINTF("DPipe: paper pout but fed Sheets=%d\n", PrintManager.fedSheets);
        if (PrintManager.EngineSendEC3)
        {
            PrintManager.EngineSendEC3 = tFALSE;
        }

        return;
    }

    pPageScriptOrderList = PRTDRV_GetNextPageScriptInOrderList(PAGE_PPOUT, 0);
    pPageScript          = pPageScriptOrderList->pPageScript;
    assert(pPageScript);

    pJobScript = PrintMgr_GetJobScript(pPageScript->pPage->pParent);
#if 1

    if (pPageScript->drvSizeError || pPageScript->pPage->pParent->pPrt->sizeError)
    {
        pPageScript->sizeErrorPaperOut = tTRUE;
        return;
    }

#else
    if ((pPageScript->drvSizeError || pPageScript->pPage->pParent->pPrt->sizeError)
        && JOBMGR_GetJobIgnorePaperErr(JOBMGR_GetPageParent(pPageScript->pPage)) != JOBMGR_ShowSizeError_Off)
    {
        pPageScript->sizeErrorPaperOut = tTRUE;
        return;
    }
#endif

    pPageScriptOrderList->paperOut = tTRUE;
    PRTDRV_TryToRemovePageScriptFromOrderList(pPageScriptOrderList);

    if (PrintManager.firstPaperOutTime == 0)
        PrintManager.currentPaperOutTime = PrintManager.firstPaperOutTime = BIOSTimerMS();
    else
        PrintManager.currentPaperOutTime = BIOSTimerMS();

    if (pPageScript->pPage->duplex != BACK_SIDE_PAGE)
    {
        PRTDRV_FlashPrintMeter(pPageScript);
        pPageScript->paperOutCnt++;
        PrintManager.paperOutCnt++;
        pJobScript->totalPageCnt++;

        if (pPageScript->pPage->duplex == FRONT_SIDE_PAGE)
        {
            pPageScript_BackSide = TAILQ_NEXT(pPageScript, entry);
            PRTDRV_FlashPrintMeter(pPageScript_BackSide);
            pPageScript_BackSide->paperOutCnt++;
            PrintManager.paperOutCnt++;
            pJobScript->totalPageCnt++;
        }

        PRTDRV_TryToRemovePageScript(pPageScript, tFALSE);
        StatusServ_SetPrnStatus(STATUSSERV_PrintCount, &pJobScript->totalPageCnt);
    }
}

#if (TONER_WITH_CHIP)
tStatus Engine_TonerStatus_Reset()
{
    return tOK;
}
void TonerStatus_Reset(void) {}
#else
tStatus Engine_TonerStatus_Reset()
{
    stDrvMsgDualCommBuf MSG_DualComm = {0};

    Uint8 TonerReset = 0;

    SetServ_GetCurrentMachineSetting(SETSERV_MACHINE_TonerResetFlag, (void *)&TonerReset);
    if (TonerReset == 0)
    {
        TonerReset = 1;
        SetServ_SetCurrentMachineSetting(SETSERV_MACHINE_TonerResetFlag, (void *)&TonerReset);
    }

    MSG_DualComm.mtype     = 1;
    MSG_DualComm.msg.msgId = PRTCOMM_RESETTONER;
    assert(msgsnd(msgQEngDualComm, &MSG_DualComm, sizeof(DRV_DualCommMsg), 0) == 0);

    return tOK;
}
void TonerStatus_Reset(void)
{
    stDrvMsgCommRespBuf commRespMSG = {0};
    PRTDRV_PRINTF("Toner reset...\n");

    while (1)
    {
        prtdrv_M3doCommand(ENGCMD_TONERSTATUS_RESET, NULL, NULL);
        msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_RESETTONER, 0);

        if ((commRespMSG.mBuf[0] >> 8) & 0xFF)
        {
            Uint8 TonerReset = 0;
            sFLASHSRV_TonerInfo NewToner;
            SetServ_SetCurrentMachineSetting(SETSERV_MACHINE_TonerResetFlag, (void *)&TonerReset);
            par_UpdateTonerInfo_IncInstallationInHistory(NewToner);
            PRTDRV_PRINTF("Toner reset successful...\n");
            break;
        }
        else
        {
            TASKSLEEP_MILLISECONDS(500);
        }
    }

}

tStatus LShell_Toner_Reset(void)
{
    stDrvMsgCommRespBuf commRespMSG = {0};
    PRTDRV_PRINTF("Toner reset.\n");
    prtdrv_M3doCommand(ENGCMD_TONERSTATUS_RESET, NULL, NULL);
    msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_RESETTONER, 0);

    if ((commRespMSG.mBuf[0] >> 8) & 0xFF)
    {
        sFLASHSRV_TonerInfo NewToner;
        par_UpdateTonerInfo_IncInstallationInHistory(NewToner);
        PRTDRV_PRINTF("Toner reset successful.\n");
        return tOK;
    }
    else
    {
        Uint8 ErrCount = 0;
        PowerSave_EngWakeup_And_Normalmode();
        TASKSLEEP_MILLISECONDS(500);
        while (ErrCount < 30)
        {
            prtdrv_M3doCommand(ENGCMD_TONERSTATUS_RESET, NULL, NULL);
            msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_RESETTONER, 0);

            if ((commRespMSG.mBuf[0] >> 8) & 0xFF)
            {
                sFLASHSRV_TonerInfo NewToner;
                par_UpdateTonerInfo_IncInstallationInHistory(NewToner);
                break;
            }
            else
            {
                ErrCount++;
                TASKSLEEP_MILLISECONDS(500);
            }
        }

        if (ErrCount < 30)
        {
            PRTDRV_PRINTF("Toner reset successful.\n");
            return tOK;
        }
        else
        {
            PRTDRV_PRINTF("Toner reset Failed.\n");
            return tERROR;
        }
    }

}
#endif

#if (DRUM_WITH_CHIP)
tStatus Engine_OPC_Reset()
{
    return tOK;
}
void OPC_Reset(void) {}
#else
tStatus Engine_OPC_Reset()
{
    stDrvMsgDualCommBuf MSG_DualComm = {0};

    Uint8 DrumReset = 0;

    SetServ_GetCurrentMachineSetting(SETSERV_MACHINE_DrumResetFlag, (void *)&DrumReset);
    if (DrumReset == 0)
    {
        DrumReset = 1;
        SetServ_SetCurrentMachineSetting(SETSERV_MACHINE_DrumResetFlag, (void *)&DrumReset);
    }

    MSG_DualComm.mtype     = 1;
    MSG_DualComm.msg.msgId = PRTCOMM_RESETDRUM;
    assert(msgsnd(msgQEngDualComm, &MSG_DualComm, sizeof(DRV_DualCommMsg), 0) == 0);

    return tOK;
}
void OPC_Reset(void)
{
    stDrvMsgCommRespBuf commRespMSG = {0};
    PRTDRV_PRINTF("OPC reset...\n");
    while (1)
    {
        prtdrv_M3doCommand(ENGCMD_OPC_RESET, NULL, NULL);
        msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_RESETOPC, 0);

        if ((commRespMSG.mBuf[0] >> 8) & 0xFF)
        {
            Uint8 DrumReset = 0;
            sFLASHSRV_TonerInfo NewDrum;

            SetServ_SetCurrentMachineSetting(SETSERV_MACHINE_DrumResetFlag, (void *)&DrumReset);
            par_UpdateDrumInfo_IncInstallationInHistory(NewDrum);
            PRTDRV_PRINTF("OPC reset successful...\n");
            break;
        }
        else
        {
            TASKSLEEP_MILLISECONDS(500);
        }
    }
}

tStatus LShell_OPC_Reset(void)
{
    stDrvMsgCommRespBuf commRespMSG = {0};
    PRTDRV_PRINTF("OPC reset.\n");

    prtdrv_M3doCommand(ENGCMD_OPC_RESET, NULL, NULL);
    msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_RESETOPC, 0);

    if ((commRespMSG.mBuf[0] >> 8) & 0xFF)
    {
        sFLASHSRV_TonerInfo NewDrum;
        par_UpdateDrumInfo_IncInstallationInHistory(NewDrum);
        PRTDRV_PRINTF("OPC reset successful.\n");
        return tOK;
    }
    else
    {
        Uint8 ErrCount = 0;
        PowerSave_EngWakeup_And_Normalmode();
        TASKSLEEP_MILLISECONDS(500);
        while (ErrCount < 30)
        {
            prtdrv_M3doCommand(ENGCMD_OPC_RESET, NULL, NULL);
            msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_RESETOPC, 0);

            if ((commRespMSG.mBuf[0] >> 8) & 0xFF)
            {
                sFLASHSRV_TonerInfo NewDrum;
                par_UpdateDrumInfo_IncInstallationInHistory(NewDrum);
                break;
            }
            else
            {
                ErrCount++;
                TASKSLEEP_MILLISECONDS(500);
            }
        }

        if (ErrCount < 30)
        {
            PRTDRV_PRINTF("OPC reset successful.\n");
            return tOK;
        }
        else
        {
            PRTDRV_PRINTF("OPC reset Failed.\n");
            return tERROR;
        }
    }
}
#endif

tStatus LShell_GetLeadingEdgeRegPlain(void *pData)
{
    stDrvMsgCommRespBuf commRespMSG = {0};

    prtdrv_M3doCommand(ENGCMD_GetLeadingEdgeRegPlain, NULL, NULL);
    msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_GetLeadingEdgeRegPlain, 0);

    if ((commRespMSG.mBuf[0] >> 8) & 0xFF)
    {
        PRTDRV_PRINTF("Get LeadingEdgeRegPlain Sucessful.\n");
        *(Sint8 *)pData = ((commRespMSG.mBuf[0] >> 16) & 0xFF);
        return tOK;
    }
    else
    {
        Uint8 ErrCount = 0;
        PowerSave_EngWakeup_And_Normalmode();
        TASKSLEEP_MILLISECONDS(500);
        while (ErrCount < 30)
        {
            prtdrv_M3doCommand(ENGCMD_GetLeadingEdgeRegPlain, NULL, NULL);
            msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_GetLeadingEdgeRegPlain, 0);

            if ((commRespMSG.mBuf[0] >> 8) & 0xFF)
            {
                *(Sint8 *)pData = ((commRespMSG.mBuf[0] >> 16) & 0xFF);
                break;
            }
            else
            {
                ErrCount++;
                TASKSLEEP_MILLISECONDS(500);
            }
        }

        if (ErrCount < 30)
        {
            PRTDRV_PRINTF("Get LeadingEdgeRegPlain Sucessful\n");
            return tOK;
        }
        else
        {
            PRTDRV_PRINTF("Get LeadingEdgeRegPlain Failed\n");
            return tERROR;
        }
    }

}

tStatus LShell_GetLeadingEdgeRegThick(void *pData)
{
    stDrvMsgCommRespBuf commRespMSG = {0};
    prtdrv_M3doCommand(ENGCMD_GetLeadingEdgeRegThick, NULL, NULL);
    msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_GetLeadingEdgeRegThick, 0);

    if ((commRespMSG.mBuf[0] >> 8) & 0xFF)
    {
        PRTDRV_PRINTF("Get LeadingEdgeRegThick Sucessful.\n");
        *(Sint8 *)pData = ((commRespMSG.mBuf[0] >> 16) & 0xFF);
        return tOK;
    }
    else
    {
        Uint8 ErrCount = 0;
        PowerSave_EngWakeup_And_Normalmode();
        TASKSLEEP_MILLISECONDS(500);
        while (ErrCount < 30)
        {
            prtdrv_M3doCommand(ENGCMD_GetLeadingEdgeRegThick, NULL, NULL);
            msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_GetLeadingEdgeRegThick, 0);

            if ((commRespMSG.mBuf[0] >> 8) & 0xFF)
            {
                *(Sint8 *)pData = ((commRespMSG.mBuf[0] >> 16) & 0xFF);
                break;
            }
            else
            {
                ErrCount++;
                TASKSLEEP_MILLISECONDS(500);
            }
        }

        if (ErrCount < 30)
        {
            PRTDRV_PRINTF("Get LeadingEdgeRegThick Sucessful\n");
            return tOK;
        }
        else
        {
            PRTDRV_PRINTF("Get LeadingEdgeRegThick Failed\n");
            return tERROR;
        }
    }
}

tStatus LShell_GetLeadingEdgeRegThin(void *pData)
{
    stDrvMsgCommRespBuf commRespMSG = {0};

    prtdrv_M3doCommand(ENGCMD_GetLeadingEdgeRegThin, NULL, NULL);
    msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_GetLeadingEdgeRegThin, 0);

    if ((commRespMSG.mBuf[0] >> 8) & 0xFF)
    {
        PRTDRV_PRINTF("Get LeadingEdgeRegThin Sucessful.\n");
        *(Sint8 *)pData = ((commRespMSG.mBuf[0] >> 16) & 0xFF);
        return tOK;
    }
    else
    {
        Uint8 ErrCount = 0;
        PowerSave_EngWakeup_And_Normalmode();
        TASKSLEEP_MILLISECONDS(500);
        while (ErrCount < 30)
        {
            prtdrv_M3doCommand(ENGCMD_GetLeadingEdgeRegThin, NULL, NULL);
            msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_GetLeadingEdgeRegThin, 0);

            if ((commRespMSG.mBuf[0] >> 8) & 0xFF)
            {
                *(Sint8 *)pData = ((commRespMSG.mBuf[0] >> 16) & 0xFF);
                break;
            }
            else
            {
                ErrCount++;
                TASKSLEEP_MILLISECONDS(500);
            }
        }

        if (ErrCount < 30)
        {
            PRTDRV_PRINTF("Get LeadingEdgeRegThin Sucessful\n");
            return tOK;
        }
        else
        {
            PRTDRV_PRINTF("Get LeadingEdgeRegThin Failed\n");
            return tERROR;
        }
    }
}

tStatus LShell_SetLeadingEdgeRegPlain(void *pData)
{
    stDrvMsgCommRespBuf commRespMSG = {0};

    prtdrv_M3doCommand(ENGCMD_SetLeadingEdgeRegPlain, pData, NULL);
    msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_SetLeadingEdgeRegPlain, 0);

    if ((commRespMSG.mBuf[0] >> 8) & 0xFF)
    {
        PRTDRV_PRINTF("Set LeadingEdgeRegPlain Sucessful.\n");
        return tOK;
    }
    else
    {
        Uint8 ErrCount = 0;
        PowerSave_EngWakeup_And_Normalmode();
        TASKSLEEP_MILLISECONDS(500);
        while (ErrCount < 30)
        {
            prtdrv_M3doCommand(ENGCMD_SetLeadingEdgeRegPlain, pData, NULL);
            msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_SetLeadingEdgeRegPlain, 0);

            if ((commRespMSG.mBuf[0] >> 8) & 0xFF)
            {
                break;
            }
            else
            {
                ErrCount++;
                TASKSLEEP_MILLISECONDS(500);
            }
        }

        if (ErrCount < 30)
        {
            PRTDRV_PRINTF("Set LeadingEdgeRegPlain Sucessful\n");
            return tOK;
        }
        else
        {
            PRTDRV_PRINTF("Set LeadingEdgeRegPlain Failed\n");
            return tERROR;
        }
    }
}

tStatus LShell_SetLeadingEdgeRegThick(void *pData)
{
    stDrvMsgCommRespBuf commRespMSG = {0};

    prtdrv_M3doCommand(ENGCMD_SetLeadingEdgeRegThick, pData, NULL);
    msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_SetLeadingEdgeRegThick, 0);

    if ((commRespMSG.mBuf[0] >> 8) & 0xFF)
    {
        PRTDRV_PRINTF("Set LeadingEdgeRegThick Sucessful.\n");
        return tOK;
    }
    else
    {
        Uint8 ErrCount = 0;
        PowerSave_EngWakeup_And_Normalmode();
        TASKSLEEP_MILLISECONDS(500);
        while (ErrCount < 30)
        {
            prtdrv_M3doCommand(ENGCMD_SetLeadingEdgeRegThick, pData, NULL);
            msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_SetLeadingEdgeRegThick, 0);

            if ((commRespMSG.mBuf[0] >> 8) & 0xFF)
            {
                break;
            }
            else
            {
                ErrCount++;
                TASKSLEEP_MILLISECONDS(500);
            }
        }

        if (ErrCount < 30)
        {
            PRTDRV_PRINTF("Set LeadingEdgeRegThick Sucessful\n");
            return tOK;
        }
        else
        {
            PRTDRV_PRINTF("Set LeadingEdgeRegThick Failed\n");
            return tERROR;
        }
    }
}

tStatus LShell_SetLeadingEdgeRegThin(void *pData)
{
    stDrvMsgCommRespBuf commRespMSG = {0};

    prtdrv_M3doCommand(ENGCMD_SetLeadingEdgeRegThin, pData, NULL);
    msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_SetLeadingEdgeRegThin, 0);

    if ((commRespMSG.mBuf[0] >> 8) & 0xFF)
    {
        PRTDRV_PRINTF("Set LeadingEdgeRegThin Sucessful.\n");
        return tOK;
    }
    else
    {
        Uint8 ErrCount = 0;
        PowerSave_EngWakeup_And_Normalmode();
        TASKSLEEP_MILLISECONDS(500);
        while (ErrCount < 30)
        {
            prtdrv_M3doCommand(ENGCMD_SetLeadingEdgeRegThin, pData, NULL);
            msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_SetLeadingEdgeRegThin, 0);

            if ((commRespMSG.mBuf[0] >> 8) & 0xFF)
            {
                break;
            }
            else
            {
                ErrCount++;
                TASKSLEEP_MILLISECONDS(500);
            }
        }

        if (ErrCount < 30)
        {
            PRTDRV_PRINTF("Set LeadingEdgeRegThin Sucessful\n");
            return tOK;
        }
        else
        {
            PRTDRV_PRINTF("Set LeadingEdgeRegThin Failed\n");
            return tERROR;
        }
    }
}

tStatus LShell_GetFusingTemperPlain(void *pData)
{
    stDrvMsgCommRespBuf commRespMSG = {0};

    prtdrv_M3doCommand(ENGCMD_GetFusingTemperPlain, NULL, NULL);
    msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_GetFusingTemperPlain, 0);

    if ((commRespMSG.mBuf[0] >> 8) & 0xFF)
    {
        PRTDRV_PRINTF("Get FusingTemperPlain Sucessful.\n");
        *(Uint8 *)pData = ((commRespMSG.mBuf[0] >> 16) & 0xFF);
        return tOK;
    }
    else
    {
        Uint8 ErrCount = 0;
        PowerSave_EngWakeup_And_Normalmode();
        TASKSLEEP_MILLISECONDS(500);
        while (ErrCount < 30)
        {
            prtdrv_M3doCommand(ENGCMD_GetFusingTemperPlain, NULL, NULL);
            msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_GetFusingTemperPlain, 0);

            if ((commRespMSG.mBuf[0] >> 8) & 0xFF)
            {
                *(Uint8 *)pData = ((commRespMSG.mBuf[0] >> 16) & 0xFF);
                break;
            }
            else
            {
                ErrCount++;
                TASKSLEEP_MILLISECONDS(500);
            }
        }

        if (ErrCount < 30)
        {
            PRTDRV_PRINTF("Get FusingTemperPlain Sucessful\n");
            return tOK;
        }
        else
        {
            PRTDRV_PRINTF("Get FusingTemperPlain Failed\n");
            return tERROR;
        }
    }
}

tStatus LShell_GetFusingTemperThick(void *pData)
{
    stDrvMsgCommRespBuf commRespMSG = {0};

    prtdrv_M3doCommand(ENGCMD_GetFusingTemperThick, NULL, NULL);
    msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_GetFusingTemperThick, 0);

    if ((commRespMSG.mBuf[0] >> 8) & 0xFF)
    {
        PRTDRV_PRINTF("Get FusingTemperThick Sucessful.\n");
        *(Uint8 *)pData = ((commRespMSG.mBuf[0] >> 16) & 0xFF);
        return tOK;
    }
    else
    {
        Uint8 ErrCount = 0;
        PowerSave_EngWakeup_And_Normalmode();
        TASKSLEEP_MILLISECONDS(500);
        while (ErrCount < 30)
        {
            prtdrv_M3doCommand(ENGCMD_GetFusingTemperThick, NULL, NULL);
            msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_GetFusingTemperThick, 0);

            if ((commRespMSG.mBuf[0] >> 8) & 0xFF)
            {
                *(Uint8 *)pData = ((commRespMSG.mBuf[0] >> 16) & 0xFF);
                break;
            }
            else
            {
                ErrCount++;
                TASKSLEEP_MILLISECONDS(500);
            }
        }

        if (ErrCount < 30)
        {
            PRTDRV_PRINTF("Get FusingTemperThick Sucessful\n");
            return tOK;
        }
        else
        {
            PRTDRV_PRINTF("Get FusingTemperThick Failed\n");
            return tERROR;
        }
    }
}

tStatus LShell_GetFusingTemperThin(void *pData)
{
    stDrvMsgCommRespBuf commRespMSG = {0};

    prtdrv_M3doCommand(ENGCMD_GetFusingTemperThin, NULL, NULL);
    msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_GetFusingTemperThin, 0);

    if ((commRespMSG.mBuf[0] >> 8) & 0xFF)
    {
        PRTDRV_PRINTF("Get FusingTemperThin Sucessful.\n");
        *(Uint8 *)pData = ((commRespMSG.mBuf[0] >> 16) & 0xFF);
        return tOK;
    }
    else
    {
        Uint8 ErrCount = 0;
        PowerSave_EngWakeup_And_Normalmode();
        TASKSLEEP_MILLISECONDS(500);
        while (ErrCount < 30)
        {
            prtdrv_M3doCommand(ENGCMD_GetFusingTemperThin, NULL, NULL);
            msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_GetFusingTemperThin, 0);

            if ((commRespMSG.mBuf[0] >> 8) & 0xFF)
            {
                *(Uint8 *)pData = ((commRespMSG.mBuf[0] >> 16) & 0xFF);
                break;
            }
            else
            {
                ErrCount++;
                TASKSLEEP_MILLISECONDS(500);
            }
        }

        if (ErrCount < 30)
        {
            PRTDRV_PRINTF("Get FusingTemperThin Sucessful\n");
            return tOK;
        }
        else
        {
            PRTDRV_PRINTF("Get FusingTemperThin Failed\n");
            return tERROR;
        }
    }
}

tStatus LShell_GetFusingTemperRecycled(void *pData)
{
    stDrvMsgCommRespBuf commRespMSG = {0};

    prtdrv_M3doCommand(ENGCMD_GetFusingTemperRecycled, NULL, NULL);
    msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_GetFusingTemperRecycled, 0);

    if ((commRespMSG.mBuf[0] >> 8) & 0xFF)
    {
        PRTDRV_PRINTF("Get FusingTemperRecycled Sucessful.\n");
        *(Uint8 *)pData = ((commRespMSG.mBuf[0] >> 16) & 0xFF);
        return tOK;
    }
    else
    {
        Uint8 ErrCount = 0;
        PowerSave_EngWakeup_And_Normalmode();
        TASKSLEEP_MILLISECONDS(500);
        while (ErrCount < 30)
        {
            prtdrv_M3doCommand(ENGCMD_GetFusingTemperRecycled, NULL, NULL);
            msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_GetFusingTemperRecycled, 0);

            if ((commRespMSG.mBuf[0] >> 8) & 0xFF)
            {
                *(Uint8 *)pData = ((commRespMSG.mBuf[0] >> 16) & 0xFF);
                break;
            }
            else
            {
                ErrCount++;
                TASKSLEEP_MILLISECONDS(500);
            }
        }

        if (ErrCount < 30)
        {
            PRTDRV_PRINTF("Get FusingTemperRecycled Sucessful\n");
            return tOK;
        }
        else
        {
            PRTDRV_PRINTF("Get FusingTemperRecycled Failed\n");
            return tERROR;
        }
    }
}

tStatus LShell_GetSC559Detection(void *pData)
{

    stDrvMsgCommRespBuf commRespMSG = {0};

    prtdrv_M3doCommand(ENGCMD_GetSC559Detection, NULL, NULL);
    msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_GetSC559Detection, 0);

    if ((commRespMSG.mBuf[0] >> 8) & 0xFF)
    {
        PRTDRV_PRINTF("Get SC559Detection Sucessful.\n");
        *(Uint8 *)pData = ((commRespMSG.mBuf[0] >> 16) & 0xFF);
        return tOK;
    }
    else
    {
        Uint8 ErrCount = 0;
        PowerSave_EngWakeup_And_Normalmode();
        TASKSLEEP_MILLISECONDS(500);
        while (ErrCount < 30)
        {
            prtdrv_M3doCommand(ENGCMD_GetSC559Detection, NULL, NULL);
            msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_GetSC559Detection, 0);

            if ((commRespMSG.mBuf[0] >> 8) & 0xFF)
            {
                *(Uint8 *)pData = ((commRespMSG.mBuf[0] >> 16) & 0xFF);
                break;
            }
            else
            {
                ErrCount++;
                TASKSLEEP_MILLISECONDS(500);
            }
        }

        if (ErrCount < 30)
        {
            PRTDRV_PRINTF("Get SC559Detection Sucessful\n");
            return tOK;
        }
        else
        {
            PRTDRV_PRINTF("Get SC559Detection Failed\n");
            return tERROR;
        }
    }
}

tStatus LShell_GetSubScanMag(void *pData)
{

    stDrvMsgCommRespBuf commRespMSG = {0};

    prtdrv_M3doCommand(ENGCMD_GetSubScanMag, NULL, NULL);
    msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_GetSubScanMag, 0);

    if ((commRespMSG.mBuf[0] >> 8) & 0xFF)
    {
        PRTDRV_PRINTF("Get SubScanMag Sucessful.\n");
        *(Sint8 *)pData = ((commRespMSG.mBuf[0] >> 16) & 0xFF);
        return tOK;
    }
    else
    {
        Uint8 ErrCount = 0;
        PowerSave_EngWakeup_And_Normalmode();
        TASKSLEEP_MILLISECONDS(500);
        while (ErrCount < 30)
        {
            prtdrv_M3doCommand(ENGCMD_GetSubScanMag, NULL, NULL);
            msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_GetSubScanMag, 0);

            if ((commRespMSG.mBuf[0] >> 8) & 0xFF)
            {
                *(Sint8 *)pData = ((commRespMSG.mBuf[0] >> 16) & 0xFF);
                break;
            }
            else
            {
                ErrCount++;
                TASKSLEEP_MILLISECONDS(500);
            }
        }

        if (ErrCount < 30)
        {
            PRTDRV_PRINTF("Get SubScanMag Sucessful\n");
            return tOK;
        }
        else
        {
            PRTDRV_PRINTF("Get SubScanMag Failed\n");
            return tERROR;
        }
    }
}

tStatus LShell_GetTransferRollerBias(void *pData)
{

    stDrvMsgCommRespBuf commRespMSG = {0};

    prtdrv_M3doCommand(ENGCMD_GetTransferRollerBias, NULL, NULL);
    msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_GetTransferRollerBias, 0);

    if ((commRespMSG.mBuf[0] >> 8) & 0xFF)
    {
        PRTDRV_PRINTF("Get TransferRollerBias Sucessful.\n");
        *(Sint8 *)pData = ((commRespMSG.mBuf[0] >> 16) & 0xFF);
        return tOK;
    }
    else
    {
        Uint8 ErrCount = 0;
        PowerSave_EngWakeup_And_Normalmode();
        TASKSLEEP_MILLISECONDS(500);
        while (ErrCount < 30)
        {
            prtdrv_M3doCommand(ENGCMD_GetTransferRollerBias, NULL, NULL);
            msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_GetTransferRollerBias, 0);

            if ((commRespMSG.mBuf[0] >> 8) & 0xFF)
            {
                *(Sint8 *)pData = ((commRespMSG.mBuf[0] >> 16) & 0xFF);
                break;
            }
            else
            {
                ErrCount++;
                TASKSLEEP_MILLISECONDS(500);
            }
        }

        if (ErrCount < 30)
        {
            PRTDRV_PRINTF("Get TransferRollerBias Sucessful\n");
            return tOK;
        }
        else
        {
            PRTDRV_PRINTF("Get TransferRollerBias Failed\n");
            return tERROR;
        }
    }
}

tStatus LShell_SetFusingTemperPlain(void *pData)
{

    stDrvMsgCommRespBuf commRespMSG = {0};

    prtdrv_M3doCommand(ENGCMD_SetFusingTemperPlain, pData, NULL);
    msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_SetFusingTemperPlain, 0);

    if ((commRespMSG.mBuf[0] >> 8) & 0xFF)
    {
        PRTDRV_PRINTF("Set FusingTemperPlain Sucessful.\n");
        return tOK;
    }
    else
    {
        Uint8 ErrCount = 0;
        PowerSave_EngWakeup_And_Normalmode();
        TASKSLEEP_MILLISECONDS(500);
        while (ErrCount < 30)
        {
            prtdrv_M3doCommand(ENGCMD_SetFusingTemperPlain, pData, NULL);
            msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_SetFusingTemperPlain, 0);

            if ((commRespMSG.mBuf[0] >> 8) & 0xFF)
            {
                break;
            }
            else
            {
                ErrCount++;
                TASKSLEEP_MILLISECONDS(500);
            }
        }

        if (ErrCount < 30)
        {
            PRTDRV_PRINTF("Set FusingTemperPlain Sucessful\n");
            return tOK;
        }
        else
        {
            PRTDRV_PRINTF("Set FusingTemperPlain Failed\n");
            return tERROR;
        }
    }
}

tStatus LShell_SetFusingTemperThick(void *pData)
{

    stDrvMsgCommRespBuf commRespMSG = {0};

    prtdrv_M3doCommand(ENGCMD_SetFusingTemperThick, pData, NULL);
    msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_SetFusingTemperThick, 0);

    if ((commRespMSG.mBuf[0] >> 8) & 0xFF)
    {
        PRTDRV_PRINTF("Set FusingTemperThick Sucessful.\n");
        return tOK;
    }
    else
    {
        Uint8 ErrCount = 0;
        PowerSave_EngWakeup_And_Normalmode();
        TASKSLEEP_MILLISECONDS(500);
        while (ErrCount < 30)
        {
            prtdrv_M3doCommand(ENGCMD_SetFusingTemperThick, pData, NULL);
            msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_SetFusingTemperThick, 0);

            if ((commRespMSG.mBuf[0] >> 8) & 0xFF)
            {
                break;
            }
            else
            {
                ErrCount++;
                TASKSLEEP_MILLISECONDS(500);
            }
        }

        if (ErrCount < 30)
        {
            PRTDRV_PRINTF("Set FusingTemperThick Sucessful\n");
            return tOK;
        }
        else
        {
            PRTDRV_PRINTF("Set FusingTemperThick Failed\n");
            return tERROR;
        }
    }
}

tStatus LShell_SetFusingTemperThin(void *pData)
{
    stDrvMsgCommRespBuf commRespMSG = {0};

    prtdrv_M3doCommand(ENGCMD_SetFusingTemperThin, pData, NULL);
    msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_SetFusingTemperThin, 0);

    if ((commRespMSG.mBuf[0] >> 8) & 0xFF)
    {
        PRTDRV_PRINTF("Set FusingTemperThin Sucessful.\n");
        return tOK;
    }
    else
    {
        Uint8 ErrCount = 0;
        PowerSave_EngWakeup_And_Normalmode();
        TASKSLEEP_MILLISECONDS(500);
        while (ErrCount < 30)
        {
            prtdrv_M3doCommand(ENGCMD_SetFusingTemperThin, pData, NULL);
            msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_SetFusingTemperThin, 0);

            if ((commRespMSG.mBuf[0] >> 8) & 0xFF)
            {
                break;
            }
            else
            {
                ErrCount++;
                TASKSLEEP_MILLISECONDS(500);
            }
        }

        if (ErrCount < 30)
        {
            PRTDRV_PRINTF("Set FusingTemperThin Sucessful\n");
            return tOK;
        }
        else
        {
            PRTDRV_PRINTF("Set FusingTemperThin Failed\n");
            return tERROR;
        }
    }
}

tStatus LShell_SetFusingTemperRecycled(void *pData)
{

    stDrvMsgCommRespBuf commRespMSG = {0};

    prtdrv_M3doCommand(ENGCMD_SetFusingTemperRecycled, pData, NULL);
    msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_SetFusingTemperRecycled, 0);

    if ((commRespMSG.mBuf[0] >> 8) & 0xFF)
    {
        PRTDRV_PRINTF("Set FusingTemperRecycled Sucessful.\n");
        return tOK;
    }
    else
    {
        Uint8 ErrCount = 0;
        PowerSave_EngWakeup_And_Normalmode();
        TASKSLEEP_MILLISECONDS(500);
        while (ErrCount < 30)
        {
            prtdrv_M3doCommand(ENGCMD_SetFusingTemperRecycled, pData, NULL);
            msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_SetFusingTemperRecycled, 0);

            if ((commRespMSG.mBuf[0] >> 8) & 0xFF)
            {
                break;
            }
            else
            {
                ErrCount++;
                TASKSLEEP_MILLISECONDS(500);
            }
        }

        if (ErrCount < 30)
        {
            PRTDRV_PRINTF("Set FusingTemperRecycled Sucessful\n");
            return tOK;
        }
        else
        {
            PRTDRV_PRINTF("Set FusingTemperRecycled Failed\n");
            return tERROR;
        }
    }
}

tStatus LShell_SetSC559Detection(void *pData)
{

    stDrvMsgCommRespBuf commRespMSG = {0};

    prtdrv_M3doCommand(ENGCMD_SetSC559Detection, pData, NULL);
    msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_SetSC559Detection, 0);

    if ((commRespMSG.mBuf[0] >> 8) & 0xFF)
    {
        PRTDRV_PRINTF("Set SC559Detection Sucessful.\n");
        return tOK;
    }
    else
    {
        Uint8 ErrCount = 0;
        PowerSave_EngWakeup_And_Normalmode();
        TASKSLEEP_MILLISECONDS(500);
        while (ErrCount < 30)
        {
            prtdrv_M3doCommand(ENGCMD_SetSC559Detection, pData, NULL);
            msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_SetSC559Detection, 0);

            if ((commRespMSG.mBuf[0] >> 8) & 0xFF)
            {
                break;
            }
            else
            {
                ErrCount++;
                TASKSLEEP_MILLISECONDS(500);
            }
        }

        if (ErrCount < 30)
        {
            PRTDRV_PRINTF("Set SC559Detection Sucessful\n");
            return tOK;
        }
        else
        {
            PRTDRV_PRINTF("Set SC559Detection Failed\n");
            return tERROR;
        }
    }
}

tStatus LShell_SetSubScanMag(void *pData)
{

    stDrvMsgCommRespBuf commRespMSG = {0};

    prtdrv_M3doCommand(ENGCMD_SetSubScanMag, pData, NULL);
    msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_SetSubScanMag, 0);

    if ((commRespMSG.mBuf[0] >> 8) & 0xFF)
    {
        PRTDRV_PRINTF("Set SubScanMag Sucessful.\n");
        return tOK;
    }
    else
    {
        Uint8 ErrCount = 0;
        PowerSave_EngWakeup_And_Normalmode();
        TASKSLEEP_MILLISECONDS(500);
        while (ErrCount < 30)
        {
            prtdrv_M3doCommand(ENGCMD_SetSubScanMag, pData, NULL);
            msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_SetSubScanMag, 0);

            if ((commRespMSG.mBuf[0] >> 8) & 0xFF)
            {
                break;
            }
            else
            {
                ErrCount++;
                TASKSLEEP_MILLISECONDS(500);
            }
        }

        if (ErrCount < 30)
        {
            PRTDRV_PRINTF("Set SubScanMag Sucessful\n");
            return tOK;
        }
        else
        {
            PRTDRV_PRINTF("Set SubScanMag Failed\n");
            return tERROR;
        }
    }
}

tStatus LShell_SetTransferRollerBias(void *pData)
{

    stDrvMsgCommRespBuf commRespMSG = {0};

    prtdrv_M3doCommand(ENGCMD_SetTransferRollerBias, pData, NULL);
    msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_SetTransferRollerBias, 0);

    if ((commRespMSG.mBuf[0] >> 8) & 0xFF)
    {
        PRTDRV_PRINTF("Set TransferRollerBias Sucessful.\n");
        return tOK;
    }
    else
    {
        Uint8 ErrCount = 0;
        PowerSave_EngWakeup_And_Normalmode();
        TASKSLEEP_MILLISECONDS(500);
        while (ErrCount < 30)
        {
            prtdrv_M3doCommand(ENGCMD_SetTransferRollerBias, pData, NULL);
            msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_SetTransferRollerBias, 0);

            if ((commRespMSG.mBuf[0] >> 8) & 0xFF)
            {
                break;
            }
            else
            {
                ErrCount++;
                TASKSLEEP_MILLISECONDS(500);
            }
        }

        if (ErrCount < 30)
        {
            PRTDRV_PRINTF("Set TransferRollerBias Sucessful\n");
            return tOK;
        }
        else
        {
            PRTDRV_PRINTF("Set TransferRollerBias Failed\n");
            return tERROR;
        }
    }
}

tStatus LShell_SetDestinationCode(void *pData)
{

    stDrvMsgCommRespBuf commRespMSG = {0};

    prtdrv_M3doCommand(ENGCMD_SetDestinationCode, pData, NULL);
    msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_SetDestinationCode, 0);

    if ((commRespMSG.mBuf[0] >> 8) & 0xFF)
    {
        PRTDRV_PRINTF("Set DestinationCode Sucessful.\n");
        return tOK;
    }
    else
    {
        Uint8 ErrCount = 0;
        PowerSave_EngWakeup_And_Normalmode();
        TASKSLEEP_MILLISECONDS(500);
        while (ErrCount < 30)
        {
            prtdrv_M3doCommand(ENGCMD_SetDestinationCode, pData, NULL);
            msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_SetDestinationCode, 0);

            if ((commRespMSG.mBuf[0] >> 8) & 0xFF)
            {
                break;
            }
            else
            {
                ErrCount++;
                TASKSLEEP_MILLISECONDS(500);
            }
        }

        if (ErrCount < 30)
        {
            PRTDRV_PRINTF("Set DestinationCode Sucessful\n");
            return tOK;
        }
        else
        {
            PRTDRV_PRINTF("Set DestinationCode Failed\n");
            return tERROR;
        }
    }
}

tStatus LShell_GetLeadingEdgeRegForUser(void *pData)
{

    stDrvMsgCommRespBuf commRespMSG = {0};

    prtdrv_M3doCommand(ENGCMD_GetLeadingEdgeRegForUser, NULL, NULL);
    msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_GetLeadingEdgeRegForUser, 0);

    if ((commRespMSG.mBuf[0] >> 8) & 0xFF)
    {
        PRTDRV_PRINTF("Get LeadingEdgeRegForUser Sucessful.\n");
        *(Sint8 *)pData = ((commRespMSG.mBuf[0] >> 16) & 0xFF);
        return tOK;
    }
    else
    {
        Uint8 ErrCount = 0;
        PowerSave_EngWakeup_And_Normalmode();
        TASKSLEEP_MILLISECONDS(500);
        while (ErrCount < 30)
        {
            prtdrv_M3doCommand(ENGCMD_GetLeadingEdgeRegForUser, NULL, NULL);
            msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_GetLeadingEdgeRegForUser, 0);

            if ((commRespMSG.mBuf[0] >> 8) & 0xFF)
            {
                *(Sint8 *)pData = ((commRespMSG.mBuf[0] >> 16) & 0xFF);
                break;
            }
            else
            {
                ErrCount++;
                TASKSLEEP_MILLISECONDS(500);
            }
        }

        if (ErrCount < 30)
        {
            PRTDRV_PRINTF("Get LeadingEdgeRegForUser Sucessful\n");
            return tOK;
        }
        else
        {
            PRTDRV_PRINTF("Get LeadingEdgeRegForUser Failed\n");
            return tERROR;
        }
    }
}

tStatus LShell_GetSide2SideReg(void *pData)
{

    stDrvMsgCommRespBuf commRespMSG = {0};

    prtdrv_M3doCommand(ENGCMD_GetSide2SideReg, NULL, NULL);
    msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_GetSide2SideReg, 0);

    if ((commRespMSG.mBuf[0] >> 8) & 0xFF)
    {
        PRTDRV_PRINTF("Get Side2SideReg Sucessful\n");
        *(Sint8 *)pData = ((commRespMSG.mBuf[0] >> 16) & 0xFF);
        return tOK;
    }
    else
    {
        Uint8 ErrCount = 0;
        PowerSave_EngWakeup_And_Normalmode();
        TASKSLEEP_MILLISECONDS(500);
        while (ErrCount < 30)
        {
            prtdrv_M3doCommand(ENGCMD_GetSide2SideReg, NULL, NULL);
            msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_GetSide2SideReg, 0);

            if ((commRespMSG.mBuf[0] >> 8) & 0xFF)
            {
                *(Sint8 *)pData = ((commRespMSG.mBuf[0] >> 16) & 0xFF);
                break;
            }
            else
            {
                ErrCount++;
                TASKSLEEP_MILLISECONDS(500);
            }
        }

        if (ErrCount < 30)
        {
            PRTDRV_PRINTF("Get Side2SideReg Sucessful\n");
            return tOK;
        }
        else
        {
            PRTDRV_PRINTF("Get Side2SideReg Failed\n");
            return tERROR;
        }
    }
}

tStatus LShell_GetSide2SideRegForUser(void *pData)
{

    stDrvMsgCommRespBuf commRespMSG = {0};

    prtdrv_M3doCommand(ENGCMD_GetSide2SideRegForUser, NULL, NULL);
    msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_GetSide2SideRegForUser, 0);

    if ((commRespMSG.mBuf[0] >> 8) & 0xFF)
    {
        PRTDRV_PRINTF("Get Side2SideRegForUser Sucessful\n");
        *(Sint8 *)pData = ((commRespMSG.mBuf[0] >> 16) & 0xFF);
        return tOK;
    }
    else
    {
        Uint8 ErrCount = 0;
        PowerSave_EngWakeup_And_Normalmode();
        TASKSLEEP_MILLISECONDS(500);
        while (ErrCount < 30)
        {
            prtdrv_M3doCommand(ENGCMD_GetSide2SideRegForUser, NULL, NULL);
            msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_GetSide2SideRegForUser, 0);

            if ((commRespMSG.mBuf[0] >> 8) & 0xFF)
            {
                *(Sint8 *)pData = ((commRespMSG.mBuf[0] >> 16) & 0xFF);
                break;
            }
            else
            {
                ErrCount++;
                TASKSLEEP_MILLISECONDS(500);
            }
        }

        if (ErrCount < 30)
        {
            PRTDRV_PRINTF("Get Side2SideRegForUser Sucessful\n");
            return tOK;
        }
        else
        {
            PRTDRV_PRINTF("Get Side2SideRegForUser Failed\n");
            return tERROR;
        }
    }
}

tStatus LShell_GetImageDensity(void *pData)
{

    stDrvMsgCommRespBuf commRespMSG = {0};

    prtdrv_M3doCommand(ENGCMD_GetImageDensity, NULL, NULL);
    msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_GetImageDensity, 0);

    if ((commRespMSG.mBuf[0] >> 8) & 0xFF)
    {
        PRTDRV_PRINTF("Get ImageDensity Sucessful\n");
        *(Sint8 *)pData = ((commRespMSG.mBuf[0] >> 16) & 0xFF);
        return tOK;
    }
    else
    {
        Uint8 ErrCount = 0;
        PowerSave_EngWakeup_And_Normalmode();
        TASKSLEEP_MILLISECONDS(500);
        while (ErrCount < 30)
        {
            prtdrv_M3doCommand(ENGCMD_GetImageDensity, NULL, NULL);
            msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_GetImageDensity, 0);

            if ((commRespMSG.mBuf[0] >> 8) & 0xFF)
            {
                *(Sint8 *)pData = ((commRespMSG.mBuf[0] >> 16) & 0xFF);
                break;
            }
            else
            {
                ErrCount++;
                TASKSLEEP_MILLISECONDS(500);
            }
        }

        if (ErrCount < 30)
        {
            PRTDRV_PRINTF("Get ImageDensity Sucessful\n");
            return tOK;
        }
        else
        {
            PRTDRV_PRINTF("Get ImageDensity Failed\n");
            return tERROR;
        }
    }
}

tStatus LShell_GetLowHumidityMode(void *pData)
{
    stDrvMsgCommRespBuf commRespMSG = {0};

    prtdrv_M3doCommand(ENGCMD_GetLowHumidityMode, NULL, NULL);
    msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_GetLowHumidityMode, 0);

    if ((commRespMSG.mBuf[0] >> 8) & 0xFF)
    {
        PRTDRV_PRINTF("Get LowHumidityMode Sucessful\n");
        *(Uint8 *)pData = ((commRespMSG.mBuf[0] >> 16) & 0xFF);
        return tOK;
    }
    else
    {
        Uint8 ErrCount = 0;
        PowerSave_EngWakeup_And_Normalmode();
        TASKSLEEP_MILLISECONDS(500);
        while (ErrCount < 30)
        {
            prtdrv_M3doCommand(ENGCMD_GetLowHumidityMode, NULL, NULL);
            msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_GetLowHumidityMode, 0);

            if ((commRespMSG.mBuf[0] >> 8) & 0xFF)
            {
                *(Uint8 *)pData = ((commRespMSG.mBuf[0] >> 16) & 0xFF);
                break;
            }
            else
            {
                ErrCount++;
                TASKSLEEP_MILLISECONDS(500);
            }
        }

        if (ErrCount < 30)
        {
            PRTDRV_PRINTF("Get LowHumidityMode Sucessful\n");
            return tOK;
        }
        else
        {
            PRTDRV_PRINTF("Get LowHumidityMode Failed\n");
            return tERROR;
        }
    }
}

tStatus LShell_GetPlateControlMode(void *pData)
{

    stDrvMsgCommRespBuf commRespMSG = {0};

    prtdrv_M3doCommand(ENGCMD_GetPlateControlMode, NULL, NULL);
    msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_GetPlateControlMode, 0);

    if ((commRespMSG.mBuf[0] >> 8) & 0xFF)
    {
        PRTDRV_PRINTF("Get PlateControlMode Sucessful\n");
        *(Uint8 *)pData = ((commRespMSG.mBuf[0] >> 16) & 0xFF);
        return tOK;
    }
    else
    {
        Uint8 ErrCount = 0;
        PowerSave_EngWakeup_And_Normalmode();
        TASKSLEEP_MILLISECONDS(500);
        while (ErrCount < 30)
        {
            prtdrv_M3doCommand(ENGCMD_GetPlateControlMode, NULL, NULL);
            msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_GetPlateControlMode, 0);

            if ((commRespMSG.mBuf[0] >> 8) & 0xFF)
            {
                *(Uint8 *)pData = ((commRespMSG.mBuf[0] >> 16) & 0xFF);
                break;
            }
            else
            {
                ErrCount++;
                TASKSLEEP_MILLISECONDS(500);
            }
        }

        if (ErrCount < 30)
        {
            PRTDRV_PRINTF("Get PlateControlMode Sucessful\n");
            return tOK;
        }
        else
        {
            PRTDRV_PRINTF("Get PlateControlMode Failed\n");
            return tERROR;
        }
    }
}

tStatus LShell_GetDestinationCode(void *pData)
{

    stDrvMsgCommRespBuf commRespMSG = {0};

    prtdrv_M3doCommand(ENGCMD_GetDestinationCode, NULL, NULL);
    msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_GetDestinationCode, 0);

    if ((commRespMSG.mBuf[0] >> 8) & 0xFF)
    {
        PRTDRV_PRINTF("Get DestinationCode Sucessful\n");
        *(Uint8 *)pData = ((commRespMSG.mBuf[0] >> 16) & 0xFF);
        return tOK;
    }
    else
    {
        Uint8 ErrCount = 0;
        PowerSave_EngWakeup_And_Normalmode();
        TASKSLEEP_MILLISECONDS(500);
        while (ErrCount < 30)
        {
            prtdrv_M3doCommand(ENGCMD_GetDestinationCode, NULL, NULL);
            msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_GetDestinationCode, 0);

            if ((commRespMSG.mBuf[0] >> 8) & 0xFF)
            {
                *(Uint8 *)pData = ((commRespMSG.mBuf[0] >> 16) & 0xFF);
                break;
            }
            else
            {
                ErrCount++;
                TASKSLEEP_MILLISECONDS(500);
            }
        }

        if (ErrCount < 30)
        {
            PRTDRV_PRINTF("Get DestinationCode Sucessful\n");
            return tOK;
        }
        else
        {
            PRTDRV_PRINTF("Get DestinationCode Failed\n");
            return tERROR;
        }
    }

}

tStatus LShell_GetPrimaryCoolingMode(void *pData)
{

    stDrvMsgCommRespBuf commRespMSG = {0};

    prtdrv_M3doCommand(ENGCMD_GetPrimaryCoolingMode, NULL, NULL);
    msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_GetPrimaryCoolingMode, 0);

    if ((commRespMSG.mBuf[0] >> 8) & 0xFF)
    {
        PRTDRV_PRINTF("Get PrimaryCoolingMode Sucessful\n");
        *(Uint8 *)pData = ((commRespMSG.mBuf[0] >> 16) & 0xFF);
        return tOK;
    }
    else
    {
        Uint8 ErrCount = 0;
        PowerSave_EngWakeup_And_Normalmode();
        TASKSLEEP_MILLISECONDS(500);
        while (ErrCount < 30)
        {
            prtdrv_M3doCommand(ENGCMD_GetPrimaryCoolingMode, NULL, NULL);
            msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_GetPrimaryCoolingMode, 0);

            if ((commRespMSG.mBuf[0] >> 8) & 0xFF)
            {
                *(Uint8 *)pData = ((commRespMSG.mBuf[0] >> 16) & 0xFF);
                break;
            }
            else
            {
                ErrCount++;
                TASKSLEEP_MILLISECONDS(500);
            }
        }

        if (ErrCount < 30)
        {
            PRTDRV_PRINTF("Get PrimaryCoolingMode Sucessful\n");
            return tOK;
        }
        else
        {
            PRTDRV_PRINTF("Get PrimaryCoolingMode Failed\n");
            return tERROR;
        }
    }

}

tStatus LShell_GetInput_FrontCover(void *pData)
{

    stDrvMsgCommRespBuf commRespMSG = {0};

    prtdrv_M3doCommand(ENGCMD_GetInput_FrontCover, NULL, NULL);
    msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_GetInput_FrontCover, 0);

    if ((commRespMSG.mBuf[0] >> 8) & 0xFF)
    {
        PRTDRV_PRINTF("Get Input_FrontCover Sucessful\n");
        *(Uint8 *)pData = ((commRespMSG.mBuf[0] >> 16) & 0xFF);
        return tOK;
    }
    else
    {
        Uint8 ErrCount = 0;
        PowerSave_EngWakeup_And_Normalmode();
        TASKSLEEP_MILLISECONDS(500);
        while (ErrCount < 30)
        {
            prtdrv_M3doCommand(ENGCMD_GetInput_FrontCover, NULL, NULL);
            msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_GetInput_FrontCover, 0);

            if ((commRespMSG.mBuf[0] >> 8) & 0xFF)
            {
                *(Uint8 *)pData = ((commRespMSG.mBuf[0] >> 16) & 0xFF);
                break;
            }
            else
            {
                ErrCount++;
                TASKSLEEP_MILLISECONDS(500);
            }
        }

        if (ErrCount < 30)
        {
            PRTDRV_PRINTF("Get Input_FrontCover Sucessful\n");
            return tOK;
        }
        else
        {
            PRTDRV_PRINTF("Get Input_FrontCover Failed\n");
            return tERROR;
        }
    }
}

tStatus LShell_GetInput_MainMotorLock(void *pData)
{

    stDrvMsgCommRespBuf commRespMSG = {0};

    prtdrv_M3doCommand(ENGCMD_GetInput_MainMotorLock, NULL, NULL);
    msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_GetInput_MainMotorLock, 0);

    if ((commRespMSG.mBuf[0] >> 8) & 0xFF)
    {
        PRTDRV_PRINTF("Get Input_MainMotorLock Sucessful\n");
        *(Uint8 *)pData = ((commRespMSG.mBuf[0] >> 16) & 0xFF);
        return tOK;
    }
    else
    {
        Uint8 ErrCount = 0;
        PowerSave_EngWakeup_And_Normalmode();
        TASKSLEEP_MILLISECONDS(500);
        while (ErrCount < 30)
        {
            prtdrv_M3doCommand(ENGCMD_GetInput_MainMotorLock, NULL, NULL);
            msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_GetInput_MainMotorLock, 0);

            if ((commRespMSG.mBuf[0] >> 8) & 0xFF)
            {
                *(Uint8 *)pData = ((commRespMSG.mBuf[0] >> 16) & 0xFF);
                break;
            }
            else
            {
                ErrCount++;
                TASKSLEEP_MILLISECONDS(500);
            }
        }

        if (ErrCount < 30)
        {
            PRTDRV_PRINTF("Get Input_MainMotorLock Sucessful\n");
            return tOK;
        }
        else
        {
            PRTDRV_PRINTF("Get Input_MainMotorLock Failed\n");
            return tERROR;
        }
    }
}

tStatus LShell_GetInput_PolygonMotorLock(void *pData)
{

    stDrvMsgCommRespBuf commRespMSG = {0};

    prtdrv_M3doCommand(ENGCMD_GetInput_PolygonMotorLock, NULL, NULL);
    msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_GetInput_PolygonMotorLock, 0);

    if ((commRespMSG.mBuf[0] >> 8) & 0xFF)
    {
        PRTDRV_PRINTF("Get Input_PolygonMotorLock Sucessful\n");
        *(Uint8 *)pData = ((commRespMSG.mBuf[0] >> 16) & 0xFF);
        return tOK;
    }
    else
    {
        Uint8 ErrCount = 0;
        PowerSave_EngWakeup_And_Normalmode();
        TASKSLEEP_MILLISECONDS(500);
        while (ErrCount < 30)
        {
            prtdrv_M3doCommand(ENGCMD_GetInput_PolygonMotorLock, NULL, NULL);
            msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_GetInput_PolygonMotorLock, 0);

            if ((commRespMSG.mBuf[0] >> 8) & 0xFF)
            {
                *(Uint8 *)pData = ((commRespMSG.mBuf[0] >> 16) & 0xFF);
                break;
            }
            else
            {
                ErrCount++;
                TASKSLEEP_MILLISECONDS(500);
            }
        }

        if (ErrCount < 30)
        {
            PRTDRV_PRINTF("Get Input_PolygonMotorLock Sucessful\n");
            return tOK;
        }
        else
        {
            PRTDRV_PRINTF("Get Input_PolygonMotorLock Failed\n");
            return tERROR;
        }
    }
}

tStatus LShell_GetInput_FanLock(void *pData)
{

    stDrvMsgCommRespBuf commRespMSG = {0};

    prtdrv_M3doCommand(ENGCMD_GetInput_FanLock, NULL, NULL);
    msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_GetInput_FanLock, 0);

    if ((commRespMSG.mBuf[0] >> 8) & 0xFF)
    {
        PRTDRV_PRINTF("Get Input_FanLock Sucessful\n");
        *(Uint8 *)pData = ((commRespMSG.mBuf[0] >> 16) & 0xFF);
        return tOK;
    }
    else
    {
        Uint8 ErrCount = 0;
        PowerSave_EngWakeup_And_Normalmode();
        TASKSLEEP_MILLISECONDS(500);
        while (ErrCount < 30)
        {
            prtdrv_M3doCommand(ENGCMD_GetInput_FanLock, NULL, NULL);
            msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_GetInput_FanLock, 0);

            if ((commRespMSG.mBuf[0] >> 8) & 0xFF)
            {
                *(Uint8 *)pData = ((commRespMSG.mBuf[0] >> 16) & 0xFF);
                break;
            }
            else
            {
                ErrCount++;
                TASKSLEEP_MILLISECONDS(500);
            }
        }

        if (ErrCount < 30)
        {
            PRTDRV_PRINTF("Get Input_FanLock Sucessful\n");
            return tOK;
        }
        else
        {
            PRTDRV_PRINTF("Get Input_FanLock Failed\n");
            return tERROR;
        }
    }
}

tStatus LShell_GetInput_LDXDETPCHECK(void *pData)
{

    stDrvMsgCommRespBuf commRespMSG = {0};

    prtdrv_M3doCommand(ENGCMD_GetInput_LDXDETPCHECK, NULL, NULL);
    msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_GetInput_LDXDETPCHECK, 0);

    if ((commRespMSG.mBuf[0] >> 8) & 0xFF)
    {
        PRTDRV_PRINTF("Get Input_LDXDETPCHECK Sucessful\n");
        *(Uint8 *)pData = ((commRespMSG.mBuf[0] >> 16) & 0xFF);
        return tOK;
    }
    else
    {
        Uint8 ErrCount = 0;
        PowerSave_EngWakeup_And_Normalmode();
        TASKSLEEP_MILLISECONDS(500);
        while (ErrCount < 30)
        {
            prtdrv_M3doCommand(ENGCMD_GetInput_LDXDETPCHECK, NULL, NULL);
            msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_GetInput_LDXDETPCHECK, 0);

            if ((commRespMSG.mBuf[0] >> 8) & 0xFF)
            {
                *(Uint8 *)pData = ((commRespMSG.mBuf[0] >> 16) & 0xFF);
                break;
            }
            else
            {
                ErrCount++;
                TASKSLEEP_MILLISECONDS(500);
            }
        }

        if (ErrCount < 30)
        {
            PRTDRV_PRINTF("Get Input_LDXDETPCHECK Sucessful\n");
            return tOK;
        }
        else
        {
            PRTDRV_PRINTF("Get Input_LDXDETPCHECK Failed\n");
            return tERROR;
        }
    }
}

tStatus LShell_GetInput_LDError(void *pData)
{
    stDrvMsgCommRespBuf commRespMSG = {0};

    prtdrv_M3doCommand(ENGCMD_GetInput_LDError, NULL, NULL);
    msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_GetInput_LDError, 0);

    if ((commRespMSG.mBuf[0] >> 8) & 0xFF)
    {
        PRTDRV_PRINTF("Get Input_LDError Sucessful\n");
        *(Uint8 *)pData = ((commRespMSG.mBuf[0] >> 16) & 0xFF);
        return tOK;
    }
    else
    {
        Uint8 ErrCount = 0;
        PowerSave_EngWakeup_And_Normalmode();
        TASKSLEEP_MILLISECONDS(500);
        while (ErrCount < 30)
        {
            prtdrv_M3doCommand(ENGCMD_GetInput_LDError, NULL, NULL);
            msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_GetInput_LDError, 0);

            if ((commRespMSG.mBuf[0] >> 8) & 0xFF)
            {
                *(Uint8 *)pData = ((commRespMSG.mBuf[0] >> 16) & 0xFF);
                break;
            }
            else
            {
                ErrCount++;
                TASKSLEEP_MILLISECONDS(500);
            }
        }

        if (ErrCount < 30)
        {
            PRTDRV_PRINTF("Get Input_LDError Sucessful\n");
            return tOK;
        }
        else
        {
            PRTDRV_PRINTF("Get Input_LDError Failed\n");
            return tERROR;
        }
    }
}

tStatus LShell_GetInput_HVPError(void *pData)
{
    stDrvMsgCommRespBuf commRespMSG = {0};

    prtdrv_M3doCommand(ENGCMD_GetInput_HVPError, NULL, NULL);
    msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_GetInput_HVPError, 0);

    if ((commRespMSG.mBuf[0] >> 8) & 0xFF)
    {
        PRTDRV_PRINTF("Get Input_HVPError Sucessful\n");
        *(Uint8 *)pData = ((commRespMSG.mBuf[0] >> 16) & 0xFF);
        return tOK;
    }
    else
    {
        Uint8 ErrCount = 0;
        PowerSave_EngWakeup_And_Normalmode();
        TASKSLEEP_MILLISECONDS(500);
        while (ErrCount < 30)
        {
            prtdrv_M3doCommand(ENGCMD_GetInput_HVPError, NULL, NULL);
            msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_GetInput_HVPError, 0);

            if ((commRespMSG.mBuf[0] >> 8) & 0xFF)
            {
                *(Uint8 *)pData = ((commRespMSG.mBuf[0] >> 16) & 0xFF);
                break;
            }
            else
            {
                ErrCount++;
                TASKSLEEP_MILLISECONDS(500);
            }
        }

        if (ErrCount < 30)
        {
            PRTDRV_PRINTF("Get Input_HVPError Sucessful\n");
            return tOK;
        }
        else
        {
            PRTDRV_PRINTF("Get Input_HVPError Failed\n");
            return tERROR;
        }
    }
}

tStatus LShell_GetInput_FuserHighTemp(void *pData)
{

    stDrvMsgCommRespBuf commRespMSG = {0};

    prtdrv_M3doCommand(ENGCMD_GetInput_FuserHighTemp, NULL, NULL);
    msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_GetInput_FuserHighTemp, 0);

    if ((commRespMSG.mBuf[0] >> 8) & 0xFF)
    {
        PRTDRV_PRINTF("Get Input_FuserHighTemp Sucessful\n");
        *(Uint8 *)pData = ((commRespMSG.mBuf[0] >> 16) & 0xFF);
        return tOK;
    }
    else
    {
        Uint8 ErrCount = 0;
        PowerSave_EngWakeup_And_Normalmode();
        TASKSLEEP_MILLISECONDS(500);
        while (ErrCount < 30)
        {
            prtdrv_M3doCommand(ENGCMD_GetInput_FuserHighTemp, NULL, NULL);
            msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_GetInput_FuserHighTemp, 0);

            if ((commRespMSG.mBuf[0] >> 8) & 0xFF)
            {
                *(Uint8 *)pData = ((commRespMSG.mBuf[0] >> 16) & 0xFF);
                break;
            }
            else
            {
                ErrCount++;
                TASKSLEEP_MILLISECONDS(500);
            }
        }

        if (ErrCount < 30)
        {
            PRTDRV_PRINTF("Get Input_FuserHighTemp Sucessful\n");
            return tOK;
        }
        else
        {
            PRTDRV_PRINTF("Get Input_FuserHighTemp Failed\n");
            return tERROR;
        }
    }
}

tStatus LShell_GetInput_RegistSens(void *pData)
{

    stDrvMsgCommRespBuf commRespMSG = {0};

    prtdrv_M3doCommand(ENGCMD_GetInput_RegistSens, NULL, NULL);
    msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_GetInput_RegistSens, 0);

    if ((commRespMSG.mBuf[0] >> 8) & 0xFF)
    {
        PRTDRV_PRINTF("Get Input_RegistSens Sucessful\n");
        *(Uint8 *)pData = ((commRespMSG.mBuf[0] >> 16) & 0xFF);
        return tOK;
    }
    else
    {
        Uint8 ErrCount = 0;
        PowerSave_EngWakeup_And_Normalmode();
        TASKSLEEP_MILLISECONDS(500);
        while (ErrCount < 30)
        {
            prtdrv_M3doCommand(ENGCMD_GetInput_RegistSens, NULL, NULL);
            msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_GetInput_RegistSens, 0);

            if ((commRespMSG.mBuf[0] >> 8) & 0xFF)
            {
                *(Uint8 *)pData = ((commRespMSG.mBuf[0] >> 16) & 0xFF);
                break;
            }
            else
            {
                ErrCount++;
                TASKSLEEP_MILLISECONDS(500);
            }
        }

        if (ErrCount < 30)
        {
            PRTDRV_PRINTF("Get Input_RegistSens Sucessful\n");
            return tOK;
        }
        else
        {
            PRTDRV_PRINTF("Get Input_RegistSens Failed\n");
            return tERROR;
        }
    }
}

tStatus LShell_GetInput_ExitSens(void *pData)
{

    stDrvMsgCommRespBuf commRespMSG = {0};

    prtdrv_M3doCommand(ENGCMD_GetInput_ExitSens, NULL, NULL);
    msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_GetInput_ExitSens, 0);

    if ((commRespMSG.mBuf[0] >> 8) & 0xFF)
    {
        PRTDRV_PRINTF("Get Input_ExitSens Sucessful\n");
        *(Uint8 *)pData = ((commRespMSG.mBuf[0] >> 16) & 0xFF);
        return tOK;
    }
    else
    {
        Uint8 ErrCount = 0;
        PowerSave_EngWakeup_And_Normalmode();
        TASKSLEEP_MILLISECONDS(500);
        while (ErrCount < 30)
        {
            prtdrv_M3doCommand(ENGCMD_GetInput_ExitSens, NULL, NULL);
            msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_GetInput_ExitSens, 0);

            if ((commRespMSG.mBuf[0] >> 8) & 0xFF)
            {
                *(Uint8 *)pData = ((commRespMSG.mBuf[0] >> 16) & 0xFF);
                break;
            }
            else
            {
                ErrCount++;
                TASKSLEEP_MILLISECONDS(500);
            }
        }

        if (ErrCount < 30)
        {
            PRTDRV_PRINTF("Get Input_ExitSens Sucessful\n");
            return tOK;
        }
        else
        {
            PRTDRV_PRINTF("Get Input_ExitSens Failed\n");
            return tERROR;
        }
    }
}

tStatus LShell_GetInput_FuserThermistor(void *pData)
{

    stDrvMsgCommRespBuf commRespMSG = {0};

    prtdrv_M3doCommand(ENGCMD_GetInput_FuserThermistor, NULL, NULL);
    msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_GetInput_FuserThermistor, 0);

    if ((commRespMSG.mBuf[0] >> 8) & 0xFF)
    {
        PRTDRV_PRINTF("Get Input_FuserThermistor Sucessful\n");
        *(Uint8 *)pData = ((commRespMSG.mBuf[0] >> 16) & 0xFF);
        return tOK;
    }
    else
    {
        Uint8 ErrCount = 0;
        PowerSave_EngWakeup_And_Normalmode();
        TASKSLEEP_MILLISECONDS(500);
        while (ErrCount < 30)
        {
            prtdrv_M3doCommand(ENGCMD_GetInput_FuserThermistor, NULL, NULL);
            msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_GetInput_FuserThermistor, 0);

            if ((commRespMSG.mBuf[0] >> 8) & 0xFF)
            {
                *(Uint8 *)pData = ((commRespMSG.mBuf[0] >> 16) & 0xFF);
                break;
            }
            else
            {
                ErrCount++;
                TASKSLEEP_MILLISECONDS(500);
            }
        }

        if (ErrCount < 30)
        {
            PRTDRV_PRINTF("Get Input_FuserThermistor Sucessful\n");
            return tOK;
        }
        else
        {
            PRTDRV_PRINTF("Get Input_FuserThermistor Failed\n");
            return tERROR;
        }
    }
}

tStatus LShell_GetInput_VideoThermistor(void *pData)
{

    stDrvMsgCommRespBuf commRespMSG = {0};

    prtdrv_M3doCommand(ENGCMD_GetInput_VideoThermistor, NULL, NULL);
    msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_GetInput_VideoThermistor, 0);

    if ((commRespMSG.mBuf[0] >> 8) & 0xFF)
    {
        PRTDRV_PRINTF("Get Input_VideoThermistor Sucessful\n");
        *(Uint8 *)pData = ((commRespMSG.mBuf[0] >> 16) & 0xFF);
        return tOK;
    }
    else
    {
        Uint8 ErrCount = 0;
        PowerSave_EngWakeup_And_Normalmode();
        TASKSLEEP_MILLISECONDS(500);
        while (ErrCount < 30)
        {
            prtdrv_M3doCommand(ENGCMD_GetInput_VideoThermistor, NULL, NULL);
            msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_GetInput_VideoThermistor, 0);

            if ((commRespMSG.mBuf[0] >> 8) & 0xFF)
            {
                *(Uint8 *)pData = ((commRespMSG.mBuf[0] >> 16) & 0xFF);
                break;
            }
            else
            {
                ErrCount++;
                TASKSLEEP_MILLISECONDS(500);
            }
        }

        if (ErrCount < 30)
        {
            PRTDRV_PRINTF("Get Input_VideoThermistor Sucessful\n");
            return tOK;
        }
        else
        {
            PRTDRV_PRINTF("Get Input_VideoThermistor Failed\n");
            return tERROR;
        }
    }
}

tStatus LShell_GetInput_AIO_ID_Chip(void *pData)
{

    stDrvMsgCommRespBuf commRespMSG = {0};

    prtdrv_M3doCommand(ENGCMD_GetInput_AIO_ID_Chip, NULL, NULL);
    msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_GetInput_AIO_ID_Chip, 0);

    if ((commRespMSG.mBuf[0] >> 8) & 0xFF)
    {
        PRTDRV_PRINTF("Get Input_AIO_ID_Chip Sucessful\n");
        *(Uint8 *)pData = ((commRespMSG.mBuf[0] >> 16) & 0xFF);
        return tOK;
    }
    else
    {
        Uint8 ErrCount = 0;
        PowerSave_EngWakeup_And_Normalmode();
        TASKSLEEP_MILLISECONDS(500);
        while (ErrCount < 30)
        {
            prtdrv_M3doCommand(ENGCMD_GetInput_AIO_ID_Chip, NULL, NULL);
            msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_GetInput_AIO_ID_Chip, 0);

            if ((commRespMSG.mBuf[0] >> 8) & 0xFF)
            {
                *(Uint8 *)pData = ((commRespMSG.mBuf[0] >> 16) & 0xFF);
                break;
            }
            else
            {
                ErrCount++;
                TASKSLEEP_MILLISECONDS(500);
            }
        }

        if (ErrCount < 30)
        {
            PRTDRV_PRINTF("Get Input_AIO_ID_Chip Sucessful\n");
            return tOK;
        }
        else
        {
            PRTDRV_PRINTF("Get Input_AIO_ID_Chip Failed\n");
            return tERROR;
        }
    }
}

tStatus LShell_GetInput_ACLowVoltage(void *pData)
{
    stDrvMsgCommRespBuf commRespMSG = {0};

    prtdrv_M3doCommand(ENGCMD_GetInput_ACLowVoltage, NULL, NULL);
    msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_GetInput_ACLowVoltage, 0);

    if ((commRespMSG.mBuf[0] >> 8) & 0xFF)
    {
        PRTDRV_PRINTF("Get Input_ACLowVoltage Sucessful\n");
        *(Uint8 *)pData = ((commRespMSG.mBuf[0] >> 16) & 0xFF);
        return tOK;
    }
    else
    {
        Uint8 ErrCount = 0;
        PowerSave_EngWakeup_And_Normalmode();
        TASKSLEEP_MILLISECONDS(500);
        while (ErrCount < 30)
        {
            prtdrv_M3doCommand(ENGCMD_GetInput_ACLowVoltage, NULL, NULL);
            msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_GetInput_ACLowVoltage, 0);

            if ((commRespMSG.mBuf[0] >> 8) & 0xFF)
            {
                *(Uint8 *)pData = ((commRespMSG.mBuf[0] >> 16) & 0xFF);
                break;
            }
            else
            {
                ErrCount++;
                TASKSLEEP_MILLISECONDS(500);
            }
        }

        if (ErrCount < 30)
        {
            PRTDRV_PRINTF("Get Input_ACLowVoltage Sucessful\n");
            return tOK;
        }
        else
        {
            PRTDRV_PRINTF("Get Input_ACLowVoltage Failed\n");
            return tERROR;
        }
    }
}

tStatus LShell_GetInput_PlateSensor(void *pData)
{
    stDrvMsgCommRespBuf commRespMSG = {0};

    prtdrv_M3doCommand(ENGCMD_GetInput_PlateSensor, NULL, NULL);
    msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_GetInput_PlateSensor, 0);

    if ((commRespMSG.mBuf[0] >> 8) & 0xFF)
    {
        PRTDRV_PRINTF("Get Input_PlateSensor Sucessful\n");
        *(Uint8 *)pData = ((commRespMSG.mBuf[0] >> 16) & 0xFF);
        return tOK;
    }
    else
    {
        Uint8 ErrCount = 0;
        PowerSave_EngWakeup_And_Normalmode();
        TASKSLEEP_MILLISECONDS(500);
        while (ErrCount < 30)
        {
            prtdrv_M3doCommand(ENGCMD_GetInput_PlateSensor, NULL, NULL);
            msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_GetInput_PlateSensor, 0);

            if ((commRespMSG.mBuf[0] >> 8) & 0xFF)
            {
                *(Uint8 *)pData = ((commRespMSG.mBuf[0] >> 16) & 0xFF);
                break;
            }
            else
            {
                ErrCount++;
                TASKSLEEP_MILLISECONDS(500);
            }
        }

        if (ErrCount < 30)
        {
            PRTDRV_PRINTF("Get Input_PlateSensor Sucessful\n");
            return tOK;
        }
        else
        {
            PRTDRV_PRINTF("Get Input_PlateSensor Failed\n");
            return tERROR;
        }
    }
}

tStatus LShell_GetACPowerUnstableTimes(void *pData)
{
    stDrvMsgCommRespBuf commRespMSG = {0};

    prtdrv_M3doCommand(ENGCMD_GetACPowerUnstableTimes, NULL, NULL);
    msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_GetACPowerUnstableTimes, 0);

    if ((commRespMSG.mBuf[0] >> 8) & 0xFF)
    {
        PRTDRV_PRINTF("Get ACPowerUnstableTimes Sucessful\n");
        *(Uint32 *)pData = commRespMSG.mBuf[1];
        return tOK;
    }
    else
    {
        Uint8 ErrCount = 0;
        PowerSave_EngWakeup_And_Normalmode();
        TASKSLEEP_MILLISECONDS(500);
        while (ErrCount < 30)
        {
            prtdrv_M3doCommand(ENGCMD_GetACPowerUnstableTimes, NULL, NULL);
            msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_GetACPowerUnstableTimes, 0);

            if ((commRespMSG.mBuf[0] >> 8) & 0xFF)
            {
                *(Uint32 *)pData = commRespMSG.mBuf[1];
                break;
            }
            else
            {
                ErrCount++;
                TASKSLEEP_MILLISECONDS(500);
            }
        }

        if (ErrCount < 30)
        {
            PRTDRV_PRINTF("Get ACPowerUnstableTimes Sucessful\n");
            return tOK;
        }
        else
        {
            PRTDRV_PRINTF("Get ACPowerUnstableTimes Failed\n");
            return tERROR;
        }
    }
}

tStatus LShell_GetTotalCounter(void *pData)
{

    stDrvMsgCommRespBuf commRespMSG = {0};

    prtdrv_M3doCommand(ENGCMD_GetTotalCounter, NULL, NULL);
    msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_GetTotalCounter, 0);

    if ((commRespMSG.mBuf[0] >> 8) & 0xFF)
    {
        PRTDRV_PRINTF("Get TotalCounter Sucessful\n");
        *(Uint32 *)pData = commRespMSG.mBuf[1];
        return tOK;
    }
    else
    {
        Uint8 ErrCount = 0;
        PowerSave_EngWakeup_And_Normalmode();
        TASKSLEEP_MILLISECONDS(500);
        while (ErrCount < 30)
        {
            prtdrv_M3doCommand(ENGCMD_GetTotalCounter, NULL, NULL);
            msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_GetTotalCounter, 0);

            if ((commRespMSG.mBuf[0] >> 8) & 0xFF)
            {
                *(Uint32 *)pData = commRespMSG.mBuf[1];
                break;
            }
            else
            {
                ErrCount++;
                TASKSLEEP_MILLISECONDS(500);
            }
        }

        if (ErrCount < 30)
        {
            PRTDRV_PRINTF("Get TotalCounter Sucessful\n");
            return tOK;
        }
        else
        {
            PRTDRV_PRINTF("Get TotalCounter Failed\n");
            return tERROR;
        }
    }
}

tStatus LShell_SetLeadingEdgeReg(void *pData)
{

    stDrvMsgCommRespBuf commRespMSG = {0};

    prtdrv_M3doCommand(ENGCMD_SetLeadingEdgeReg, pData, NULL);
    msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_SetLeadingEdgeReg, 0);

    if ((commRespMSG.mBuf[0] >> 8) & 0xFF)
    {
        PRTDRV_PRINTF("Set LeadingEdgeReg Sucessful.\n");
        return tOK;
    }
    else
    {
        Uint8 ErrCount = 0;
        PowerSave_EngWakeup_And_Normalmode();
        TASKSLEEP_MILLISECONDS(500);
        while (ErrCount < 30)
        {
            prtdrv_M3doCommand(ENGCMD_SetLeadingEdgeReg, pData, NULL);
            msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_SetLeadingEdgeReg, 0);

            if ((commRespMSG.mBuf[0] >> 8) & 0xFF)
            {
                break;
            }
            else
            {
                ErrCount++;
                TASKSLEEP_MILLISECONDS(500);
            }
        }

        if (ErrCount < 30)
        {
            PRTDRV_PRINTF("Set LeadingEdgeReg Sucessful\n");
            return tOK;
        }
        else
        {
            PRTDRV_PRINTF("Set LeadingEdgeReg Failed\n");
            return tERROR;
        }
    }
}

tStatus LShell_SetSide2SideReg(void *pData)
{
    stDrvMsgCommRespBuf commRespMSG = {0};

    prtdrv_M3doCommand(ENGCMD_SetSide2SideReg, pData, NULL);
    msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_SetSide2SideReg, 0);

    if ((commRespMSG.mBuf[0] >> 8) & 0xFF)
    {
        PRTDRV_PRINTF("Set Side2SideReg Sucessful\n");
        return tOK;
    }
    else
    {
        Uint8 ErrCount = 0;
        PowerSave_EngWakeup_And_Normalmode();
        TASKSLEEP_MILLISECONDS(500);
        while (ErrCount < 30)
        {
            prtdrv_M3doCommand(ENGCMD_SetSide2SideReg, pData, NULL);
            msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_SetSide2SideReg, 0);

            if ((commRespMSG.mBuf[0] >> 8) & 0xFF)
            {
                break;
            }
            else
            {
                ErrCount++;
                TASKSLEEP_MILLISECONDS(500);
            }
        }

        if (ErrCount < 30)
        {
            PRTDRV_PRINTF("Set Side2SideReg Sucessful\n");
            return tOK;
        }
        else
        {
            PRTDRV_PRINTF("Set Side2SideReg Failed\n");
            return tERROR;
        }
    }
}

tStatus LShell_SetSide2SideRegForUser(void *pData)
{
    stDrvMsgCommRespBuf commRespMSG = {0};

    prtdrv_M3doCommand(ENGCMD_SetSide2SideRegForUser, pData, NULL);
    msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_SetSide2SideRegForUser, 0);

    if ((commRespMSG.mBuf[0] >> 8) & 0xFF)
    {
        PRTDRV_PRINTF("Set Side2SideRegForUser Sucessful\n");
        return tOK;
    }
    else
    {
        Uint8 ErrCount = 0;
        PowerSave_EngWakeup_And_Normalmode();
        TASKSLEEP_MILLISECONDS(500);
        while (ErrCount < 30)
        {
            prtdrv_M3doCommand(ENGCMD_SetSide2SideRegForUser, pData, NULL);
            msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_SetSide2SideRegForUser, 0);

            if ((commRespMSG.mBuf[0] >> 8) & 0xFF)
            {
                break;
            }
            else
            {
                ErrCount++;
                TASKSLEEP_MILLISECONDS(500);
            }
        }

        if (ErrCount < 30)
        {
            PRTDRV_PRINTF("Set Side2SideRegForUser Sucessful\n");
            return tOK;
        }
        else
        {
            PRTDRV_PRINTF("Set Side2SideRegForUser Failed\n");
            return tERROR;
        }
    }
}

tStatus LShell_SetImageDensity(void *pData)
{
    stDrvMsgCommRespBuf commRespMSG = {0};

    prtdrv_M3doCommand(ENGCMD_SetImageDensity, pData, NULL);
    msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_SetImageDensity, 0);

    if ((commRespMSG.mBuf[0] >> 8) & 0xFF)
    {
        PRTDRV_PRINTF("Set ImageDensity Sucessful\n");
        return tOK;
    }
    else
    {
        Uint8 ErrCount = 0;
        PowerSave_EngWakeup_And_Normalmode();
        TASKSLEEP_MILLISECONDS(500);
        while (ErrCount < 30)
        {
            prtdrv_M3doCommand(ENGCMD_SetImageDensity, pData, NULL);
            msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_SetImageDensity, 0);

            if ((commRespMSG.mBuf[0] >> 8) & 0xFF)
            {
                break;
            }
            else
            {
                ErrCount++;
                TASKSLEEP_MILLISECONDS(500);
            }
        }

        if (ErrCount < 30)
        {
            PRTDRV_PRINTF("Set ImageDensity Sucessful\n");
            return tOK;
        }
        else
        {
            PRTDRV_PRINTF("Set ImageDensity Failed\n");
            return tERROR;
        }
    }
}

tStatus LShell_SetLowHumidityMode(void *pData)
{

    stDrvMsgCommRespBuf commRespMSG = {0};

    prtdrv_M3doCommand(ENGCMD_SetLowHumidityMode, pData, NULL);
    msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_SetLowHumidityMode, 0);

    if ((commRespMSG.mBuf[0] >> 8) & 0xFF)
    {
        PRTDRV_PRINTF("Set LowHumidityMode Sucessful\n");
        return tOK;
    }
    else
    {
        Uint8 ErrCount = 0;
        PowerSave_EngWakeup_And_Normalmode();
        TASKSLEEP_MILLISECONDS(500);
        while (ErrCount < 30)
        {
            prtdrv_M3doCommand(ENGCMD_SetLowHumidityMode, pData, NULL);
            msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_SetLowHumidityMode, 0);

            if ((commRespMSG.mBuf[0] >> 8) & 0xFF)
            {
                break;
            }
            else
            {
                ErrCount++;
                TASKSLEEP_MILLISECONDS(500);
            }
        }

        if (ErrCount < 30)
        {
            PRTDRV_PRINTF("Set LowHumidityMode Sucessful\n");
            return tOK;
        }
        else
        {
            PRTDRV_PRINTF("Set LowHumidityMode Failed\n");
            return tERROR;
        }
    }
}

tStatus LShell_SetPlateControlMode(void *pData)
{
    stDrvMsgCommRespBuf commRespMSG = {0};

    prtdrv_M3doCommand(ENGCMD_SetPlateControlMode, pData, NULL);
    msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_SetPlateControlMode, 0);

    if ((commRespMSG.mBuf[0] >> 8) & 0xFF)
    {
        PRTDRV_PRINTF("Set PlateControlMode Sucessful\n");
        return tOK;
    }
    else
    {
        Uint8 ErrCount = 0;
        PowerSave_EngWakeup_And_Normalmode();
        TASKSLEEP_MILLISECONDS(500);
        while (ErrCount < 30)
        {
            prtdrv_M3doCommand(ENGCMD_SetPlateControlMode, pData, NULL);
            msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_SetPlateControlMode, 0);

            if ((commRespMSG.mBuf[0] >> 8) & 0xFF)
            {
                break;
            }
            else
            {
                ErrCount++;
                TASKSLEEP_MILLISECONDS(500);
            }
        }

        if (ErrCount < 30)
        {
            PRTDRV_PRINTF("Set PlateControlMode Sucessful\n");
            return tOK;
        }
        else
        {
            PRTDRV_PRINTF("Set PlateControlMode Failed\n");
            return tERROR;
        }
    }
}

tStatus LShell_SetPrimaryCoolingMode(void *pData)
{
    stDrvMsgCommRespBuf commRespMSG = {0};

    prtdrv_M3doCommand(ENGCMD_SetPrimaryCoolingMode, pData, NULL);
    msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_SetPrimaryCoolingMode, 0);

    if ((commRespMSG.mBuf[0] >> 8) & 0xFF)
    {
        PRTDRV_PRINTF("Set PrimaryCoolingMode Sucessful\n");
        return tOK;
    }
    else
    {
        Uint8 ErrCount = 0;
        PowerSave_EngWakeup_And_Normalmode();
        TASKSLEEP_MILLISECONDS(500);
        while (ErrCount < 30)
        {
            prtdrv_M3doCommand(ENGCMD_SetPrimaryCoolingMode, pData, NULL);
            msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_SetPrimaryCoolingMode, 0);

            if ((commRespMSG.mBuf[0] >> 8) & 0xFF)
            {
                break;
            }
            else
            {
                ErrCount++;
                TASKSLEEP_MILLISECONDS(500);
            }
        }

        if (ErrCount < 30)
        {
            PRTDRV_PRINTF("Set PrimaryCoolingMode Sucessful\n");
            return tOK;
        }
        else
        {
            PRTDRV_PRINTF("Set PrimaryCoolingMode Failed\n");
            return tERROR;
        }
    }
}

tStatus LShell_SetOutput_MainMotor(void *pData)
{
    stDrvMsgCommRespBuf commRespMSG = {0};

    prtdrv_M3doCommand(ENGCMD_SetOutput_MainMotor, pData, NULL);
    msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_SetOutput_MainMotor, 0);

    if ((commRespMSG.mBuf[0] >> 8) & 0xFF)
    {
        PRTDRV_PRINTF("Set Output_MainMotor Sucessful\n");
        return tOK;
    }
    else
    {
        Uint8 ErrCount = 0;
        PowerSave_EngWakeup_And_Normalmode();
        TASKSLEEP_MILLISECONDS(500);
        while (ErrCount < 30)
        {
            prtdrv_M3doCommand(ENGCMD_SetOutput_MainMotor, pData, NULL);
            msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_SetOutput_MainMotor, 0);

            if ((commRespMSG.mBuf[0] >> 8) & 0xFF)
            {
                break;
            }
            else
            {
                ErrCount++;
                TASKSLEEP_MILLISECONDS(500);
            }
        }

        if (ErrCount < 30)
        {
            PRTDRV_PRINTF("Set Output_MainMotor Sucessful\n");
            return tOK;
        }
        else
        {
            PRTDRV_PRINTF("Set Output_MainMotor Failed\n");
            return tERROR;
        }
    }
}

tStatus LShell_SetOutput_FeedCluth(void *pData)
{
    stDrvMsgCommRespBuf commRespMSG = {0};

    prtdrv_M3doCommand(ENGCMD_SetOutput_FeedCluth, pData, NULL);
    msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_SetOutput_FeedCluth, 0);

    if ((commRespMSG.mBuf[0] >> 8) & 0xFF)
    {
        PRTDRV_PRINTF("Set Output_FeedCluth Sucessful\n");
        return tOK;
    }
    else
    {
        Uint8 ErrCount = 0;
        PowerSave_EngWakeup_And_Normalmode();
        TASKSLEEP_MILLISECONDS(500);
        while (ErrCount < 30)
        {
            prtdrv_M3doCommand(ENGCMD_SetOutput_FeedCluth, pData, NULL);
            msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_SetOutput_FeedCluth, 0);

            if ((commRespMSG.mBuf[0] >> 8) & 0xFF)
            {
                break;
            }
            else
            {
                ErrCount++;
                TASKSLEEP_MILLISECONDS(500);
            }
        }

        if (ErrCount < 30)
        {
            PRTDRV_PRINTF("Set Output_FeedCluth Sucessful\n");
            return tOK;
        }
        else
        {
            PRTDRV_PRINTF("Set Output_FeedCluth Failed\n");
            return tERROR;
        }
    }
}

tStatus LShell_SetOutput_PlateClutch(void *pData)
{
    stDrvMsgCommRespBuf commRespMSG = {0};

    prtdrv_M3doCommand(ENGCMD_SetOutput_PlateClutch, pData, NULL);
    msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_SetOutput_PlateClutch, 0);

    if ((commRespMSG.mBuf[0] >> 8) & 0xFF)
    {
        PRTDRV_PRINTF("Set Output_PlateClutch Sucessful\n");
        return tOK;
    }
    else
    {
        Uint8 ErrCount = 0;
        PowerSave_EngWakeup_And_Normalmode();
        TASKSLEEP_MILLISECONDS(500);
        while (ErrCount < 30)
        {
            prtdrv_M3doCommand(ENGCMD_SetOutput_PlateClutch, pData, NULL);
            msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_SetOutput_PlateClutch, 0);

            if ((commRespMSG.mBuf[0] >> 8) & 0xFF)
            {
                break;
            }
            else
            {
                ErrCount++;
                TASKSLEEP_MILLISECONDS(500);
            }
        }

        if (ErrCount < 30)
        {
            PRTDRV_PRINTF("Set Output_PlateClutch Sucessful\n");
            return tOK;
        }
        else
        {
            PRTDRV_PRINTF("Set Output_PlateClutch Failed\n");
            return tERROR;
        }
    }
}

tStatus LShell_SetOutput_FanHighSpeed(void *pData)
{

    stDrvMsgCommRespBuf commRespMSG = {0};

    prtdrv_M3doCommand(ENGCMD_SetOutput_FanHighSpeed, pData, NULL);
    msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_SetOutput_FanHighSpeed, 0);

    if ((commRespMSG.mBuf[0] >> 8) & 0xFF)
    {
        PRTDRV_PRINTF("Set Output_FanHighSpeed Sucessful\n");
        return tOK;
    }
    else
    {
        Uint8 ErrCount = 0;
        PowerSave_EngWakeup_And_Normalmode();
        TASKSLEEP_MILLISECONDS(500);
        while (ErrCount < 30)
        {
            prtdrv_M3doCommand(ENGCMD_SetOutput_FanHighSpeed, pData, NULL);
            msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_SetOutput_FanHighSpeed, 0);

            if ((commRespMSG.mBuf[0] >> 8) & 0xFF)
            {
                break;
            }
            else
            {
                ErrCount++;
                TASKSLEEP_MILLISECONDS(500);
            }
        }

        if (ErrCount < 30)
        {
            PRTDRV_PRINTF("Set Output_FanHighSpeed Sucessful\n");
            return tOK;
        }
        else
        {
            PRTDRV_PRINTF("Set Output_FanHighSpeed Failed\n");
            return tERROR;
        }
    }
}

tStatus LShell_SetOutput_FanLowSpeed(void *pData)
{

    stDrvMsgCommRespBuf commRespMSG = {0};

    prtdrv_M3doCommand(ENGCMD_SetOutput_FanLowSpeed, pData, NULL);
    msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_SetOutput_FanLowSpeed, 0);

    if ((commRespMSG.mBuf[0] >> 8) & 0xFF)
    {
        PRTDRV_PRINTF("Set Output_FanLowSpeed Sucessful\n");
        return tOK;
    }
    else
    {
        Uint8 ErrCount = 0;
        PowerSave_EngWakeup_And_Normalmode();
        TASKSLEEP_MILLISECONDS(500);
        while (ErrCount < 30)
        {
            prtdrv_M3doCommand(ENGCMD_SetOutput_FanLowSpeed, pData, NULL);
            msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_SetOutput_FanLowSpeed, 0);

            if ((commRespMSG.mBuf[0] >> 8) & 0xFF)
            {
                break;
            }
            else
            {
                ErrCount++;
                TASKSLEEP_MILLISECONDS(500);
            }
        }

        if (ErrCount < 30)
        {
            PRTDRV_PRINTF("Set Output_FanLowSpeed Sucessful\n");
            return tOK;
        }
        else
        {
            PRTDRV_PRINTF("Set Output_FanLowSpeed Failed\n");
            return tERROR;
        }
    }
}

tStatus LShell_SetOutput_LDHeateOn(void *pData)
{

    stDrvMsgCommRespBuf commRespMSG = {0};

    prtdrv_M3doCommand(ENGCMD_SetOutput_LDHeateOn, pData, NULL);
    msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_SetOutput_LDHeateOn, 0);

    if ((commRespMSG.mBuf[0] >> 8) & 0xFF)
    {
        PRTDRV_PRINTF("Set Output_LDHeateOn Sucessful\n");
        return tOK;
    }
    else
    {
        Uint8 ErrCount = 0;
        PowerSave_EngWakeup_And_Normalmode();
        TASKSLEEP_MILLISECONDS(500);
        while (ErrCount < 30)
        {
            prtdrv_M3doCommand(ENGCMD_SetOutput_LDHeateOn, pData, NULL);
            msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_SetOutput_LDHeateOn, 0);

            if ((commRespMSG.mBuf[0] >> 8) & 0xFF)
            {
                break;
            }
            else
            {
                ErrCount++;
                TASKSLEEP_MILLISECONDS(500);
            }
        }

        if (ErrCount < 30)
        {
            PRTDRV_PRINTF("Set Output_LDHeateOn Sucessful\n");
            return tOK;
        }
        else
        {
            PRTDRV_PRINTF("Set Output_LDHeateOn Failed\n");
            return tERROR;
        }
    }
}

tStatus LShell_SetOutput_FuserHeater(void *pData)
{

    stDrvMsgCommRespBuf commRespMSG = {0};

    prtdrv_M3doCommand(ENGCMD_SetOutput_FuserHeater, pData, NULL);
    msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_SetOutput_FuserHeater, 0);

    if ((commRespMSG.mBuf[0] >> 8) & 0xFF)
    {
        PRTDRV_PRINTF("Set Output_FuserHeater Sucessful\n");
        return tOK;
    }
    else
    {
        Uint8 ErrCount = 0;
        PowerSave_EngWakeup_And_Normalmode();
        TASKSLEEP_MILLISECONDS(500);
        while (ErrCount < 30)
        {
            prtdrv_M3doCommand(ENGCMD_SetOutput_FuserHeater, pData, NULL);
            msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_SetOutput_FuserHeater, 0);

            if ((commRespMSG.mBuf[0] >> 8) & 0xFF)
            {
                break;
            }
            else
            {
                ErrCount++;
                TASKSLEEP_MILLISECONDS(500);
            }
        }

        if (ErrCount < 30)
        {
            PRTDRV_PRINTF("Set Output_FuserHeater Sucessful\n");
            return tOK;
        }
        else
        {
            PRTDRV_PRINTF("Set Output_FuserHeater Failed\n");
            return tERROR;
        }
    }
}

tStatus LShell_SetOutput_ChargeBias(void *pData)
{

    stDrvMsgCommRespBuf commRespMSG = {0};

    prtdrv_M3doCommand(ENGCMD_SetOutput_ChargeBias, pData, NULL);
    msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_SetOutput_ChargeBias, 0);

    if ((commRespMSG.mBuf[0] >> 8) & 0xFF)
    {
        PRTDRV_PRINTF("Set Output_ChargeBias Sucessful\n");
        return tOK;
    }
    else
    {
        Uint8 ErrCount = 0;
        PowerSave_EngWakeup_And_Normalmode();
        TASKSLEEP_MILLISECONDS(500);
        while (ErrCount < 30)
        {
            prtdrv_M3doCommand(ENGCMD_SetOutput_ChargeBias, pData, NULL);
            msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_SetOutput_ChargeBias, 0);

            if ((commRespMSG.mBuf[0] >> 8) & 0xFF)
            {
                break;
            }
            else
            {
                ErrCount++;
                TASKSLEEP_MILLISECONDS(500);
            }
        }

        if (ErrCount < 30)
        {
            PRTDRV_PRINTF("Set Output_ChargeBias Sucessful\n");
            return tOK;
        }
        else
        {
            PRTDRV_PRINTF("Set Output_ChargeBias Failed\n");
            return tERROR;
        }
    }
}

tStatus LShell_SetOutput_DevelopBias(void *pData)
{

    stDrvMsgCommRespBuf commRespMSG = {0};

    prtdrv_M3doCommand(ENGCMD_SetOutput_DevelopBias, pData, NULL);
    msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_SetOutput_DevelopBias, 0);

    if ((commRespMSG.mBuf[0] >> 8) & 0xFF)
    {
        PRTDRV_PRINTF("Set Output_DevelopBias Sucessful\n");
        return tOK;
    }
    else
    {
        Uint8 ErrCount = 0;
        PowerSave_EngWakeup_And_Normalmode();
        TASKSLEEP_MILLISECONDS(500);
        while (ErrCount < 30)
        {
            prtdrv_M3doCommand(ENGCMD_SetOutput_DevelopBias, pData, NULL);
            msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_SetOutput_DevelopBias, 0);

            if ((commRespMSG.mBuf[0] >> 8) & 0xFF)
            {
                break;
            }
            else
            {
                ErrCount++;
                TASKSLEEP_MILLISECONDS(500);
            }
        }

        if (ErrCount < 30)
        {
            PRTDRV_PRINTF("Set Output_DevelopBias Sucessful\n");
            return tOK;
        }
        else
        {
            PRTDRV_PRINTF("Set Output_DevelopBias Failed\n");
            return tERROR;
        }
    }
}

tStatus LShell_SetOutput_TransCurrent(void *pData)
{

    stDrvMsgCommRespBuf commRespMSG = {0};

    prtdrv_M3doCommand(ENGCMD_SetOutput_TransCurrent, pData, NULL);
    msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_SetOutput_TransCurrent, 0);

    if ((commRespMSG.mBuf[0] >> 8) & 0xFF)
    {
        PRTDRV_PRINTF("Set Output_TransCurrent Sucessful\n");
        return tOK;
    }
    else
    {
        Uint8 ErrCount = 0;
        PowerSave_EngWakeup_And_Normalmode();
        TASKSLEEP_MILLISECONDS(500);
        while (ErrCount < 30)
        {
            prtdrv_M3doCommand(ENGCMD_SetOutput_TransCurrent, pData, NULL);
            msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_SetOutput_TransCurrent, 0);

            if ((commRespMSG.mBuf[0] >> 8) & 0xFF)
            {
                break;
            }
            else
            {
                ErrCount++;
                TASKSLEEP_MILLISECONDS(500);
            }
        }

        if (ErrCount < 30)
        {
            PRTDRV_PRINTF("Set Output_TransCurrent Sucessful\n");
            return tOK;
        }
        else
        {
            PRTDRV_PRINTF("Set Output_TransCurrent Failed\n");
            return tERROR;
        }
    }
}

tStatus LShell_SetOutput_PolygonMotor(void *pData)
{

    stDrvMsgCommRespBuf commRespMSG = {0};

    prtdrv_M3doCommand(ENGCMD_SetOutput_PolygonMotor, pData, NULL);
    msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_SetOutput_PolygonMotor, 0);

    if ((commRespMSG.mBuf[0] >> 8) & 0xFF)
    {
        PRTDRV_PRINTF("Set Output_PolygonMotor Sucessful\n");
        return tOK;
    }
    else
    {
        Uint8 ErrCount = 0;
        PowerSave_EngWakeup_And_Normalmode();
        TASKSLEEP_MILLISECONDS(500);
        while (ErrCount < 30)
        {
            prtdrv_M3doCommand(ENGCMD_SetOutput_PolygonMotor, pData, NULL);
            msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_SetOutput_PolygonMotor, 0);

            if ((commRespMSG.mBuf[0] >> 8) & 0xFF)
            {
                break;
            }
            else
            {
                ErrCount++;
                TASKSLEEP_MILLISECONDS(500);
            }
        }

        if (ErrCount < 30)
        {
            PRTDRV_PRINTF("Set Output_PolygonMotor Sucessful\n");
            return tOK;
        }
        else
        {
            PRTDRV_PRINTF("Set Output_PolygonMotor Failed\n");
            return tERROR;
        }
    }
}

tStatus LShell_SetOutput_LD1HeateOn(void *pData)
{
    stDrvMsgCommRespBuf commRespMSG = {0};

    prtdrv_M3doCommand(ENGCMD_SetOutput_LD1HeateOn, pData, NULL);
    msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_SetOutput_LD1HeateOn, 0);

    if ((commRespMSG.mBuf[0] >> 8) & 0xFF)
    {
        PRTDRV_PRINTF("Set Output_LD1HeateOn Sucessful\n");
        return tOK;
    }
    else
    {
        Uint8 ErrCount = 0;
        PowerSave_EngWakeup_And_Normalmode();
        TASKSLEEP_MILLISECONDS(500);
        while (ErrCount < 30)
        {
            prtdrv_M3doCommand(ENGCMD_SetOutput_LD1HeateOn, pData, NULL);
            msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_SetOutput_LD1HeateOn, 0);

            if ((commRespMSG.mBuf[0] >> 8) & 0xFF)
            {
                break;
            }
            else
            {
                ErrCount++;
                TASKSLEEP_MILLISECONDS(500);
            }
        }

        if (ErrCount < 30)
        {
            PRTDRV_PRINTF("Set Output_LD1HeateOn Sucessful\n");
            return tOK;
        }
        else
        {
            PRTDRV_PRINTF("Set Output_LD1HeateOn Failed\n");
            return tERROR;
        }
    }
}

tStatus LShell_SetOutput_LD2HeateOn(void *pData)
{

    stDrvMsgCommRespBuf commRespMSG = {0};

    prtdrv_M3doCommand(ENGCMD_SetOutput_LD2HeateOn, pData, NULL);
    msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_SetOutput_LD2HeateOn, 0);

    if ((commRespMSG.mBuf[0] >> 8) & 0xFF)
    {
        PRTDRV_PRINTF("Set Output_LD2HeateOn Sucessful\n");
        return tOK;
    }
    else
    {
        Uint8 ErrCount = 0;
        PowerSave_EngWakeup_And_Normalmode();
        TASKSLEEP_MILLISECONDS(500);
        while (ErrCount < 30)
        {
            prtdrv_M3doCommand(ENGCMD_SetOutput_LD2HeateOn, pData, NULL);
            msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_SetOutput_LD2HeateOn, 0);

            if ((commRespMSG.mBuf[0] >> 8) & 0xFF)
            {
                break;
            }
            else
            {
                ErrCount++;
                TASKSLEEP_MILLISECONDS(500);
            }
        }

        if (ErrCount < 30)
        {
            PRTDRV_PRINTF("Set Output_LD2HeateOn Sucessful\n");
            return tOK;
        }
        else
        {
            PRTDRV_PRINTF("Set Output_LD2HeateOn Failed\n");
            return tERROR;
        }
    }
}

tStatus LShell_FusingSCReset()
{
    stDrvMsgCommRespBuf commRespMSG = {0};

    prtdrv_M3doCommand(ENGCMD_FusingSCReset, NULL, NULL);
    msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_FusingSCReset, 0);

    if ((commRespMSG.mBuf[0] >> 8) & 0xFF)
    {
        PRTDRV_PRINTF("FusingSCReset Sucessful\n");
        return tOK;
    }
    else
    {
        Uint8 ErrCount = 0;
        PowerSave_EngWakeup_And_Normalmode();
        TASKSLEEP_MILLISECONDS(500);
        while (ErrCount < 30)
        {
            prtdrv_M3doCommand(ENGCMD_FusingSCReset, NULL, NULL);
            msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_FusingSCReset, 0);

            if ((commRespMSG.mBuf[0] >> 8) & 0xFF)
            {
                break;
            }
            else
            {
                ErrCount++;
                TASKSLEEP_MILLISECONDS(500);
            }
        }

        if (ErrCount < 30)
        {
            PRTDRV_PRINTF("FusingSCReset Sucessful\n");
            return tOK;
        }
        else
        {
            PRTDRV_PRINTF("FusingSCReset Failed\n");
            return tERROR;
        }
    }
}

static int gUSBDisconnectFlag = 0;
int        clearUSBDisconnectFlag()
{
    gUSBDisconnectFlag = 0;
    return 0;
}

int setUSBDisconnectFlag()
{
    gUSBDisconnectFlag = 1;
    return 0;
}

int getUSBDisconnectFlag()
{
    return gUSBDisconnectFlag;
}

tStatus PRTDRV_USB_Reset()
{
    stDrvMsgDualCommBuf MSG_DualComm = {0};

    MSG_DualComm.mtype     = 1;
    MSG_DualComm.msg.msgId = PRTCOMM_RESETUSB;
    assert(msgsnd(msgQEngDualComm, &MSG_DualComm, sizeof(DRV_DualCommMsg), 0) == 0);

    return tOK;
}

int     gResetUSBFlag = 0;
tStatus PRTDRV_USBReset(tUint8 IsNeedSleep)
{

    stDrvMsgCommRespBuf commRespMSG = {0};

    if (IsNeedSleep != 0xFF)
    {
        setUSBDisconnectFlag();
    }

    gResetUSBFlag = 1;
    prtdrv_M3doCommand(ENGCMD_RESETUSB, &IsNeedSleep, NULL);
    msgrcv(msgQCommResponse, &commRespMSG, sizeof(commRespMSG.mBuf), DUALCOMM_RESETUSB, 0);
    gResetUSBFlag = 0;

    if ((commRespMSG.mBuf[0] >> 8) & 0xFF)
    {
        PRTDRV_PRINTF("USBReset Sucessful\n");
        return tOK;
    }
    else
    {
        PRTDRV_PRINTF("USBReset Failed\n");
        return tERROR;
    }
}

Sint32 PRTDRV_ServantTask(void *parm)
{
    DRV_ProcessMsg     processMsg  = {0};
    stDrvMsgProcessBuf MSG_Process = {0};
    stPrintMgrMsgBuf   MSG         = {0};
    stJobMgr_JobDscp  *pJob        = NULL;
    PM_ScriptDscp     *pPageScript = tNULL;
    stJobMgr_PageDscp *pPage       = tNULL;
    eJobMgr_JobType    jobType     = JOBMGR_PrintJob;
    eJobMgr_ColorSpace colorSpace  = JOBMGR_Color;
    eJobMgr_PaperSize  paperSize   = JOBMGR_A4L;
    eJobMgr_MediaType  paperType   = JOBMGR_Plain;
    tUint16            repId       = 0;
    tInt32             actual_size = 0, cancelJobId = 0;
    tBool              doRecovery = tFALSE, waitFirstJobAllPaperOut = tFALSE, waitFirstJob = tFALSE;
    PM_JobScript      *pJobScript_First = NULL, *pJobScript_ToCancel = NULL;
    tUint8             duplex = 0;

#if 0 //PRINTDEBUGINFO
    tChar* recvMsg[PRTDRV_MAXMSGID] =
    {
        "PRTDRV_CANCELJOB",
        "PRTDRV_RECORDPAGE",
        "PRTDRV_RECORDJOBERROR",
        "PRTDRV_DORECOVERY",
    };
#endif
    if (!PrintManager.virtualMode)
    {
#if (!TONER_WITH_CHIP || !DRUM_WITH_CHIP)
        Uint8 ResetFlag = 0;
#endif
#if (!TONER_WITH_CHIP)
        SetServ_GetCurrentMachineSetting(SETSERV_MACHINE_TonerResetFlag, (void *)&ResetFlag);
        if (ResetFlag == 1)
        {
            Engine_TonerStatus_Reset();
        }
#endif
#if (!DRUM_WITH_CHIP)
        SetServ_GetCurrentMachineSetting(SETSERV_MACHINE_DrumResetFlag, (void *)&ResetFlag);
        if (ResetFlag == 1)
        {
            Engine_OPC_Reset();
        }
#endif
    }
    PRTDRV_PRINTF("%s: Start.\n", __FUNCTION__);
    while (tTRUE)
    {
        actual_size = msgrcv(msgQServant, &MSG_Process, sizeof(DRV_ProcessMsg), 0, 0);
        assert(actual_size > 0);
        processMsg = MSG_Process.msg;

#if 0
        PRTDRV_PRINTF("DPipe: servantMsg[%d] = %s, @%d\n", processMsg.msgId, recvMsg[processMsg.msgId], BIOSTimerMS());
#endif
        switch (processMsg.msgId)
        {
            case PRTDRV_CANCELJOB:
                pJob         = (stJobMgr_JobDscp *)processMsg.para1;
                jobType      = JOBMGR_GetJobType(pJob);
                waitFirstJob = tFALSE;
                doRecovery   = tFALSE;
                cancelJobId  = JOBMGR_GetJobId(pJob);

                pJobScript_First = PrintMgr_GetFirstJobScript();
                assert(pJobScript_First != NULL);

                if (pJob == pJobScript_First->pJob)
                {
                    StatusServ_SetPrnStatusBit(STATUSSERV_PrintStatus, STATUSSERV_PRINT_CANCELING);
                }

                do {
                    pJobScript_ToCancel = PrintMgr_GetJobScript(pJob);

                    if (pJobScript_ToCancel == NULL) TASKSLEEP_MILLISECONDS(300);
                } while (pJobScript_ToCancel == NULL);

                if (pJob == pJobScript_First->pJob)
                    waitFirstJob = tTRUE;
                else
                {
                    TAILQ_FOREACH(pPageScript, &pJobScript_ToCancel->psList, entry)
                    {
                        if (pPageScript->dispatchedCnt > 0)
                        {
                            waitFirstJob = tTRUE;
                            doRecovery   = tTRUE;
                            break;
                        }
                    }
                }
#if PRINTDEBUGINFO
                PRTDRV_PRINTF("DPipe: PRTDRV_CANCELJOB, jobId = %d, waitFirstJob = %d @%d\n", cancelJobId, waitFirstJob, BIOSTimerMS());
#else
                PRTDRV_PRINTF("DPipe: Cancel jobId = %d, wait = %d\n", cancelJobId, waitFirstJob);
#endif
                while (waitFirstJob) //To be cancelled job is the header job, Wait for all pages finished, all fed paper out, or till error occurs
                {
                    if (pJobScript_First != PrintMgr_GetFirstJobScript()) //first job was removed
                    {
                        break;
                    }
                    //Job is finished, no any page
                    if (JOBMGR_GetJobHeadPage(pJobScript_First->pJob) == NULL && JOBMGR_IsJobWriting(pJobScript_First->pJob) == tFALSE)
                    {
                        if ((PrintManager.fedSheets > 0) && (PRTDRV_GetError() == 0))
                        {
                            TASKSLEEP_MILLISECONDS(300);
                            continue;
                        }
                        else
                        {
                            PRTDRV_PRINTF("tmp: head page = NULL\n");
                            break;
                        }
                    }

                    //Job is not finished, wait for checking in page
                    if (TAILQ_EMPTY(&pJobScript_First->psList))
                    {
                        TASKSLEEP_MILLISECONDS(300);
                        continue;
                    }

                    //Break if error except size error,
                    if (PRTDRV_GetError())
                    {
                        doRecovery = tTRUE;
                        if (PrintManager.fedSheets == 0)
                        {
                            break;
                        }
                    }

                    //To check whether any page is not out
                    waitFirstJobAllPaperOut = tFALSE;

                    // assert happens when cancel job meet no paper
                    if (PrintManager.fedSheets > 0)
                    {
                        TASKSLEEP_MILLISECONDS(300);
                        continue;
                    }

                    TAILQ_FOREACH(pPageScript, &pJobScript_First->psList, entry)
                    {
                        if ((pPageScript->paperOutCnt < pPageScript->fedCnt && !pPageScript->sizeErrorPaperOut) //paper is not out yet
                            || pPageScript->planeDoneCnt[JOBMGR_Black] < pPageScript->fedCnt)                   //data is not finished yet
                        {
                            if (pPageScript->drvPTA)
                                doRecovery = tTRUE;
                            else
                                waitFirstJobAllPaperOut = tTRUE;

                            break;
                        }
                    }

                    TASKSLEEP_MILLISECONDS(300);

                    if (!waitFirstJobAllPaperOut)
                    {
                        break;
                    }
                }

                PRTDRV_DoRecovery();

                while (pJobScript_ToCancel)
                {
                    pPage = JOBMGR_GetJobHeadPage(pJob);

                    if (pPage == NULL)
                    {
                        if (JOBMGR_IsJobWriting(pJob) || !pJobScript_ToCancel->endInputJob)
                        {
                            TASKSLEEP_MILLISECONDS(100);
                            continue;
                        }
                        else
                        {
                            PRTDRV_TryToRemoveJob(pJob);
                            break;
                        }
                    }

                    {
                        PRTDRV_RemoveJobPageScriptFromOrderList(pJobScript_ToCancel);

                        TAILQ_FOREACH(pPageScript, &pJobScript_ToCancel->psList, entry)
                        {
                            //PRTDRV_ConsumePageData(pPageScript);

                            //PRTDRV_RemovePageScriptFromOrderList(pPageScript);

                            PRTDRV_TryToRemovePageScript(pPageScript, tTRUE);
                        }
                    }

                    TASKSLEEP_MILLISECONDS(10);
                }

#if PRINTDEBUGINFO
                PRTDRV_PRINTF("DPipe: PRTDRV_CANCELJOB, send PRT_CANCELDONE cancelJobId=%d, @%d\n", cancelJobId, BIOSTimerMS());
#else
                PRTDRV_PRINTF("DPipe: JobId = %d cancelled\n", cancelJobId);
#endif
                MSG.mtype     = 1;
                MSG.msg.msgId = PRT_CANCELDONE;
                MSG.msg.para  = cancelJobId;
                assert(msgsnd(msgQDriver, &MSG, sizeof(PRT_MSG), 0) == 0);
                break;

            case PRTDRV_RECORDPAGE:

                jobType    = (eJobMgr_JobType)processMsg.para1;
                colorSpace = (eJobMgr_ColorSpace)processMsg.para2;
                repId      = processMsg.para3;
                paperSize  = (eJobMgr_PaperSize)processMsg.para4;
                paperType  = (eJobMgr_MediaType)processMsg.para5;
                duplex     = (eJobMgr_MediaType)processMsg.para6;

                PRTDRV_PRINTF("DPipe: ServantTask received RECORDPAGE, jobType=%d, colorSpace=%d, repId=%d, paperSize=%d, paperType = %d @%d\n",
                              jobType, colorSpace, repId, paperSize, paperType, BIOSTimerMS());

                PrtMgr_FlashPrintMeter(jobType, colorSpace, repId, paperSize, paperType, duplex);

                break;

            case PRTDRV_RECORDJOBERROR:

                PRTDRV_PRINTF("DPipe: ServantTask received RECORDJOBERROR @%d\n", BIOSTimerMS());

                pJob = PrintMgr_GetFirstJob();

                if (pJob && processMsg.para1 == RPT_JOB_RESULT_PAPER_JAM) RptMgr_SetErrToJobRec(pJob, RPT_JOB_RESULT_PAPER_JAM);

                break;

            case PRTDRV_DORECOVERY:
#if 0
            PRTDRV_PRINTF("DPipe: ServantTask received PRTDRV_DORECOVERY @%d\n", BIOSTimerMS());
#endif
                PRTDRV_DoRecovery();

                MSG.mtype     = 1;
                MSG.msg.msgId = PRT_RECOVERDONE;
                assert(msgsnd(msgQDriver, &MSG, sizeof(PRT_MSG), 0) == 0);
#if 0
            PRTDRV_PRINTF("DPipe: ServantTask sent PRT_RECOVERDONE @%d\n", BIOSTimerMS());
#endif
                break;

            default:
                assert(0);
                break;
        }
    }
    return 0;
}

#if (TestPagePrintingDemo && PROJ_3IN1) //add for demo
Osif_MsgQId TestPagePrintingDemoMsgQ;

void PRTDRV_TestPagePrintingDemoTask()
{
    Uint8  TestPagePrintmsg = 0;
    tInt32 iReceivedMsgSize = 0;

    while (tTRUE)
    {
        Osif_MsgQReceive(TestPagePrintingDemoMsgQ, (tInt32 *)&TestPagePrintmsg, sizeof(Uint8), &iReceivedMsgSize, OSIF_Suspend);
        PRTDRV_PRINTF("DPipe: SystemReady_msg=%d\n", TestPagePrintmsg);
        PRTDRV_PRINTF("DPipe: Start print Lenovo_Demo Page\n");
        KeyPressedDemo();
    }
}
#endif

/**
 * PRTDRV_PrtStartWarmingUp
 * send EC3 to engine
 * @param tVoid
 * @return
 */
tVoid PRTDRV_PrtStartWarmingUp(tUint8 size, tUint8 type)
{
    stJobMgr_JobDscp *job          = tNULL;
    tUint8            JobIndex     = 0;
    tUint8            JobDirection = 0;
#if 0 //NXDEMO
    return;
#endif
    job = JobMgr_GetExecutingJobs(&JobIndex, JobDirection, JOBMGR_UnknowJob);

    if (job && JOBMGR_IsJobDelete(job))
    {
        return;
    }

    if (PrintManager.virtualMode == tTRUE) return;

    if (StatusServ_GetEngStatusBit(STATUSSERV_EngWarmingUp, STATUSSERV_ENG_WARMING_UP))
    {
        //PRTDRV_PRINTF("DPipe: Engine is warming up, Ignore EC3 CMD\n");
        return;
    }

    if (PRTDRV_GetError())
    {
        //PRTDRV_PRINTF("DPipe: PRTDRV_GetError happened, Ignore EC3 CMD\n\n");
        return;
    }

    if (PrintManager.resolution != JOBMGR_DPI600x600)
    {
        PrintManager.resolution = JOBMGR_DPI600x600;
        SwitchVideoPll(0);
    }

    ParSendWarmingUpCMD2Engine(size, type);
    PRTDRV_PRINTF("TIMESTAMP: Preparing Print.\n");
    PRTDRV2_Send_PFC(0, 0);

    PrintManager.EngineSendEC3 = tTRUE;
}

/**
 * PRTDRV_CheckCopyJobCanBeCanceledOrNot(tVoid)
 * @param tVoid
 * @return---tTRUE: can be canceled
               ---tFALSE: cannot be canceled.
 */

tBool PRTDRV_CheckCopyJobCanBeCanceledOrNot(tVoid)
{
    if (PrintManager.EngineSendEC3 == tTRUE)
    {
        return tFALSE;
    }
    else
    {
        return tTRUE;
    }
}

int par_JamCodeRequiry(Uint32 *jamCodeData32)
{
    Uint16        data16 = 0, i = 0;
    unsigned char jamCodeData[7];

    for (i = 1; i <= 4; i++) jamCodeData[i] = (jamCodeData32[0] >> ((i - 1) * 8)) & 0xFF;

    for (i = 5; i <= 6; i++) jamCodeData[i] = (jamCodeData32[1] >> ((i - 5) * 8)) & 0xFF;

    StatusServ_GetEngStatus(STATUSSERV_EngJam, &data16);

    for (i = 2; i <= 6; i++)
    {
        if (jamCodeData[i])
        {
            PRTDRV_PRINTF("JAMCodeInquiry: i=%d, jamCode=0x%x\n", i, jamCodeData[i]);
        }

        switch (jamCodeData[i])
        {
            case 0x01:
#if 1
                if ((data16 & STATUSSERV_ENG_JAM_REG_ON_EARLY) == 0) RptMgr_SaveErrorInHistory(SM_InitializeJam);
#endif

                StatusServ_SetEngStatusBit(STATUSSERV_EngJam, STATUSSERV_ENG_JAM_REG_ON_EARLY); //Initialize Jam
                break;
            case 0x08:

#if 1
                if ((data16 & STATUSSERV_ENG_JAM_MISFEED & STATUSSERV_ENG_JAM_DUPLEX_ENTRY_NOT_REACH & STATUSSERV_ENG_JAM_DUPLEX_NO_FEED) == 0)
                    RptMgr_SaveErrorInHistory(SM_NofeedJam);
#endif

                StatusServ_SetEngStatusBit(STATUSSERV_EngJam, STATUSSERV_ENG_JAM_MISFEED); //Nofeed jam
                break;
            case 0x39:
#if 1
                if ((data16 & STATUSSERV_ENG_JAM_REMAIN_AT_REG) == 0) RptMgr_SaveErrorInHistory(SM_RegistJam);
#endif

                StatusServ_SetEngStatusBit(STATUSSERV_EngJam, STATUSSERV_ENG_JAM_REMAIN_AT_REG); //Jam at Regist
                break;
            case 0x14:
#if 1
                if ((data16 & STATUSSERV_ENG_JAM_EXIT_OFF) == 0) RptMgr_SaveErrorInHistory(SM_FuseJam);
#endif

                StatusServ_SetEngStatusBit(STATUSSERV_EngJam, STATUSSERV_ENG_JAM_EXIT_OFF); //Jam at fuser
                break;
            case 0x3C:
#if 1
                if ((data16 & STATUSSERV_ENG_JAM_REMAIN_AT_EXIT) == 0) RptMgr_SaveErrorInHistory(SM_ExitJam);
#endif

                StatusServ_SetEngStatusBit(STATUSSERV_EngJam, STATUSSERV_ENG_JAM_REMAIN_AT_EXIT); //Jam at exit
                break;

            case 0x1A:
#if 1
                if ((data16 & STATUSSERV_ENG_JAM_DUPLEX_ENTRY_NOT_REACH) == 0) RptMgr_SaveErrorInHistory(SM_DuplexEntryJam);
#endif

                StatusServ_SetEngStatusBit(STATUSSERV_EngJam, STATUSSERV_ENG_JAM_DUPLEX_ENTRY_NOT_REACH); //Paper doesn't reach duplex entry sensor

                break;

            case 0x09:
#if 1
                if ((data16 & STATUSSERV_ENG_JAM_DUPLEX_NO_FEED) == 0) RptMgr_SaveErrorInHistory(SM_DuplexNoFeedJam);
#endif

                StatusServ_SetEngStatusBit(STATUSSERV_EngJam, STATUSSERV_ENG_JAM_DUPLEX_NO_FEED); //Duplex tray NoFeed Jam

                break;
            default:
                break;
        }
    }

    return 0;
}

unsigned char valuePaperDelivery = 10, PaperEmptyNGFlag = 0;
void          par_OnStatusChanged(unsigned char SRCmd, unsigned char RxStatus)
{
    stPrintMgrMsgBuf   MSG         = {0};
    stDrvMsgProcessBuf MSG_process = {0};
    tUint8             data        = 0;
    tUint32            value       = 0;

    *par_Status[SRCmd].peng_InfoElement = RxStatus; /*Set the new status*/

    switch (SRCmd)
    {
        case CMD_SR0:
            //if (eng_Info.BasicStatus.OperatorCallError || eng_Info.BasicStatus.RequestFromEngine)
            //{
            //    PRTDRV_PRINTF("Par: eng_Info.BasicStatus.OperatorCallError or eng_Info.BasicStatus.RequestFromEngine 0x%02X\n",
            //        *((Uint8 *)&eng_Info.BasicStatus));
            //    break;
            //}
            if (valuePaperDelivery != eng_Info.BasicStatus.PaperDelivery)
            {
                if ((valuePaperDelivery == 1) && (eng_Info.BasicStatus.PaperDelivery == 0) && (PaperEmptyNGFlag == 1))
                {
                    stPrintMgrMsgBuf MSG      = {0};
                    unsigned char    sendData = 0, recvData = 0;
                    sendData = CMD_EC0;
                    par_SendRecvCmd(&sendData, &recvData, 1);
                    PaperEmptyNGFlag = 0;

                    MSG.mtype     = 1;
                    MSG.msg.msgId = PRT_MISPRINTCANCEL;
                    MSG.msg.para  = 0;
                    assert(msgsnd(msgQDriver, &MSG, sizeof(PRT_MSG), 0) == 0);
                }
                valuePaperDelivery = eng_Info.BasicStatus.PaperDelivery;
            }

            StatusServ_GetEngStatus4HW(&value);

            if (eng_Info.BasicStatus.ServiceCallError) //error detect
            {
                switch (*(tUint16 *)&PrintManager.SCCode)
                {
                    case 0: //warning Logging SC
                        return;
                    case 202: //Polygomotor on timeout error
                        if ((value & STATUSSERV_ENG_HWERR_POLYGO_ON_ERR) == 0) RptMgr_SaveErrorInHistory(SM_IsPolygoMotorOnTimeOutErr);
                        StatusServ_SetEngStatus4HW(STATUSSERV_ENG_HWERR_POLYGO_ON_ERR);
                        break;
                    case 203:
                        if ((value & STATUSSERV_ENG_HWERR_POLYGO_OFF_ERR) == 0) RptMgr_SaveErrorInHistory(SM_IsPolygoMotorOffTimeOutErr);
                        StatusServ_SetEngStatus4HW(STATUSSERV_ENG_HWERR_POLYGO_OFF_ERR);
                        break;
                    case 204:
                        if ((value & STATUSSERV_ENG_HWERR_POLYGO_LOCK_ERR) == 0) RptMgr_SaveErrorInHistory(SM_IsPolygoLockSignalErr);
                        StatusServ_SetEngStatus4HW(STATUSSERV_ENG_HWERR_POLYGO_LOCK_ERR);
                        break;
                    case 220:
                        if ((value & STATUSSERV_ENG_HWERR_BEAM_SYNC_ERR) == 0) RptMgr_SaveErrorInHistory(SM_BeamSynchronize_Err);
                        StatusServ_SetEngStatus4HW(STATUSSERV_ENG_HWERR_BEAM_SYNC_ERR);
                        break;
                    case 491:
                        if ((value & STATUSSERV_ENG_HWERR_BIAS_LEAK) == 0) RptMgr_SaveErrorInHistory(SM_IsBiasleakErr);
                        StatusServ_SetEngStatus4HW(STATUSSERV_ENG_HWERR_BIAS_LEAK);
                        break;
                    case 497:
                        if ((value & STATUSSERV_ENG_HWERR_MACHINE_THERMISTOR) == 0) RptMgr_SaveErrorInHistory(SM_IsMachineThermistorErr);
                        StatusServ_SetEngStatus4HW(STATUSSERV_ENG_HWERR_MACHINE_THERMISTOR);
                        break;
                    //case 500:
                    // StatusServ_SetEngStatusBit(STATUSSERV_EngHWError, STATUSSERV_ENG_HWERR_MAIN_MOTOR_ERR);
                    //break;
                    case 501:
                        if ((value & STATUSSERV_ENG_HWERR_PLATE_ACTION_ERR) == 0) RptMgr_SaveErrorInHistory(SM_IsPlateActionErr);
                        StatusServ_SetEngStatus4HW(STATUSSERV_ENG_HWERR_PLATE_ACTION_ERR);
                        break;
                    case 520:
                        if ((value & STATUSSERV_ENG_HWERR_MAIN_MOTOR_ERR) == 0) RptMgr_SaveErrorInHistory(SM_IsMainMotorErr);
                        StatusServ_SetEngStatus4HW(STATUSSERV_ENG_HWERR_MAIN_MOTOR_ERR);
                        break;
                    case 530:
                        if ((value & STATUSSERV_ENG_HWERR_MAIN_FAN_MOTOR_ERR) == 0) RptMgr_SaveErrorInHistory(SM_IsMainFanMotorErr);
                        StatusServ_SetEngStatus4HW(STATUSSERV_ENG_HWERR_MAIN_FAN_MOTOR_ERR);
                        break;
                    case 541:
                        if ((value & STATUSSERV_ENG_HWERR_FUSER_THERMISTOR) == 0) RptMgr_SaveErrorInHistory(SM_IsFuserThermistorErr);
                        StatusServ_SetEngStatus4HW(STATUSSERV_ENG_HWERR_FUSER_THERMISTOR);
                        break;
                    case 542:
                        if ((value & STATUSSERV_ENG_HWERR_FUSER_RELOAD) == 0) RptMgr_SaveErrorInHistory(SM_IsFuserReloadErr);
                        StatusServ_SetEngStatus4HW(STATUSSERV_ENG_HWERR_FUSER_RELOAD);
                        break;
                    case 543: //high temperature
                        if ((value & STATUSSERV_ENG_HWERR_HIGH_TEMPER_S) == 0) RptMgr_SaveErrorInHistory(SM_IsHighTempErrSoft);
                        StatusServ_SetEngStatus4HW(STATUSSERV_ENG_HWERR_HIGH_TEMPER_S);
                        break;
                    case 544: //high temperature
                        if ((value & STATUSSERV_ENG_HWERR_HIGH_TEMPER_H) == 0) RptMgr_SaveErrorInHistory(SM_IsHighTempErrHard);
                        StatusServ_SetEngStatus4HW(STATUSSERV_ENG_HWERR_HIGH_TEMPER_H);
                        break;
                    case 545:
                        if ((value & STATUSSERV_ENG_HWERR_FUSER_FULL_HEATER) == 0) RptMgr_SaveErrorInHistory(SM_IsFuserFullHeaterErr);
                        StatusServ_SetEngStatus4HW(STATUSSERV_ENG_HWERR_FUSER_FULL_HEATER);
                        break;
                    case 546:
                        if ((value & STATUSSERV_ENG_HWERR_FUSER_MISREPORT) == 0) RptMgr_SaveErrorInHistory(SM_IsFuserMisreportErr);
                        StatusServ_SetEngStatus4HW(STATUSSERV_ENG_HWERR_FUSER_MISREPORT);
                        break;
                    case 548:
                        if ((value & STATUSSERV_ENG_HWERR_FUSER_STANDARD) == 0) RptMgr_SaveErrorInHistory(SM_IsFuserStandardErr);
                        StatusServ_SetEngStatus4HW(STATUSSERV_ENG_HWERR_FUSER_STANDARD);
                        break;
                    case 549:
                        if ((value & STATUSSERV_ENG_HWERR_FUSER_45) == 0) RptMgr_SaveErrorInHistory(SM_IsFuser45Err);
                        StatusServ_SetEngStatus4HW(STATUSSERV_ENG_HWERR_FUSER_45);
                        break;
                    case 559:
                        if ((value & STATUSSERV_ENG_HWERR_FUSER_3TIMES) == 0) RptMgr_SaveErrorInHistory(SM_IsFuser3TimesJamErr);
                        StatusServ_SetEngStatus4HW(STATUSSERV_ENG_HWERR_FUSER_3TIMES);
                        break;
                    case 560:
                        if ((value & STATUSSERV_ENG_HWERR_FUSER_LOW_VOL) == 0) RptMgr_SaveErrorInHistory(SM_IsLowVoltageFuserReloadErr);
                        StatusServ_SetEngStatus4HW(STATUSSERV_ENG_HWERR_FUSER_LOW_VOL);
                        break;
                    //case 587:
                    //
                    //    StatusServ_SetEngStatus4HW(STATUSSERV_ENG_HWERR_MOTOR_THERMISTOR);
                    //  break;
                    //case 669://
                    //    RptMgr_SaveErrorInHistory(SM_IsEEPROMCommunicationErr);
                    //    StatusServ_SetEngStatus4HW(STATUSSERV_ENG_HWERR_FUSER_EEPROM);
                    //  break;
                    case 688:
                        if ((value & STATUSSERV_ENG_HWERR_MOTOR_CTL_SIGNAL_ERR) == 0) RptMgr_SaveErrorInHistory(SM_IsMotorCTLSignalErr);
                        StatusServ_SetEngStatus4HW(STATUSSERV_ENG_HWERR_MOTOR_CTL_SIGNAL_ERR);
                        break;
                    case 990:
                        if ((value & STATUSSERV_ENG_HWERR_FAIL_SAFE_ERR) == 0) RptMgr_SaveErrorInHistory(SM_IsFailSafeErrorErr);
                        StatusServ_SetEngStatus4HW(STATUSSERV_ENG_HWERR_FAIL_SAFE_ERR);
                        break;
                    default:
                        PRTDRV_PRINTF("Par: SC%d", *(tUint16 *)&PrintManager.SCCode);
                        break;
                }

                MSG.mtype     = 1;
                MSG.msg.msgId = PRT_ENGINE_ERROR;
                MSG.msg.para  = CMD_SR0;
                assert(msgsnd(msgQDriver, &MSG, sizeof(PRT_MSG), 0) == 0);
            }
            break;
        case CMD_SR19:
            if (*(unsigned char *)&eng_Info.RemainingPaper)
            {
                StatusServ_SetEngStatusBit(STATUSSERV_EngError, STATUSSERV_ENG_ERR_PAPERJAM);

                MSG.mtype     = 1;
                MSG.msg.msgId = PRT_JAM;
                assert(msgsnd(msgQDriver, &MSG, sizeof(PRT_MSG), 0) == 0);

                MSG_process.mtype     = 1;
                MSG_process.msg.msgId = PRTDRV_RECORDJOBERROR;
                MSG_process.msg.para1 = RPT_JOB_RESULT_PAPER_JAM;
                assert(msgsnd(msgQServant, &MSG_process, sizeof(DRV_ProcessMsg), 0) == 0);
            }
            else
            {
                tUint16 stauts_tmp = STATUSSERV_ENG_NO_JAM;
                par_JamCancelSetting();
                StatusServ_SetEngStatus(STATUSSERV_EngJam, (tVoid *)&stauts_tmp);
                StatusServ_ClrEngStatusBit(STATUSSERV_EngError, STATUSSERV_ENG_ERR_PAPERJAM);
            }
            break;
        case CMD_SR34: //overheat mode
            if (eng_Info.WaitDetailStatus.OverHeat)
            {
                StatusServ_SetEngStatusBit(STATUSSERV_EngError, STATUSSERV_ENG_ERR_COOLDOWN);

                MSG.mtype     = 1;
                MSG.msg.msgId = PRT_COOLDOWN;
                MSG.msg.para  = 0;
                assert(msgsnd(msgQDriver, &MSG, sizeof(PRT_MSG), 0) == 0);
            }
            else
                StatusServ_ClrEngStatusBit(STATUSSERV_EngError, STATUSSERV_ENG_ERR_COOLDOWN);

            if (eng_Info.WaitDetailStatus.WarmingUp)
            {
                StatusServ_SetEngStatusBit(STATUSSERV_EngWarmingUp, STATUSSERV_ENG_WARMING_UP);
            }
            else
            {
                StatusServ_ClrEngStatusBit(STATUSSERV_EngWarmingUp, STATUSSERV_ENG_WARMING_UP);
            }

            break;

        case CMD_SR36: //misprint
            if (eng_Info.MisprintDetailStatus.MismatchPaperSize)
            {
                data = STATUSSERV_PRINT_SIZE_ERROR;
                StatusServ_SetPrnStatus(STATUSSERV_PrintErr, &data);

                MSG.mtype     = 1;
                MSG.msg.msgId = PRT_SIZEERROR;
                MSG.msg.para  = CMD_SR36;
                assert(msgsnd(msgQDriver, &MSG, sizeof(PRT_MSG), 0) == 0);
            }
            else if (eng_Info.MisprintDetailStatus.PaperEmptyNG)
            {
                if (eng_Info.BasicStatus.PaperDelivery == 1)
                {
                    PaperEmptyNGFlag = 1;
                }
                else
                {
                    stPrintMgrMsgBuf MSG      = {0};
                    unsigned char    sendData = 0, recvData = 0;
                    sendData = CMD_EC0;
                    par_SendRecvCmd(&sendData, &recvData, 1);

                    MSG.mtype     = 1;
                    MSG.msg.msgId = PRT_MISPRINTCANCEL;
                    MSG.msg.para  = 0;
                    assert(msgsnd(msgQDriver, &MSG, sizeof(PRT_MSG), 0) == 0);
                }
            }
            else
            {
                StatusServ_GetPrnStatus(STATUSSERV_PrintErr, &data);
                data &= ~STATUSSERV_PRINT_SIZE_ERROR;
                StatusServ_SetPrnStatus(STATUSSERV_PrintErr, &data);
            }
            break;

        case CMD_SR37: //Near End of K Toner, warning
            if (eng_Info.WarningDetailStatus1.NearEndofKToner)
                StatusServ_SetEngStatusBit(STATUSSERV_EngKTonerInf, STATUSSERV_ENG_TONER_NEARLY_EMPTY);
            else
                StatusServ_ClrEngStatusBit(STATUSSERV_EngKTonerInf, STATUSSERV_ENG_TONER_NEARLY_EMPTY);

            if (eng_Info.WarningDetailStatus1.NearEndofKOPC)
                StatusServ_SetEngStatusBit(STATUSSERV_EngDrumInf, STATUSSERV_ENG_DRUM_CARTRIDGE_PREPARE_REPLACEMENT);
            else
                StatusServ_ClrEngStatusBit(STATUSSERV_EngDrumInf, STATUSSERV_ENG_DRUM_CARTRIDGE_PREPARE_REPLACEMENT);

            break;

        case CMD_SR39: //End of K Toner, warning
            if (eng_Info.WarningDetailStatus3.EndofKToner)
            {
                StatusServ_SetEngStatusBit(STATUSSERV_EngKTonerInf, STATUSSERV_ENG_TONER_EMPTY);
            }
            else
            {
                StatusServ_ClrEngStatusBit(STATUSSERV_EngKTonerInf, STATUSSERV_ENG_TONER_EMPTY);
            }
            break;
        case CMD_SR60: //End of Waste Toner Box

            PRTDRV_PRINTF("End of K-OPC! %d\n\n", eng_Info.LifeEndStatus.EndofKOPC);

            if (eng_Info.LifeEndStatus.EndofKOPC)
            {
                RptMgr_SaveErrorInHistory(SM_OPCEnd);
                StatusServ_SetEngStatusBit(STATUSSERV_EngDrumInf, STATUSSERV_ENG_DRUM_CARTRIDGE_LIFE_END);

                MSG.mtype     = 1;
                MSG.msg.msgId = PRT_TONEREVENT;
                MSG.msg.para  = CMD_SR60;
                assert(msgsnd(msgQDriver, &MSG, sizeof(PRT_MSG), 0) == 0);
            }
            else
            {
                StatusServ_ClrEngStatusBit(STATUSSERV_EngDrumInf, STATUSSERV_ENG_DRUM_CARTRIDGE_LIFE_END);
            }
            break;
        case CMD_SR48:
            if (eng_Info.CoverOpenStatus.CoverOpen)
            {
                StatusServ_SetEngStatusBit(STATUSSERV_EngCoverOpen, STATUSSERV_ENG_SIDE1);
                StatusServ_SetEngStatusBit(STATUSSERV_EngError, STATUSSERV_ENG_ERR_COVEROPEN);

                MSG.mtype     = 1;
                MSG.msg.msgId = PRT_COVEROPEN;
                MSG.msg.para  = CMD_SR48;
                assert(msgsnd(msgQDriver, &MSG, sizeof(PRT_MSG), 0) == 0);
            }
            else
            {
                par_GetCurrentTonerPageCounter(tTRUE);
                par_GetCurrentTonerDotCounter(tTRUE);
                //par_GetWasteTonerFullStatus(tTRUE);
                par_GetCurrentOPCPageCounter(tTRUE);
                par_GetCurrentOPCDotCounter(tTRUE);

                StatusServ_ClrEngStatusBit(STATUSSERV_EngCoverOpen, STATUSSERV_ENG_SIDE1);
                StatusServ_ClrEngStatusBit(STATUSSERV_EngError, STATUSSERV_ENG_ERR_COVEROPEN);
            }
            break;
        case CMD_SR49: //AIO error detect
            if (eng_Info.UnitInstallationStatus.AIO)
            {
                StatusServ_ClrEngStatusBit(STATUSSERV_EngKTonerInf, STATUSSERV_ENG_TONER_NEARLY_EMPTY);
                StatusServ_ClrEngStatusBit(STATUSSERV_EngKTonerInf, STATUSSERV_ENG_TONER_EMPTY);
                //StatusServ_ClrEngStatusBit(STATUSSERV_EngKTonerInf, STATUSSERV_ENG_TONER_WASTE_TONER_FULL);
                StatusServ_ClrEngStatusBit(STATUSSERV_EngDrumInf, STATUSSERV_ENG_DRUM_CARTRIDGE_LIFE_END);
                //clear bits for error clear
                *(Uint8 *)&eng_Info.WarningDetailStatus1 = 0;
                *(Uint8 *)&eng_Info.WarningDetailStatus3 = 0;
                *(Uint8 *)&eng_Info.LifeEndStatus        = 0;

#if 0
                data = 0;
                StatusServ_SetEngStatus(STATUSSERV_EngKTonerLifeCnt, &data);
#endif
                StatusServ_SetEngStatusBit(STATUSSERV_EngKTonerInf, STATUSSERV_ENG_TONER_MISS);

                MSG.mtype     = 1;
                MSG.msg.msgId = PRT_TONEREVENT;
                MSG.msg.para  = CMD_SR49;
                assert(msgsnd(msgQDriver, &MSG, sizeof(PRT_MSG), 0) == 0);
            }
            else if (eng_Info.UnitInstallationStatus.CRUM)
            {
                StatusServ_ClrEngStatusBit(STATUSSERV_EngKTonerInf, STATUSSERV_ENG_TONER_NEARLY_EMPTY);
                StatusServ_ClrEngStatusBit(STATUSSERV_EngKTonerInf, STATUSSERV_ENG_TONER_EMPTY);
                //StatusServ_ClrEngStatusBit(STATUSSERV_EngKTonerInf, STATUSSERV_ENG_TONER_WASTE_TONER_FULL);
                StatusServ_ClrEngStatusBit(STATUSSERV_EngDrumInf, STATUSSERV_ENG_DRUM_CARTRIDGE_LIFE_END);
                //clear bits for error clear
                *(Uint8 *)&eng_Info.WarningDetailStatus1 = 0;
                *(Uint8 *)&eng_Info.WarningDetailStatus3 = 0;
                *(Uint8 *)&eng_Info.LifeEndStatus        = 0;
#if 0
                data = 0;
                StatusServ_SetEngStatus(STATUSSERV_EngKTonerLifeCnt, &data);
#endif
                StatusServ_SetEngStatusBit(STATUSSERV_EngKTonerInf, STATUSSERV_ENG_TONER_CRUM_ID_WARNING);

                MSG.mtype     = 1;
                MSG.msg.msgId = PRT_TONEREVENT;
                MSG.msg.para  = CMD_SR49;
                assert(msgsnd(msgQDriver, &MSG, sizeof(PRT_MSG), 0) == 0);
            }
            else
            {
                StatusServ_ClrEngStatusBit(STATUSSERV_EngKTonerInf, STATUSSERV_ENG_TONER_MISS);
                StatusServ_ClrEngStatusBit(STATUSSERV_EngKTonerInf, STATUSSERV_ENG_TONER_CRUM_ID_WARNING);
            }
        default:
            break;
    }
}

Uint8  verIOT[9]      = {0, 0x2E, 0, 0, 0, 0, 0, 0, 0};
Uint8  IOTCodeVer[15] = {0};
Sint32 PRTDRV_EngCommTask(void *parm)
{
    tInt32            actual_size = 0;
    unsigned char     SRCmd = 0, value = 0, statusType = 0; /*0-Byte, 1-set engine status, 2-Bit Status Changed*/
    eStatusServ_EngId engStatusId = 0;
    stEngStatus       engStatus   = {0};

    PRTDRV_PRINTF("%s: Start.\n", __FUNCTION__);
    while (tTRUE)
    {
        actual_size = msgrcv(msgQEngComm, &engStatus, sizeof(engStatus.status), 0, 0);
        statusType  = engStatus.mtype;

        switch (engStatus.mtype)
        {
            case ENGSTATUS_SRCmd:
            {
                SRCmd = (engStatus.status[0] >> 8) & 0xFF;
                value = (engStatus.status[0] >> 16) & 0xFF;
#if 0
            PRTDRV_PRINTF("Sky: SRCmd=SR%d, value=0x%x\n", SRCmd, value);
#endif
                par_OnStatusChanged(SRCmd, value);
            }
            break;

            case ENGSTATUS_EngStatus:
            {
                engStatusId = (engStatus.status[0] >> 8) & 0xFF;
                value       = (engStatus.status[0] >> 16) & 0xFF;
#if 0
            PRTDRV_PRINTF("Sky: engStatusId=%d, value=0x%x\n", engStatusId, value);
#endif

                StatusServ_SetEngStatus(engStatusId, &value);
            }
            break;

            case ENGSTATUS_EngStatusBit:
            {
                engStatusId = (engStatus.status[0] >> 8) & 0xFF;
                value       = (engStatus.status[0] >> 16) & 0xFF;
#if 1
                PRTDRV_PRINTF("Sky: engStatusId.=%d, value=0x%x, setOrClear=%d\n", engStatusId, value, (engStatus.status[0] >> 24));

#endif
                if ((engStatus.status[0] >> 24))
                {
#if 1
                    if ((engStatusId == STATUSSERV_EngReady) && (value == STATUSSERV_IREADY))
                    {

                        PRTDRV_PRINTF("TIMESTAMP: ENG Ready\n");
#if 0
            	   par_GetCurrentTonerLifeCnt(tTRUE);
								 par_GetCurrentTonerDotCounter(tTRUE);
								 par_GetCurrentOPCPageCounter(tTRUE);
#endif
                    }
#endif
                    StatusServ_SetEngStatusBit(engStatusId, value);
                }
                else
                {
                    StatusServ_ClrEngStatusBit(engStatusId, value);
                }
            }
            break;

            case ENGSTATUS_IOTVersion:
            {
                extern char *UI_Version_Get(void);
                verIOT[0] = (engStatus.status[0] >> 8) & 0xFF;
                verIOT[2] = (engStatus.status[0] >> 16) & 0xFF;
                verIOT[3] = (engStatus.status[0] >> 24) & 0xFF;

                verIOT[4] = (engStatus.status[1]) & 0xFF;
                verIOT[5] = (engStatus.status[1] >> 8) & 0xFF;
                verIOT[6] = (engStatus.status[1] >> 16) & 0xFF;
                verIOT[7] = (engStatus.status[1] >> 24) & 0xFF;
#if SCANNER_EN
                sprintf((char *)IOTCodeVer, "%s-%s", verIOT, UI_Version_Get());
#else
                sprintf((char *)IOTCodeVer, "%s", verIOT);
#endif
                StatusServ_SetCodeVersion(IOT_Code_Version, IOTCodeVer);
            }
            break;

            case ENGSTATUS_SCCode:
            {
                PrintManager.SCCode = (((engStatus.status[0] >> 16) & 0xFF) << 8) | ((engStatus.status[0] >> 8) & 0xFF);
            }
            break;

            case ENGSTATUS_TonerPageCnt:
            {
                tonerPageCnt = engStatus.status[1];
            }
            break;

            case ENGSTATUS_TonerDotCnt:
            {
                tonerDotCnt = engStatus.status[1];
            }
            break;

            case ENGSTATUS_OPCPageCnt:
            {
                OPCPageCnt = engStatus.status[1];
            }
            break;
            case ENGSTATUS_OPCDotCnt:
            {
                OPCDotCnt = engStatus.status[1];
            }
            break;
            case ENGSTATUS_PARStopLoop:
            {
                EngineReadyInSleepMode = 2;
            }
            break;
            case ENGSTATUS_ENGInSleep:
            {
                EngineAllowShutdown = tTRUE;
            }
            break;
            default:
                break;
        }
    }

    return 0;
}

Sint32 PRTDRV_EngDualCommTask(void *parm)
{
    DRV_DualCommMsg dualCommMsg = {0};

    stDrvMsgDualCommBuf MSG = {0};

    tInt32 actual_size = 0;

    PRTDRV_PRINTF("%s: Start.\n", __FUNCTION__);
    while (tTRUE)
    {
        actual_size = msgrcv(msgQEngDualComm, &MSG, sizeof(DRV_DualCommMsg), 0, 0);
        assert(actual_size > 0);
        dualCommMsg = MSG.msg;

        switch (dualCommMsg.msgId)
        {
#if (!TONER_WITH_CHIP)
            case PRTCOMM_RESETTONER:
            {
                tUint8 data = 0;
                while (1)
                {
                    StatusServ_GetEngStatus(STATUSSERV_EngError, &data);
                    if (data & STATUSSERV_ENG_ERR_COVEROPEN)
                    {
                        TASKSLEEP_MILLISECONDS(500);
                    }
                    else
                    {
                        break;
                    }
                }
                TonerStatus_Reset();
            }
            break;
#endif

#if (!DRUM_WITH_CHIP)
            case PRTCOMM_RESETDRUM:
            {
                tUint8 data = 0;

                while (1)
                {
                    StatusServ_GetEngStatus(STATUSSERV_EngError, &data);
                    if (data & STATUSSERV_ENG_ERR_COVEROPEN)
                    {
                        TASKSLEEP_MILLISECONDS(500);
                    }
                    else
                    {
                        break;
                    }
                }
                OPC_Reset();
            }
            break;
#endif
            case PRTCOMM_RESETUSB:
            {
                extern tStatus PRTDRV_USBReset(tUint8 IsNeedSleep);
                PRTDRV_USBReset(0xFF);
            }
            break;
            default:
                break;
        }
    }

    return 0;
}

/**
 * @fn      Sint32 PRTDRV_PushBandTask()
 * @brief   This task will push pageScript and bands in orderList to M3.
 * @param
 * @return  N/A.
 * Barton
 * 2021.3.16
 */
Sint32 PRTDRV_PushBandTask(void *parm)
{
    tInt32            actual_size = 0;
    stPageBandTaskMsg taskMsg;
    stPageBandMsg     pageBandMsg;
    BANDINFO         *pBand       = tNULL;
    PM_ScriptDscp    *pPageScript = NULL;
    eJobMgr_CMYK      colorId = JOBMGR_Black, colorStartId = JOBMGR_Cyan, colorEndId = JOBMGR_Black;
    tUint8            isFirstBand[JOBMGR_MAXCOLORID], isLastBand[JOBMGR_MAXCOLORID];
    Uint32           *pBandAddress = NULL, *pPBandStart = NULL;
    int               rv;
    Uint32            args[30];
    int               nretargs;
    Uint32            response;
    Uint32            bandcnt = 0;

    PRTDRV_PRINTF("%s: Start.\n", __FUNCTION__);
    while (tTRUE)
    {
        actual_size = msgrcv(msgQPushPageBand, &taskMsg, sizeof(stPageBandMsg), 0, 0);

        pageBandMsg = taskMsg.pageBandMsg;

        switch (pageBandMsg.msgId)
        {
            case PAGEBAND_NEWPAGESCRIPT:
                pPageScript = pageBandMsg.pPageScript;

                if (JOBMGR_GetPageColorSpace(pPageScript->pPage) == JOBMGR_Color || JOBMGR_GetPageColorSpace(pPageScript->pPage) == JOBMGR_Color2RGB)
                {
                    colorStartId = JOBMGR_Cyan;
                    colorEndId   = (eJobMgr_CMYK)(JOBMGR_MAXCOLORID - 1);
                }
                else
                {
#if DUAL_BEAM
                    colorStartId = JOBMGR_Cyan;
                    colorEndId   = JOBMGR_Magenta;
#else
                    colorStartId = colorEndId = JOBMGR_Black;
#endif
                }

                for (colorId = colorStartId; colorId <= colorEndId; colorId++)
                {
                    isFirstBand[colorId] = tTRUE;
                    isLastBand[colorId]  = tFALSE;
                }

                bandcnt = 0;
				

                while (tTRUE)
                {
                    for (colorId = colorStartId; colorId <= colorEndId; colorId++)
                    {
                        pBand = (BANDINFO *)bufB_DataGet(colorId, pPageScript->pPrintBuf);

                        args[0] = colorMapping_PrtMgrToChannel[colorId]; //dmaCh
                        args[0] |= pBand->isLastBand << 8;
                        args[0] |= isFirstBand[colorId] << 16;

                        if ((JOBMGR_GetPageCodec(pBand->pPageScript->pPage) == JOBMGR_RAW && PrintManager.rawModeRAW))
                        {
                            tUint8 *pVIP1Buf    = (tUint8 *)MemServ_GetWorkingBufPtr(MemServ_IP1Buf);
                            tUint8 *pPIP1Buf    = (tUint8 *)MemServ_GetPWorkingBufPtr(MemServ_IP1Buf);
                            tUint8 *pVReportBuf = (tUint8 *)MemServ_GetWorkingBufPtr(MemServ_ReportBuf);
                            tUint8 *pPReportBuf = (tUint8 *)MemServ_GetPWorkingBufPtr(MemServ_ReportBuf);
                            pBandAddress        = (Uint32 *)*pBand->bandStart;

                            //PSPRINTF("PRTDRV_PushBandTask: pBandAddress=0x%lx, pVReportBuf = 0x%lx\n", pBandAddress, pVReportBuf);

                            if (((tUint8 *)pBandAddress >= pVIP1Buf)
                                && ((tUint8 *)pBandAddress < pVIP1Buf + MemServ_GetWorkingBufSize(MemServ_IP1Buf)))
                            {
                                pPBandStart = (tUint32 *)(pPIP1Buf + ((tUint8 *)pBandAddress - pVIP1Buf));
                            }
                            else if (((tUint8 *)pBandAddress >= pVReportBuf)
                                     && ((tUint8 *)pBandAddress < pVReportBuf + MemServ_GetWorkingBufSize(MemServ_ReportBuf)))
                            {
                                pPBandStart = (tUint32 *)(pPReportBuf + ((tUint8 *)pBandAddress - pVReportBuf));
                            }
                            else
                                assert(0);
                        }
                        else if (JOBMGR_GetPageCodec(pBand->pPageScript->pPage) == JOBMGR_PDL_DISPLAYLIST && PrintManager.rawModePDL)
                        {
                            tUint8 *pVRAWBuf = (tUint8 *)MemServ_GetWorkingBufPtr(MemServ_SecurePrintBuf);
                            tUint8 *pPRAWBuf = (tUint8 *)MemServ_GetPWorkingBufPtr(MemServ_SecurePrintBuf);

                            pBandAddress = (Uint32 *)*pBand->bandStart;
                            if (((tUint8 *)pBandAddress >= pVRAWBuf)
                                && ((tUint8 *)pBandAddress < pVRAWBuf + MemServ_GetWorkingBufSize(MemServ_SecurePrintBuf)))
                            {
                                pPBandStart = (tUint32 *)(pPRAWBuf + ((tUint8 *)pBandAddress - pVRAWBuf));
                            }
                            else
                                assert(0);
                        }
                        else
                        {
                            tUint8 *pVBandBuf = (tUint8 *)MemServ_GetWorkingBufPtr(MemServ_PrintBuf);
                            tUint8 *pPBandBuf = (tUint8 *)MemServ_GetPWorkingBufPtr(MemServ_PrintBuf);

                            pBandAddress = pBand->bandStart;
#if 1							
            if (DATA_MASK==0)
			{
				if (isFirstBand[colorId])
				{
					PSPRINTF("PRTDRV_PushBandTask: all band set to 0!!!\n");
				}
				memset((tUint8 *)pBandAddress, 0, pBand->dataSizeBytes);
			}
			else if (DATA_MASK==1)
			{
				if (isFirstBand[colorId])
				{
					PSPRINTF("PRTDRV_PushBandTask: all band set to 1!!!\n");
				}
				memset((tUint8 *)pBandAddress, 0xFF, pBand->dataSizeBytes);
			}
			else//normal
			{
				
			}
#endif
#if 0
                    	if (isFirstBand[colorId])
                    	{
                    		memset((tUint8 *)pBandAddress, 0xFF, pBand->dataSizeBytes);
                    		//memset((tUint8 *)pBandAddress, 0xAA, pBand->dataSizeBytes);
                    	}                    	
                    	if (pBand->isLastBand)
                      {
                      	memset((tUint8 *)pBandAddress, 0xFF, pBand->dataSizeBytes);
                      }
#endif

                            if (((tUint8 *)pBandAddress >= pVBandBuf)
                                && ((tUint8 *)pBandAddress < pVBandBuf + MemServ_GetWorkingBufSize(MemServ_PrintBuf)))
                            {
                                pPBandStart = (tUint32 *)(pPBandBuf + ((tUint8 *)pBandAddress - pVBandBuf));
                            }
                            else
                            {
                                assert(0);
                            }
                        }
#if 0            
                    PSPRINTF("PRTDRV_PushBandTask: pBand=0x%lx, pPBandStart = 0x%lx\n", pBand, pPBandStart);
#endif
                        args[1] = (Uint32)((tUint64)pBand & 0x00000000ffffffff);
                        args[2] = (Uint32)((tUint64)pPBandStart & 0x00000000ffffffff);
                        args[3] = (pBand->dataSizeBytes / pBand->dataLines); //bytesperline
                        args[3] |= pBand->dataLines << 16;
                        args[4] = (Uint32)(((tUint64)pBand & 0xffffffff00000000) >> 32);
						
#if 0    
                    PSPRINTF("MBOX_DATA: args[1]=0x%x, args[4] = 0x%x\n", args[1], args[4]);
#endif
                     if (isFirstBand[colorId]||pBand->isLastBand)
					 {
                       PSPRINTF("PRTDRV_PushBandTask:  bandcnt=%d pBand=0x%lx, pPBandStart = 0x%lx\n", bandcnt, pBand, pPBandStart);
                        PSPRINTF("                      high=0x%x,low=0x%x ,first=%d,last=%d\n", args[4], args[1], isFirstBand[colorId],
						              pBand->isLastBand);
					 }

#if 0 //NXDEMO
                        PSPRINTF("PRTDRV_PushBandTask:  bandcnt=%d pBand=0x%lx, pPBandStart = 0x%lx\n", bandcnt, pBand, pPBandStart);
                        PSPRINTF("                      high=0x%x,low=0x%x ,first=%d,last=%d\n", args[4], args[1], isFirstBand[colorId],
                                 pBand->isLastBand);

                        args[5] = (Uint32)(bandcnt);
                        rv      = M3doCommand_demo(PRINT_PUSH_BAND, 6, &response, &nretargs, args);
#else
	                    args[5] = (Uint32)(bandcnt);
                        rv = M3doCommand(PRINT_PUSH_BAND, 6, &response, &nretargs, args);
#endif
                        bandcnt++;
                        isLastBand[colorId] = pBand->isLastBand;

                        if (isFirstBand[colorId]) isFirstBand[colorId] = tFALSE;
                    }
#if DUAL_BEAM
                    if (isLastBand[colorEndId])
                    {
                        PSPRINTF("PRTDRV_PushBandTask: DD colorid=%d isLastBand=%d\n", colorEndId, isLastBand[colorEndId]);
                        break;
                    }
#else
                    if (isLastBand[colorEndId])
                    {
#if PRINT_USELASTBANDFEED
                        PM_ScriptDscp* pPageScript =pBand->pPageScript;
                        PSPRINTF("PRTDRV_PushBandTask: isLastBand enable feed page,colorid=%d\n",colorEndId);
                        PIS_InformPageReady(pBand->pPageScript, colorEndId);
#else
#endif
                        bandcnt = 0;
                        break;
                    }
#endif
                    TASKSLEEP_MILLISECONDS(1);
                }

                break;

            case PAGEBAND_NEWBAND:

                break;

            default:
                break;
        }
    }

    return 0;
}

/**
 * @fn      void PRTDRV_PrtMgrTask()
 * @brief   This task will be started by receiving events.
 * @param
 * @return  N/A.
 * @note:   This task handles all printing events
 * Barton
 * 2013.6.28
 */
Sint32 PRTDRV_PrtDriverTask(void *parm)
{
    PRT_MSG            prtMsg        = {0};
    stPrintMgrMsgBuf   MSG           = {0};
    stDrvMsgProcessBuf MSG_process   = {0};
    tBool              bNeedRecovery = (tBool)PRTDRV_GetError(), bRecovering = tFALSE, bCancelling = tFALSE, requestToFeed = tTRUE;
    tInt32         i = 0, actual_size = 0, timeout = 0, onebandMs = 50 /*60000 / 600 * 128 / (11 * 18) * 1000 / TS_OS_Ticks_Per_Second*/, dotSums = 0;
    tUint16        delayForUserCancelNextJob = 0, delayAfterCancel = 6, dotCnt = 1;
    tUint16        jamStatus                     = 0;
    PM_ScriptDscp *pPageScript                   = tNULL;
    stJobMgr_JobDscp       *pJob                 = tNULL;
    PM_JobScript           *pJobScript           = NULL;
    PM_PageScriptOrderList *pPageScriptOrderList = NULL;
    unsigned char           sendData[5] = {0}, recvData[3] = {0, 0, 0};

#if PRINTDEBUGINFO
    tChar *recvMsg[PRT_MAXSTATE] = {
        "PRT_DMADONE_0",
        "PRT_DMADONE_1",
        "PRT_DMADONE_2",
        "PRT_DMADONE_3",
        "PRT_WAITING",
        "PRT_PFCREQ",
        "PRT_PFA",
        "PRT_PFA2",
        "PRT_FEED",
        "PRT_PFNA",
        "PRT_PTA",
        "PRT_PPOUT",
        "PRT_PPOUT2",
        "PRT_NEWPAGE",
        "PRT_PAGEREADY",
        "PRT_NEWJOB",
        "PRT_ENDINPUTJOB",
        "PRT_ENDOUTPUTJOB",
        "PRT_TESTPRINT",
        "PRT_PAPER_EMPTY",
        "PRT_SIZEERROR",
        "PRT_JAM",
        "PRT_COVEROPEN",
        "PRT_TONEREVENT",
        "PRT_ENGINE_ERROR",
        "PRT_BAND_ERROR",
        "PRT_DMA_ISR_ERROR",
        "PRT_COOLDOWN",
        "PRT_RECOVERDONE",
        "PRT_CANCELJOB",
        "PRT_CANCELDONE",
        "PRT_DOTCNT",
        "PRT_ENGIREADYNOT_VALID",
        "PRT_ERRSIMULA",
        "PRT_REQUESTFAXPAGE",
        "PRT_OUTOFMEMORY",
        "PRT_RESETTONER",
        "PRT_RESETDRUM",
        "PRT_ENGINE_READY",
        "PRT_MISPRINTCANCEL",
        "PRT_FGATE",
#if CFG_Authorization
        "PRT_DRAMCHECKFail",
#endif
    };
#endif

    PRTDRV_PRINTF("%s: Start.\n", __FUNCTION__);
    while (tTRUE)
    {
        prtMsg.msgId = PRT_WAITING;
        prtMsg.para  = 0;

        if (timeout == 0)
        {
            actual_size = msgrcv(msgQDriver, &MSG, sizeof(PRT_MSG), 0, 0);
            assert(actual_size > 0);
            prtMsg = MSG.msg;
        }
        else
        {

            for (i = 0; i < timeout; i++)
            {
                actual_size = msgrcv(msgQDriver, &MSG, sizeof(PRT_MSG), 0, IPC_NOWAIT);
                if (actual_size == -1)
                {
                    usleep(1000);
                }
                else
                {
                    prtMsg = MSG.msg;
                    break;
                }
            }
        }
        if (PrintManager.virtualMode) bNeedRecovery = tFALSE;

        if (actual_size != -1)
        {
#if PRINTDEBUGINFO
            if (prtMsg.msgId != PRT_FGATE)
            {
                PRTDRV_PRINTF("DPipe: prtMsg = %s, para = %d, timeout = %d, @%d\n", recvMsg[prtMsg.msgId], prtMsg.para, timeout, BIOSTimerMS());
            }
#endif
        }
        else
        {
            if (delayForUserCancelNextJob > 0)
            {
                if (delayForUserCancelNextJob++ > delayAfterCancel) delayForUserCancelNextJob = 0;
            }
        }

        switch (prtMsg.msgId)
        {
            case PRT_NEWJOB:
                PRTDRV_NewJob(prtMsg.pJob);
                timeout = onebandMs * 2;

                break;

            case PRT_ENDINPUTJOB:

                pJobScript = PrintMgr_GetJobScript(prtMsg.pJob);
                //assert(pJobScript);
                if (pJobScript == NULL)
                {
                    PRTDRV_PRINTF("DPipe: job script is NULL.\n");
                    break;
                }

                pJobScript->endInputJob = tTRUE;

                if (bCancelling) break;

                if (JOBMGR_GetJobTotalPage(prtMsg.pJob) == 0
                    || (JOBMGR_IsJobWriting(prtMsg.pJob) == tFALSE && JobMgr_GetJobHeadPrintPage(prtMsg.pJob) == NULL))
                {
                    PRTDRV_TryToRemoveJob(prtMsg.pJob);
                }

                break;

            case PRT_PPOUT2:

                PRTDRV_PaperOut(prtMsg.para);
                break;

            case PRT_DOTCNT:

                if (bNeedRecovery || bRecovering || PrintManager.virtualMode) break;

                dotSums = prtMsg.para;

                if (dotSums <= 500)
                    dotCnt = 1;
                else
                    dotCnt = (dotSums + 500) / 1000;

                sendData[0] = CMD_EEC59;
                sendData[1] = dotCnt & 0x00ff;
                sendData[2] = (dotCnt >> 8) & 0x00ff;
                par_SendRecvCmd(sendData, recvData, 3);

#if LENOVO_PROJECT
                par_UpdateTonerInfo_IncDotCntInHistory(dotCnt);
#endif

                break;

            case PRT_PFA:
                break;

            case PRT_PFA2:
                break;

            case PRT_FEED:

                PrintManager.feedTime = BIOSTimerMS();
                StatusServ_SetPrnStatusBit(STATUSSERV_PrintStatus, STATUSSERV_PRINT_BUSY);
                break;

            case PRT_DMADONE_0:
            case PRT_DMADONE_1:
            case PRT_DMADONE_2:
            case PRT_DMADONE_3:
                PRTDRV_PageDMADone(prtMsg.pPageScript, prtMsg.msgId);
                break;

            case PRT_PAGEREADY:

                if (bNeedRecovery || bRecovering || bCancelling) break;

                delayForUserCancelNextJob = 0;

                if (requestToFeed == tFALSE) break;

                pPageScriptOrderList = PRTDRV_GetNextPageScriptInOrderList(PAGE_FEED, 0);

                if (pPageScriptOrderList == NULL) break;

                pPageScript = PRTDRV_FeedPage(pPageScriptOrderList, &bNeedRecovery);
#if  PRINT_USELASTBANDFEED
#else
                if (pPageScript != NULL) requestToFeed = tFALSE;
#endif			

                break;

            case PRT_PFCREQ:

                pPageScriptOrderList = PRTDRV_GetNextPageScriptInOrderList(PAGE_FEED, 0);

                if (pPageScriptOrderList)
                {
                    pPageScript = pPageScriptOrderList->pPageScript;
                }
                else
                {
                    pPageScript = NULL;
                }

                if (pPageScript && pPageScript->pPage->duplex != FRONT_SIDE_PAGE)
                {
                    if (bNeedRecovery) break;

                    if (PRTDRV_GetError() != 0) break;
                }

                if (PRTDRV_DecodeBandCntisEnough(pPageScript))
                {
                    pPageScript = PRTDRV_FeedPage(pPageScriptOrderList, &bNeedRecovery);
                }
                else
                {
                    pPageScript = NULL;
                }

                requestToFeed = (pPageScript == NULL) ? tTRUE : tFALSE;

                break;

            case PRT_FGATE:
                pJob = PrintMgr_GetFirstJob();
                if (PrintManager.rawModeRAW && pJob && (JOBMGR_GetJobType(pJob) == JOBMGR_CopyJob))
                {
                    PRTDRV_PRINTF("DPipe: prtMsg = %s, timeout = %d, @%d\n", recvMsg[PRT_FGATE], prtMsg.para, timeout, BIOSTimerMS());

                    pPageScriptOrderList = PRTDRV_GetNextPageScriptInOrderList(PAGE_FEED, 0);

                    if (pPageScriptOrderList)
                    {
                        pPageScript = pPageScriptOrderList->pPageScript;
                    }
                    else
                    {
                        pPageScript = NULL;
                    }

                    if (pPageScript && pPageScript->pPage->duplex != FRONT_SIDE_PAGE)
                    {
                        if (bNeedRecovery) break;

                        if (PRTDRV_GetError() != 0) break;
                    }

                    if (PRTDRV_DecodeBandCntisEnough(pPageScript))
                    {
                        pPageScript = PRTDRV_FeedPage(pPageScriptOrderList, &bNeedRecovery);
                    }
                    else
                    {
                        pPageScript = NULL;
                    }

                    requestToFeed = (pPageScript == NULL) ? tTRUE : tFALSE;
                }

                break;

            case PRT_PPOUT:

                PRTDRV_PaperOut(prtMsg.para);
                break;

            case PRT_CANCELJOB:

#if 1
                assert(semaphore_p(PrintManager.mutex_lock) == 1);
#else
                TSmutexWait(PrintManager.mutex_lock);
#endif

                pJobScript = PrintMgr_GetJobScript(prtMsg.pJob);

                MSG_process.mtype     = 1;
                MSG_process.msg.msgId = PRTDRV_CANCELJOB;
                MSG_process.msg.para1 = (tUint32)prtMsg.pJob;
                assert(msgsnd(msgQServant, &MSG_process, sizeof(DRV_ProcessMsg), 0) == 0);
                bCancelling = tTRUE;

#if 1
                assert(semaphore_v(PrintManager.mutex_lock) == 1);
#else
                TSmutexSignal(PrintManager.mutex_lock);
#endif

                break;
            case PRT_CANCELDONE:

                bCancelling = bRecovering = tFALSE;
                requestToFeed             = tTRUE;

                if (!PrintMgr_IsFirstJobPsListEmpty()) delayForUserCancelNextJob = 1;

                //PRTDRV_PRINTF("tmp: set C2E_SIGNAL_PRREQ = SIGNAL_INVALID\n");
                //PRTDRV_EngineNotifySignal(C2E_SIGNAL_PRREQ, SIGNAL_INVALID);
                bNeedRecovery = tTRUE;
#if DUAL_BEAM
                for (int j = 0; j < MAXDMACNT; j++)
                {
                    PrintManager.dmaWorking[j] = 0;
                }
#else
                PrintManager.dmaWorking = 0;
#endif
                PrintManager.recoveralbelTime = 0;
                break;
            case PRT_RECOVERDONE:
                bNeedRecovery                 = (tBool)PRTDRV_GetError();
                bRecovering                   = tFALSE;
                requestToFeed                 = tTRUE;
                PrintManager.recoveralbelTime = 0;

                PrintManager.resolution = JOBMGR_DPIMAX;
                PrintManager.duplex     = 0xFF;
                PrintManager.paperSize  = 0xFF;
                PrintManager.mediaType  = 0xFF;
                PrintManager.width      = 0xFFFF;
                PrintManager.length     = 0xFFFF;
#if DUAL_BEAM
                for (int j = 0; j < MAXDMACNT; j++)
                {
                    PrintManager.dmaWorking[j] = 0;
                }
#else
                PrintManager.dmaWorking = 0;
#endif
            case PRT_NEWPAGE:
            case PRT_WAITING:
                if (prtMsg.msgId == PRT_NEWPAGE)
                {
                    PRTDRV_AddPageScript(prtMsg.pPageScript);

                    pJobScript = PrintMgr_GetFirstJobScript();
                    assert(pJobScript);

                    if (prtMsg.pPageScript->pPage->pParent != pJobScript->pJob) break;
                }

                //if (bCancelling)
                //    break;
                if (bNeedRecovery)
                {
                    if (bRecovering)
                    {
                        break;
                    }

                    if (PrintManager.fedSheets > 0)
                    {
                        break;
                    }

                    if (PRTDRV_GetError() != 0)
                    {
                        pPageScript = PrintMgr_GetFirstPageScript();

                        if (pPageScript == NULL || pPageScript->dispatchedCnt == 0)
                        {
                            StatusServ_GetEngStatus(STATUSSERV_EngJam, &jamStatus);

                            if (jamStatus == STATUSSERV_ENG_JAM_MISFEED) PRTDRV2_Send_RESMISFED();
                        }
#if 1
                        pJobScript = PrintMgr_GetFirstJobScript();
                        if (pJobScript && pJobScript->pJob)
                        {
                            if ((PrintManager.recoveralbelTime == 0) && !(pJobScript->pJob->JobStatus & JOBMGR_JobDelete))
                            {
                                Uint8 jobAutoCancel = 0;
                                SetServ_GetCurrentPrnSetting(SETSERV_PRN_Layout, (void *)&jobAutoCancel);
#if defined(GM266DNS) || defined(GM268DNAS) || defined(G338DNS) || defined(GM339DNS) || defined(G263DNS)
                                if (!jobAutoCancel)
                                {
                                    //jobAutoCancel =1;
                                    //SetServ_SetCurrentPrnSetting(SETSERV_PRN_Layout, (void *)&jobAutoCancel);
                                    //PRTDRV_PRINTF("TCM type engine: Set jobAutoCancel= %d\n",jobAutoCancel);
                                    PRTDRV_PRINTF("TCM type engine: Get jobAutoCancel= %d\n", jobAutoCancel);
                                }
#endif
                                if (jobAutoCancel)
                                {
                                    PrintManager.recoveralbelTime = BIOSTimerMS();
                                    PRTDRV_PRINTF("DPipe: other Error and job wait timeout to cancel time=%d\n", PrintManager.recoveralbelTime);
                                }
                            }
                            else if (PrintManager.recoveralbelTime > 0 && (BIOSTimerMS() - PrintManager.recoveralbelTime > 60000))
                            {
                                PrintManager.recoveralbelTime = 0;
                                if (!(pJobScript->pJob->JobStatus & JOBMGR_JobDelete))
                                {
                                    PRTDRV_PRINTF("DPipe: Error occur auto job cancel %d\n", pJobScript->pJob->JobId);
                                    JobMgr_CancelJob(pJobScript->pJob);
                                }
                            }
                        }
#endif
                        break;
                    }

                    if (PrintMgr_IsFirstJobPsListEmpty())
                    {
                        bNeedRecovery = tFALSE;
                        break;
                    }

                    bRecovering           = tTRUE;
                    MSG_process.mtype     = 1;
                    MSG_process.msg.msgId = PRTDRV_DORECOVERY;
                    assert(msgsnd(msgQServant, &MSG_process, sizeof(DRV_ProcessMsg), 0) == 0);
                    break;
                }

                if (TAILQ_EMPTY(&PrintManager.jobList))
                {
                    timeout = 0;
                    break;
                }

                if (delayForUserCancelNextJob > 0)
                {
                    //PRTDRV_PRINTF("tmp: delayForUserCancelNextJob=%d\n", delayForUserCancelNextJob);
                    break;
                }

                pPageScript = PRTDRV_DispatchPageScript();

                if (pPageScript)
                {
                    if (pPageScript->pPage->duplex == BACK_SIDE_PAGE) pPageScript = PRTDRV_DispatchPageScript();
                }

                timeout = onebandMs * 2;
                break;

            case PRT_TESTPRINT:
                break;

            case PRT_MISPRINTCANCEL:
                if (PrintManager.fedSheets > 0)
                {
                    PrintManager.fedSheets = 0;
                }
                break;
            default:
                if (PRTDRV_IOTError(prtMsg.msgId, prtMsg.para))
                {
                    //StatusServ_ClrPrnStatusBit(STATUSSERV_PrintStatus, STATUSSERV_PRINT_BUSY);
                    bNeedRecovery = tTRUE;
                    timeout       = onebandMs * 2;
                }

                break;
        }
    }
    return 0;
}

#if PURE_VIRTUAL_MODE
#define Band_DBDstChecksum_Enable 0
Sint32 PRTDRV_VirtualDriverTask(void *parm)
{
    tInt32                actual_nByte = 0, i = 0;
    tUint8                recv_msg[msgENGERR_SIZE] = {0}, cmd = 0;
    stPrtDrvVirtualMsgBuf MSG_Virtual = {0};
    PM_ScriptDscp        *pPageScript = NULL;
    BANDINFO             *pBand       = NULL;
    eJobMgr_CMYK          colorId = JOBMGR_Cyan, colorStartId = JOBMGR_Cyan, colorEndId = JOBMGR_Black;
    tBool                 planeDone[4] = {tFALSE, tFALSE, tFALSE, tFALSE};

    stPrintMgrMsgBuf MSG   = {0};
    int              sleep = 0;
#if Band_DBDstChecksum_Enable
    tUint32 k = 0, dstChecksum[2] = {0};
    tUint8 *dstBandBuf = NULL;
    tUint8  j          = 0;
#endif

    PRTDRV_PRINTF("%s: Start.\n", __FUNCTION__);
    while (tTRUE)
    {
        cmd          = 0;
        actual_nByte = msgrcv(msgVirtualEng, &MSG_Virtual, msgENGERR_SIZE, 0, 0);
        assert(actual_nByte > 0);

        memcpy(recv_msg, MSG_Virtual.virtualBuf, actual_nByte);

        cmd = recv_msg[1];

        switch (cmd)
        {
            case CONSUMEPAGE:

                if (pPageScript == NULL) memcpy((void *)&pPageScript, &recv_msg[2], sizeof(PM_ScriptDscp *));

                if (JOBMGR_GetPageColorSpace(pPageScript->pPage) == JOBMGR_Color || JOBMGR_GetPageColorSpace(pPageScript->pPage) == JOBMGR_Color2RGB)
                {
                    colorStartId             = JOBMGR_Cyan;
                    colorEndId               = (eJobMgr_CMYK)(JOBMGR_MAXCOLORID - 1);
                    planeDone[JOBMGR_Yellow] = planeDone[JOBMGR_Magenta] = planeDone[JOBMGR_Cyan] = planeDone[JOBMGR_Black] = tFALSE;
                }
                else
                {
                    colorStartId = colorEndId = JOBMGR_Black;

#if DUAL_BEAM
                    colorStartId             = JOBMGR_Cyan;
                    colorEndId               = JOBMGR_Magenta;
                    planeDone[JOBMGR_Yellow] = planeDone[JOBMGR_Black] = tTRUE;
#else
                    colorStartId = colorEndId = JOBMGR_Black;
                    planeDone[JOBMGR_Yellow] = planeDone[JOBMGR_Magenta] = planeDone[JOBMGR_Cyan] = tTRUE;
#endif
                }

                while (tTRUE)
                {
#if Band_DBDstChecksum_Enable
                    dstChecksum[0] = dstChecksum[1] = 0;
#endif
                    for (colorId = colorStartId; colorId <= colorEndId; colorId++)
                    {
                        pBand = (BANDINFO *)bufB_DataGet(colorId, pPageScript->pPrintBuf);
                        assert(pBand);
#if Band_DBDstChecksum_Enable
                        dstBandBuf = (tUint8 *)pBand->bandStart;
                        for (k = 0; k < pBand->dataSizeBytes; k++)
                        {
                            dstChecksum[colorId] += *(dstBandBuf + k);
                        }
#endif
                        if (pBand->isLastBand) planeDone[pBand->colorId] = tTRUE;

                        bufB_DataUsed(pBand);
                    }
#if Band_DBDstChecksum_Enable
                    j++;
                    PRTDRV_PRINTF("Sky: j = %d, dstChecksum = 0x%x\n", j, (dstChecksum[0] + dstChecksum[1]));
#endif
                    if (planeDone[JOBMGR_Yellow] && planeDone[JOBMGR_Magenta] && planeDone[JOBMGR_Cyan] && planeDone[JOBMGR_Black])
                    {
                        MSG.mtype     = 1;
                        MSG.msg.msgId = PRT_PFCREQ;
                        MSG.msg.para  = 0;
                        assert(msgsnd(msgQDriver, &MSG, sizeof(PRT_MSG), 0) == 0);

                        PRTDRV_PaperOut(pPageScript->sheet);

                        MSG.mtype     = 1;
                        MSG.msg.msgId = PRT_WAITING;
                        MSG.msg.para  = 0;
                        assert(msgsnd(msgQDriver, &MSG, sizeof(PRT_MSG), 0) == 0);

                        pPageScript              = NULL;
                        planeDone[JOBMGR_Yellow] = planeDone[JOBMGR_Magenta] = planeDone[JOBMGR_Cyan] = planeDone[JOBMGR_Black] = tFALSE;

                        break;
                    }

                    if (sleep)
                    {
                        TASKSLEEP_MILLISECONDS(60 * 1000 / 18 / 54 / 2);
                    }
                }

                break;

            default:

                PRTDRV_PRINTF("DPipe: Virtual drv task received %d bytes: ", actual_nByte);

                for (i = 0; i < actual_nByte; i++) PRTDRV_PRINTF("%02x, ", recv_msg[i]);

                PRTDRV_PRINTF("\n");

                break;
        }
    }

    return 0;
}
#endif

/************ ********* ********* ********* ********* ***********
 *          ISR for Print driver
 ********* ********* ********* ********* ********* ********* **********/
tVoid PRTDRV_STOP_DMA_IMMEDIATELY(tUint8 dmaCh)
{
    int    rv;
    Uint32 args[30] = {0};
    int    nretargs;
    Uint32 response;

    args[0] = dmaCh;
#if 0
    Log(2, "STOP_DMA_IMMEDIATELY\n");
#endif
    rv = M3doCommand(PRINT_STOP_DMA_IMMEDIATELY, 1, &response, &nretargs, args);
}

void PRTDRV_DMADONE_ISR(tUint64 pBandFromM3)
{
    stPrintMgrMsgBuf  MSG         = {0};
    BANDINFO         *pBand       = (BANDINFO *)pBandFromM3;
    stJobMgr_JobDscp *pJob        = NULL;
    PM_ScriptDscp    *pPageScript = NULL;
    tBool             isLastBand  = tFALSE;
#if DUAL_BEAM
    eJobMgr_CMYK colorId = pBand->colorId;

    DRV_MSGID dmaCh = colorMapping_PrtMgrToChannel[colorId];
#else
    eJobMgr_CMYK colorId = JOBMGR_Black;
    DRV_MSGID    dmaCh   = PRT_DMADONE_0;
#endif
    tUint32 pfcREQCnt = 0, linesPerBand = 0, dataLength = 0;
#if 0 
    PRTDRV_PRINTF("PRTDRV_DMADONE_ISR pBandFromM3 =%lx\n", pBandFromM3);
#endif
    pPageScript = pBand->pPageScript;
    pJob        = JOBMGR_GetPageParent(pPageScript->pPage);
    isLastBand  = pBand->isLastBand;
#if DUAL_BEAM
    PrintManager.dmaDoneCnt[dmaCh]++;
#endif

#if DUAL_BEAM
    if (colorId == JOBMGR_Cyan)
#else
    if (colorId == JOBMGR_Black)
#endif
    {
#if !DUAL_BEAM
        PrintManager.dmaDoneCnt++;
#endif

        //if ((!PrintManager.rawModeRAW) || (JOBMGR_GetJobType(pJob) != JOBMGR_CopyJob))
        if (!(PrintManager.rawModeRAW && (JOBMGR_GetJobType(pJob) == JOBMGR_CopyJob)))
        {
            dataLength   = pPageScript->dataLength;
            linesPerBand = PIS_STD_BANDLINES;

            if (dataLength % linesPerBand != 0)
            {
                pfcREQCnt = (dataLength / linesPerBand + 1) / 3;
            }
            else
            {
                pfcREQCnt = (dataLength / linesPerBand) / 3;
            }

            if (pfcREQCnt == 0)
            {
                pfcREQCnt = 1;
            }

            if ((JOBMGR_GetPageCodec(pPageScript->pPage) == JOBMGR_RAW && isLastBand)
                || (JOBMGR_GetPageCodec(pPageScript->pPage) == JOBMGR_PDL_DISPLAYLIST && PrintManager.rawModePDL && isLastBand))
            {
#if DUAL_BEAM
                pfcREQCnt = PrintManager.dmaDoneCnt[dmaCh];
#else
                pfcREQCnt = PrintManager.dmaDoneCnt;
#endif
            }

#if DUAL_BEAM
            if (PrintManager.dmaDoneCnt[dmaCh] == pfcREQCnt /* && !pBand->isLastBand*/) //skip blank page.
#else
            if (PrintManager.dmaDoneCnt == pfcREQCnt /* && !pBand->isLastBand*/) //skip blank page.
#endif
            {
#if PRINT_USELASTBANDFEED				
#else
                MSG.mtype     = 1;
                MSG.msg.msgId = PRT_PFCREQ;
                MSG.msg.para  = pfcREQCnt;
                assert(msgsnd(msgQDriver, &MSG, sizeof(PRT_MSG), 0) == 0);
#endif				
            }
        }
    }
#if CFG_Authorization
    if (isLastBand)
    {
        PRTDRV_PRINTF("Clear Print Memory-pageId=%d:\n", pBand->pPageScript->pPage->PageId);

        memset(pBand->bandStart, 1, pBand->bandSizeBytes);
        PRTDRV_PRINTF("memset to 1\n");
        memset(pBand->bandStart, 0, pBand->bandSizeBytes);
        PRTDRV_PRINTF("memset to 0\n");
    }
#endif

    bufB_DataUsed(pBand);

    if (isLastBand)
    {
#if DUAL_BEAM
        PSPRINTF("PRTDRV_DMADONE_ISR DD colorId=%d dmach=%d dmadone=%d\n", colorId, dmaCh, PrintManager.dmaDoneCnt[dmaCh]);
        PrintManager.dmaWorking[dmaCh] = 0;
#else
        PrintManager.dmaWorking = 0;
#endif
        PRTDRV_STOP_DMA_IMMEDIATELY(dmaCh);
#if 0        
        MSG.mtype = 1;
        MSG.msg.msgId = dmaCh;
        MSG.msg.pPageScript = pPageScript;
        MSG.msg.para = pPageScript->pPage->PageId;

#if DUAL_BEAM
        MSG.msg.para = PrintManager.dmaDoneCnt[dmaCh];
#else
        if (dmaCh == 0)
            MSG.msg.para = PrintManager.dmaDoneCnt;
#endif
        //PRT_STOP_DMA_IMMEDIATELY(dmaCh);
        assert(msgsnd(msgQDriver, &MSG, sizeof(PRT_MSG), 0) == 0);
        //gpst4DmaSendingBand1[dmaCh] = NULL;
#else
        PRTDRV_PageDMADone(pPageScript, dmaCh);
#endif
        if (PrintManager.virtualMode)
        {
#if DUAL_BEAM
            if (colorId == JOBMGR_Magenta)
#else
            if (colorId == JOBMGR_Black)
#endif
            {
                MSG.mtype     = 1;
                MSG.msg.msgId = PRT_PPOUT;
                MSG.msg.para  = 0;
                assert(msgsnd(msgQDriver, &MSG, sizeof(PRT_MSG), 0) == 0);
            }
        }
#if 0 //NXDEMO
        MSG.mtype     = 1;
        MSG.msg.msgId = PRT_PPOUT;
        MSG.msg.para  = 0;
        assert(msgsnd(msgQDriver, &MSG, sizeof(PRT_MSG), 0) == 0);

#endif

        return;
    }

#if 0
    if (gpst4DmaSendingBand1[dmaCh] == NULL)
        return;

    if (gpst4DmaSendingBand1[dmaCh]->isLastBand)
        return;

    gpst4DmaSendingBand2[dmaCh] = pBand = (BANDINFO*) bufB_DataGet(colorId, pPageScript->pPrintBuf);

    if (pBand == NULL)
    {
        MSG.mtype = 1;
        MSG.msg.msgId = PRT_BAND_ERROR;
        MSG.msg.para = colorId;
        assert(msgsnd(msgQDriver,&MSG, sizeof(PRT_MSG), 0) == 0);
        return;
    }

    PRTDRV_ConfigDMA(pBand, tFALSE, dmaCh);
#endif
}

void PRTDRV_DMADONE_CONSUME(tUint64 pBandFromM3)
{
    PRTDRV_PRINTF("PRTDRV_DMADONE_CONSUME pBandFromM3 =0x%lx\n", pBandFromM3);
    if (pBandFromM3)
        bufB_DataUsed((BANDINFO *)pBandFromM3);
    else
        PRTDRV_SendFlushPageDone(0);
}

//--------------------- DMA error isr --------------------------
void PRTDRV_DMAERROR_ISR(int vector)
{
    stPrintMgrMsgBuf MSG = {0};

    ICU_InterruptClear(vector);

    MSG.mtype     = 1;
    MSG.msg.msgId = PRT_DMA_ISR_ERROR;
    MSG.msg.para  = vector;
    assert(msgsnd(msgQDriver, &MSG, sizeof(PRT_MSG), 0) == 0);
}

/************ ********* ********* ********* ********* ***********
 *          Driver's initialization
 ********* ********* ********* ********* ********* ********* **********/
/**
 * @fn       void PRTDRV_InterruptRegister(void)
 * @brief
 * @note     IRQ_PDD3 -- Cause by DMA3 task to be completed.
 IRQ_PDE3 -- When DMA3 task reports an error's event
 IRQ_LPRI3 -- Cause by laser channel 3 done event
 */

void PRTDRV_InterruptRegister(void)
{
#if 0
    tUint8  channel = 0;
    for (channel = 0; channel < 4; channel++)
    {
#if INFERNO53XX
        ICU_InterruptDisable(IRQ_PDD0 + channel * 3);
        ICU_InterruptDisable(IRQ_PDE0 + channel * 3);
        ICU_InterruptDisable(IRQ_LPRI + channel * 3);

        ICU_InterruptClear(IRQ_PDD0 + channel * 3);
        ICU_InterruptClear(IRQ_PDE0 + channel * 3);
        ICU_InterruptClear(IRQ_LPRI + channel * 3);
#else
        ICU_InterruptDisable(IRQ_PDD0 + channel);
        ICU_InterruptDisable(IRQ_PDE0 + channel);
        ICU_InterruptDisable(IRQ_LPRI + channel);

        ICU_InterruptClear(IRQ_PDD0 + channel);
        ICU_InterruptClear(IRQ_PDE0 + channel);
        ICU_InterruptClear(IRQ_LPRI + channel);
#endif

        switch (channel)
        {
            case 0: //As channel = 0
                TaskIsrRegister(IRQ_PDD0, PRTDRV_DMADONE_ISR, DMA_ISR_PRIORITY);
                TaskIsrRegister(IRQ_PDE0, PRTDRV_DMAERROR_ISR, DMA_ISR_PRIORITY);
                //TaskIsrRegister( IRQ_LPRI, PRTDRV_DMA0LaserDone_ISR, DMA_ISR_PRIORITY );
                //hisr_id_DMA0DONE = Osif_HisrCreate(PRTDRV_DMA0DONE_HISR/*Function*/, DMA_HISR_PRIORITY, PRTDRV_DMA_STACK_SIZE);
                break;
            case 1: //As channel =1
                TaskIsrRegister(IRQ_PDD1, PRTDRV_DMADONE_ISR, DMA_ISR_PRIORITY);
                TaskIsrRegister(IRQ_PDE1, PRTDRV_DMAERROR_ISR, DMA_ISR_PRIORITY);
                //TaskIsrRegister( IRQ_LPRI1, PRTDRV_DMA1LaserDone_ISR, DMA_ISR_PRIORITY );
                //hisr_id_DMA1DONE = Osif_HisrCreate(PRTDRV_DMA1DONE_HISR/*Function*/, DMA_HISR_PRIORITY, PRTDRV_DMA_STACK_SIZE);
                break;
            case 2: //As channel = 2
                TaskIsrRegister(IRQ_PDD2, PRTDRV_DMADONE_ISR, DMA_ISR_PRIORITY);
                TaskIsrRegister(IRQ_PDE2, PRTDRV_DMAERROR_ISR, DMA_ISR_PRIORITY);
                //TaskIsrRegister( IRQ_LPRI2, PRTDRV_DMA2LaserDone_ISR, DMA_ISR_PRIORITY );
                //hisr_id_DMA2DONE = Osif_HisrCreate(PRTDRV_DMA2DONE_HISR/*Function*/, DMA_HISR_PRIORITY, PRTDRV_DMA_STACK_SIZE);
                break;
            case 3: //As channel = 3
                TaskIsrRegister(IRQ_PDD3, PRTDRV_DMADONE_ISR, DMA_ISR_PRIORITY);
                TaskIsrRegister(IRQ_PDE3, PRTDRV_DMAERROR_ISR, DMA_ISR_PRIORITY);
                //TaskIsrRegister( IRQ_LPRI3, PRTDRV_DMA3LaserDone_ISR, DMA_ISR_PRIORITY );
                //hisr_id_DMA3DONE = Osif_HisrCreate(PRTDRV_DMA3DONE_HISR/*Function*/, DMA_HISR_PRIORITY, PRTDRV_DMA_STACK_SIZE);
                break;
        }
    }
#endif
}

/**
 * @fn       static int PRTDRV_Utilities_init()
 * @brief    init. function for creating semaphore, queue,etc.
 * @param    N/A.
 * @return   int -> PRTDRV_OK
 * @note     [Internal function]
 */
void PRTDRV_Utilities_init(void)
{
#if 0
    PAR_Utilities_init();
#endif

    msgQEngComm = msgget((key_t)PrintDrv_MSGKEY_EngComm, (IPC_CREAT | 0666));
    assert(msgQEngComm >= 0);

    msgQEngDualComm = msgget((key_t)PrintDrv_MSGKEY_EngDualComm, (IPC_CREAT | 0666));
    assert(msgQEngDualComm >= 0);

    msgQCommResponse = msgget((key_t)PrintDrv_MSGKEY_CommResp, (IPC_CREAT | 0666));
    assert(msgQCommResponse >= 0);

    msgQDriver = msgget((key_t)PrintDrv_MSGKEY_Driver, (IPC_CREAT | 0666));
    assert(msgQDriver >= 0);

    msgQServant = msgget((key_t)PrintDrv_MSGKEY_Servant, (IPC_CREAT | 0666));
    assert(msgQServant >= 0);

    msgQPushPageBand = msgget((key_t)PrintDrv_MSGKEY_PushPageBand, (IPC_CREAT | 0666));
    assert(msgQPushPageBand >= 0);

    msgQFlushPage = msgget((key_t)PrintDrv_MSGKEY_FlashPageBand, (IPC_CREAT | 0666));
    assert(msgQFlushPage >= 0);

#if 0
    //ISR -- Call function to regist the interrupts
    PRTDRV_InterruptRegister();

    fgateTimer = BIOStimerAlloc();
    ASSERT(fgateTimer >= 0);
#endif
}

void PRTDRV_InitVirtualTask(void)
{
#if PURE_VIRTUAL_MODE
#if 1
    pthread_t task_Virtual_DriverTask;
#else
    TsThread task_Virtual_DriverTask = INVALIDTHREAD;
#endif
#endif

    msgVirtualEng = msgget((key_t)PrintDrv_MSGKEY_VirtualEng, (IPC_CREAT | 0666));
    assert(msgVirtualEng >= 0);

#if PURE_VIRTUAL_MODE
#if 1
    pthread_create(&task_Virtual_DriverTask, 0, PRTDRV_VirtualDriverTask, (void *)0);
#else
    task_Virtual_DriverTask = TScreateThread(PRTDRV_VirtualDriverTask, PRTDRV_DRIVER_STACK_SIZE, PRINT_DRV_PRIORITY, NULL, "PRTDRV_VirtualDriver");
#endif
#endif
}

void PRTDRV_InitOK_SendAgain(void)
{
    int    rv;
    Uint32 args[30];
    int    nretargs;
    Uint32 response;
    rv = M3doCommand(PRINT_DRV_INIT_DONE, 1, &response, &nretargs, args);
}

/**
 * @fn       void PRTDRV_Init(tBool initFromSleepMode)
 * @brief    This function is used to create diver's tasks, and ensure driver no error before our driver init. ok.
 * @param    Uint32 - dummy
 * @return   N/A.
 * @note     [Internal function]
 */
void PRTDRV_Init(tBool initFromSleepMode)
{
#if 1
    pthread_t task_id_PRT_DriverTask;
#else
    TsThread task_id_PRT_DriverTask = INVALIDTHREAD;
#endif
    int    rv;
    Uint32 args[30];
    int    nretargs;
    Uint32 response;

#if 0
    TsThread task_id_PAR_ReadStatus = INVALIDTHREAD;
#endif

    if (initFromSleepMode) /*Init from Sleep mode*/
    {
        PSPRINTF("DPipe: transfering from sleep to normal mode\n");
#if 0
        TASKSLEEP_MILLISECONDS(1000);//(2000); //@20100510  to let engine become more stable (ex. seq bit will error.)
#endif
    }
    else /*Init from Power-On*/
    {
        PRTDRV_Utilities_init();
#if 0
        task_id_PAR_ReadStatus = TScreateThread(
                            PAR_ReadStatus,
                            PAR_ReadStatus_STACK_SIZE,
                            PRINT_PAR_PRIORITY,
                            NULL,
                            "PAR_ReadStatus"
                            );
#endif

        if (PrintManager.virtualMode)
        {
            PRTDRV_InitVirtualTask();
        }
#if 1
        pthread_create(&task_id_PRT_DriverTask, 0, PRTDRV_EngCommTask, (void *)0);
        pthread_create(&task_id_PRT_DriverTask, 0, PRTDRV_PrtDriverTask, (void *)0);
        pthread_create(&task_id_PRT_DriverTask, 0, PRTDRV_ServantTask, (void *)0);
        pthread_create(&task_id_PRT_DriverTask, 0, PRTDRV_PushBandTask, (void *)0);
        pthread_create(&task_id_PRT_DriverTask, 0, PRTDRV_EngDualCommTask, (void *)0);
#else
        //PRTDRV_InitPageScriptOrder();
        task_id_PRT_DriverTask = TScreateThread(PRTDRV_EngCommTask, PRTDRV_DRIVER_STACK_SIZE, PRINT_DRV_PRIORITY, NULL, "PRTDRV_EngComm");

        task_id_PRT_DriverTask = TScreateThread(PRTDRV_PrtDriverTask, PRTDRV_DRIVER_STACK_SIZE, PRINT_DRV_PRIORITY, NULL, "PRTDRV_PrtDriver");

        task_id_PRT_DriverTask = TScreateThread(PRTDRV_ServantTask, PRTDRV_DRIVER_STACK_SIZE, (PRINT_DRV_PRIORITY + 9), NULL, "PRTDRV_Servant");

        task_id_PRT_DriverTask = TScreateThread(PRTDRV_PushBandTask, PRTDRV_DRIVER_STACK_SIZE, (PRINT_DRV_PRIORITY + 9), NULL, "PRTDRV_PushBand");

        task_id_PRT_DriverTask =
            TScreateThread(PRTDRV_EngDualCommTask, PRTDRV_DRIVER_STACK_SIZE, (PRINT_DRV_PRIORITY + 9), NULL, "PRTDRV_EngDualComm");
#endif

#if 0
        PRTDRV_PRINTF("ttt: virtualMode = %d\n", PrintManager.virtualMode);
        args[0] = PrintManager.virtualMode;
        rv = M3doCommand(
          PRINT_VIRTUAL_MODE,
          1,
          &response,
          &nretargs,
          args
          );
#endif
        rv = M3doCommand(PRINT_DRV_INIT_DONE, 1, &response, &nretargs, args);
        PRTDRV_PRINTF("DPipe: init Done\n");
#if 0
        {
        	extern void PowerSave_wakeupdate_mode_set();
        	PowerSave_wakeupdate_mode_set();
        }
#endif
    }

#if 0
    PRTDRV_ResetPif_DmaIF(JOBMGR_MAXCOLORID, 1);
    PRTDRV_SetupPif_DmaTriggerIF();

    //signal inversion
    WRREG_UINT32(MFP_INVCTL1,((RDREG_UINT32(MFP_INVCTL1) & 0xFFFFFFFE) | 0x01));
#endif
#if 1
    PrintManager.inSleep = tFALSE;
    prtdrv_M3doCommand(ENGCMD_SLEEP_STATE, &PrintManager.inSleep, NULL);
#endif
    PrintManager.initOK           = tTRUE;
    PrintManager.jobContinuePrint = tTRUE;
    PrintManager.resolution       = JOBMGR_DPIMAX;
    PrintManager.duplex           = 0xFF;
    PrintManager.paperSize        = 0xFF;
    PrintManager.mediaType        = 0xFF;
    PrintManager.width            = 0xFFFF;
    PrintManager.length           = 0xFFFF;
#if 0
    {
    	char printLog[480] = {0};
    	unsigned char md5[20] =  {0};
    	unsigned char md51[20] =  {0};
    	int i = 0, r = 0;
    	extern int Md5_Hash_Calcute(void* in,Uint32 len,unsigned char* out);
    	memcpy(printLog, "Sky test Md5", strlen("Sky test Md5"));
    	Md5_Hash_Calcute(printLog, strlen(printLog), md5);
    	PRTDRV_PRINTF("Sky: len1=%d, len2=%d\n", strlen("Sky test Md5"), strlen(printLog));
    	for (i = 0; i<sizeof(md5); i++)
    	{
    		PRTDRV_PRINTF("Sky: md5[%d]=0x%x\n", i, *(md5+i));
    	}
    	
    	Md5_Hash_Calcute(printLog, strlen(printLog), md51);
    	for (i = 0; i<sizeof(md51); i++)
    	{
    		PRTDRV_PRINTF("Sky: md51[%d]=0x%x\n", i, *(md51+i));
    	}
    	
    	r = memcmp(md5, md51, 16);
    	
    	PRTDRV_PRINTF("Sky: r = %d\n", r);
    	
    	
    }
#endif
}

//extern int virtual_mode; //add this for ICE Debug
/**
 * @fn       void PRTDRV_SLEEP_ON(void)
 * @brief    This function will let the printer engine enter the sleep mode.
 * @param    N/A.
 * @return   N/A.
 * @note
 */

tBool PRTDRV_SLEEP_ON(void)
{
    unsigned char send_data = 0, recv_data = 0;
    Uint8         TryCnt = 0;
    if (PrintManager.virtualMode) return tTRUE;

#if 0
    if (PRTDRV_GetError() != 0)
    {
        PRTDRV_PRINTF("PRTDRV: EngineError = 0x%x\n", PRTDRV_GetError());
        return tFALSE;
    }
#endif

    EngineInSleepProcessing = tTRUE;

    send_data = CMD_EC30;
    par_SendRecvCmd(&send_data, &recv_data, 1);

    send_data = CMD_SR43;
    par_SendRecvCmd(&send_data, &recv_data, 1);

    while (TryCnt <= 3)
    {
        TASKSLEEP_MILLISECONDS(300);
        //PRTDRV_PRINTF("PRTDRV: Eng_Info.EnergySavingDetailStatus 0x%x\n", eng_Info.EnergySavingDetailStatus);
        if (eng_Info.EnergySavingDetailStatus.SleepMode)
        {
            break;
        }
        TryCnt++;
    }

    if (TryCnt == 4)
    {
        PRTDRV_PRINTF("PRTDRV: EnergySaving failed, DetailStatus 0x%x\n", eng_Info.EnergySavingDetailStatus);
        EngineInSleepProcessing = tFALSE;
        return tFALSE;
    }

    EngineReadyInSleepMode = 1;
    prtdrv_M3doCommand(ENGCMD_INSLEEP_MODE, NULL, NULL);

    TryCnt = 0;
    while ((EngineReadyInSleepMode != 2) && (TryCnt <= 4))
    {
        //PRTDRV_PRINTF("PRTDRV: EngineReadyInSleepMode %d\n", EngineReadyInSleepMode);
        TASKSLEEP_MILLISECONDS(100);
        prtdrv_M3doCommand(ENGCMD_INSLEEP_MODE, NULL, NULL);
        TryCnt++;
    }

    PRTDRV_PRINTF("PRTDRV: EngineReadyInSleepMode %d, TryCnt = %d\n", EngineReadyInSleepMode, TryCnt);

    PrintManager.resolution = JOBMGR_DPIMAX;
    PrintManager.inSleep    = tTRUE;

    prtdrv_M3doCommand(ENGCMD_SLEEP_STATE, &PrintManager.inSleep, NULL);

    TryCnt = 0;
    while ((EngineAllowShutdown == tFALSE) && (TryCnt <= 3))
    {
        //PRTDRV_PRINTF("PRTDRV: Waiting for C2E_SIGNAL_SLEEP SIGNAL_VALID\n");
        TASKSLEEP_MILLISECONDS(1000);
        TryCnt++;
    }

    PRTDRV_PRINTF("PRTDRV: EngineAllowShutdown %d, TryCnt = %d\n", EngineAllowShutdown, TryCnt);

    EngineAllowShutdown = tFALSE;
    TASKSLEEP_MILLISECONDS(1000);
    EngineInSleepProcessing = tFALSE;

    return tTRUE;
}

/**
 * @fn       void PRTDRV_SLEEP_OFF(void)
 * @brief    This function will let the printer engine exit the sleep mode.
 *           And synchronize UART with engine again.
 * @param    N/A.
 * @return   N/A.
 * @note
 */
void PRTDRV_SLEEP_OFF(void)
{
#if 0
    unsigned char send_data = 0, recv_data = 0;
#endif

    if (PrintManager.virtualMode) return;

    PRTDRV_EngineNotifySignal(C2E_SIGNAL_SLEEP, SIGNAL_INVALID); //ben move it out
#if 0    
    while (tTRUE)
    {
        PRTDRV_PRINTF("DPipe: transfering to normal mode.\n");

        send_data = CMD_SR43;
        par_SendRecvCmd(&send_data, &recv_data, 1);

        TASKSLEEP_MILLISECONDS(300);
        PRTDRV_PRINTF("DPipe: eng_Info.EnergySavingDetailStatus 0x%x\n", eng_Info.EnergySavingDetailStatus);

        if (eng_Info.EnergySavingDetailStatus.SleepMode == 0)
        {
        	break;
        }
    }
#endif
    PRTDRV_Init(tTRUE);
#if 0
    TASKSLEEP_MILLISECONDS(500);
#endif
}

/************ ********* ********* ********* ********* ***********
 *      API for print manager
 ********* ********* ********* ********* ********* ********* **********/
tUint8 PrtMgrGetPrintMediaSize()
{
    tUint8         paperSize  = 0;
    PM_ScriptDscp *pageScript = tNULL;

    pageScript = PrintMgr_GetFirstPageScript();

    if (pageScript != NULL)
        paperSize = pageScript->pPage->PaperSize;
    else
        SetServ_GetCurrentMachineSetting(SETSERV_MACHINE_PaperSize, &paperSize);

    return paperSize;
}

tUint32 PrtMgrGetPrintMediaCustomSize(tUint8 wid_leng)
{
    tUint32        customSize = 0;
    PM_ScriptDscp *pageScript = tNULL;
    pageScript                = PrintMgr_GetFirstPageScript();

    if (pageScript)
    {
        if (wid_leng == STATUSSERV_PRINT_CORRECT_CUSTOM_WIDTH)
            customSize = pageScript->pPage->PaperWidth / 10;
        else
            customSize = pageScript->pPage->PaperLength / 10;
    }
    else
    {
        if (wid_leng == STATUSSERV_PRINT_CORRECT_CUSTOM_WIDTH)
            customSize = 0;
        else
            customSize = 0;
    }

    return customSize;
}

tUint8 PrtMgrGetPrintMediaType()
{
    tUint8         mediaType  = 0;
    PM_ScriptDscp *pageScript = tNULL;

    pageScript = PrintMgr_GetFirstPageScript();

    if (pageScript)
        mediaType = pageScript->pPage->MediaType;
    else
        SetServ_GetCurrentMachineSetting(SETSERV_MACHINE_PaperType, &mediaType);

    return mediaType;
}

// below use for fax decode and scale
tUint32 PrtMgrGetOutputWidth(eJobMgr_PaperSize paperSize, eJobMgr_Resolution res)
{
    assert(paperSize < JOBMGR_UnknowPaperSize && res < JOBMGR_DPIMAX);
    return PrtMgrAreaTableX[paperSize] * PrtMgrResConvTableX[res] / 600;
}

tUint32 PrtMgrGetOutputLength(eJobMgr_PaperSize paperSize, eJobMgr_Resolution res)
{
    assert(paperSize < JOBMGR_UnknowPaperSize && res < JOBMGR_DPIMAX);
    return PrtMgrAreaTableY[paperSize] * PrtMgrResConvTableY[res] / 600;
}

tUint32 PRTMGRGetXResolution(eJobMgr_Resolution res)
{
    assert(res < JOBMGR_DPIMAX);
    return PrtMgrResConvTableX[(res)];
}

tUint32 PRTMGRGetYResolution(eJobMgr_Resolution res)
{
    assert(res < JOBMGR_DPIMAX);
    return PrtMgrResConvTableY[(res)];
}

#endif //[end]#ifndef __PIF_DRV_C__

/*
 * SwitchVideoPll()
 *------------------------------
 * modify the setting of VPLLCTR1 register,  switch the Video working mode 600dpi/1200dpi.
 *
 * Input:       mode - 0: 600dpi / 1: 1200dpi / 2: deep sleep reset
 * Return:  NULL
 *
*/
void SwitchVideoPll(int mode)
{
    static Uint8 vpllMode = 2;

    int    rv;
    Uint32 args[30];
    int    nretargs;
    Uint32 response;

    if (vpllMode == mode)
    {
        return;
    }
    else
    {
        vpllMode = mode;
    }

    if (mode > 1) return;

    args[0] = mode;

#if 0 //PRINTDEBUGINFO
  Log(2, "PRINT_SWITCH_PLL\n");
#endif

    rv = M3doCommand(PRINT_SWITCH_PLL, 1, &response, &nretargs, args);
}

/**
 * @fn      void PRTDRV_PifIOSetup(void)
 * @brief   To setup printer interface's GPIO
 * @param   N/A.
 * @return  N/A.
 * @note    GPO 49 = P_nPAGE, GPI50 = P_nREADY
 PIODIRB [63:32] | bit63.. |.52  !bit51|bit50|bit49|bit48 !.44!.40 !.36 !.32
 |..       |     !   * |Ready| PAGE| *    ! 0 ! 0  ! 0  ! 0

 GP0 21 = MCU_nSLEEP (High-invert to Low--> cut off MCU)
 PIODIRA [31:0]  | bit31.. |.24  !bit23|bit22|bit21|bit20 !.3 !.2  !.1  !.0
 |..       |     !   * |*    |SLEEP| *    ! 0 ! 0  ! 0  ! 0

*/
void PRTDRV_PifIOSetup(void) {}

void PrtDrv_SetVirtualMode(tBool value)
{
    PrintManager.virtualMode = value;

    if (value) StatusServ_SetEngStatusBit(STATUSSERV_EngReady, STATUSSERV_ENG_READY);
}

void PrtDrv_SetRawModePDL(tBool value)
{
    PrintManager.rawModePDL = value;
}

void PrtDrv_SetRawModeRAW(tBool value)
{
    PrintManager.rawModeRAW = value;
}

tBool PrtDrv_GetRawModePDL()
{
    return PrintManager.rawModePDL;
}

tBool PrtDrv_GetRawModeRAW()
{
    return PrintManager.rawModeRAW;
}

eJobMgr_JobType PrtDrv_GetRunningJobType(void)
{
    stJobMgr_JobDscp *pJob = PrintMgr_GetFirstJob();

    if (pJob == NULL)
        return JOBMGR_UnknowJob;
    else
        return JOBMGR_GetJobType(pJob);
}

/**
 * @fn      tBool PrtDrv_HWSetup(tVoid)
 * @brief   This function will call by PrinterManager to init. print driver.
 * @param   N/A.
 * @return  tBool -tTRUE/tFALSE
 * @note
 */
tBool PrtDrv_HWSetup(void)
{
#if 0
    _PrtDrv_FgateTimerInit(0);
#endif

    PRTDRV_Init(tFALSE);

    return (tTRUE);
}

tBool PrtDrv_CheckInitDone(tVoid)
{
    return PrintManager.initOK;
}

tBool PRTDRV_XCTonerInfoReady()
{
    return 0;
}

tBool IsMarketChinaOrNot()
{
    return tTRUE;
}

void PrtDrv_SetColorMapping(unsigned char hw_ver)
{
    if (hw_ver == 0x00)
    {
        colorMapping_ChannelToPrtMgr = &colorMapping_ChannelToPrtMgr_evt0[0];
        colorMapping_PrtMgrToChannel = &colorMapping_PrtMgrToChannel_evt0[0];
    }
    else
    {
        colorMapping_ChannelToPrtMgr = &colorMapping_ChannelToPrtMgr_others[0];
        colorMapping_PrtMgrToChannel = &colorMapping_PrtMgrToChannel_others[0];
    }
}

Uint32 par_GetRealTonerCntFromFlash(void)
{
#if TONER_WITH_CHIP
    int    i;
    Uint32 cnt = 0;

    if (TonerInstallHistory == tNULL)
    {
        TonerInstallHistory = (sFLASHSRV_TonerInfo *)malloc(6 * sizeof(sFLASHSRV_TonerInfo));
        assert(TonerInstallHistory != tNULL);
        FlashSrv_ReadTonerInfomation((Uint8 *)&TonerInstallHistory[0]);
    }

    for (i = 0; i < 5; i++)
    {
        if (TonerInstallHistory[i].SerialNumber[0] != '\0')
        {
            cnt = TonerInstallHistory[i].InstallationRanking;
        }
        else
            break;
    }

    return cnt;
#else
    Uint32 cnt = 0;

    if (TonerInstallHistory == tNULL)
    {
        TonerInstallHistory = (sFLASHSRV_TonerInfo *)malloc(6 * sizeof(sFLASHSRV_TonerInfo));
        assert(TonerInstallHistory != tNULL);
        FlashSrv_ReadTonerInfomation((Uint8 *)&TonerInstallHistory[0]);
    }

    if (memcmp(TonerInstallHistory[0].SerialNumber, TonerSN, sizeof(TonerSN)) == 0)
    {
        cnt = TonerInstallHistory[0].InstallationRanking;
    }
    else
    {
        memcpy(TonerInstallHistory[0].SerialNumber, TonerSN, sizeof(TonerSN));
        TonerInstallHistory[0].TotalPrintedDot     = 0;
        TonerInstallHistory[0].TotalPrintedPage    = 0;
        TonerInstallHistory[0].InstallationRanking = 0;
        FlashSrv_WriteTonerInfomation((Uint8 *)&TonerInstallHistory[0]);
    }

    return cnt;
#endif
}

Uint32 par_GetRealDrumCntFromFlash(void)
{
#if DRUM_WITH_CHIP
    int    i;
    Uint32 cnt = 0;

    if (DrumInstallHistory == tNULL) // When start the machine
    {
        DrumInstallHistory = (sFLASHSRV_TonerInfo *)malloc(6 * sizeof(sFLASHSRV_TonerInfo));
        assert(DrumInstallHistory != tNULL);
        FlashSrv_ReadDrumInfomation((Uint8 *)&DrumInstallHistory[0]);
    }

    for (i = 0; i < 5; i++)
    {
        if (DrumInstallHistory[i].SerialNumber[0] != '\0')
            cnt = DrumInstallHistory[i].InstallationRanking;
        else
            break;
    }

    return cnt;
#else
    Uint32 cnt = 0;

    if (DrumInstallHistory == tNULL) // When start the machine
    {
        DrumInstallHistory = (sFLASHSRV_TonerInfo *)malloc(6 * sizeof(sFLASHSRV_TonerInfo));
        assert(DrumInstallHistory != tNULL);
        FlashSrv_ReadDrumInfomation((Uint8 *)&DrumInstallHistory[0]);
    }

    if (memcmp(DrumInstallHistory[0].SerialNumber, DrumSN, sizeof(DrumSN)) == 0)
    {
        cnt = DrumInstallHistory[0].InstallationRanking;
    }
    else
    {
        memcpy(DrumInstallHistory[0].SerialNumber, DrumSN, sizeof(DrumSN));
        DrumInstallHistory[0].TotalPrintedDot     = 0;
        DrumInstallHistory[0].TotalPrintedPage    = 0;
        DrumInstallHistory[0].InstallationRanking = 0;
        FlashSrv_WriteDrumInfomation((Uint8 *)&DrumInstallHistory[0]);
    }

    return cnt;
#endif
}

void par_UpdateTonerInfo_IncPageCntInHistory(void)
{
    if (DrumInstallHistory == tNULL) // When start the machine
    {
        DrumInstallHistory = (sFLASHSRV_TonerInfo *)malloc(6 * sizeof(sFLASHSRV_TonerInfo));
        assert(DrumInstallHistory != tNULL);
        FlashSrv_ReadDrumInfomation((Uint8 *)&DrumInstallHistory[0]);
    }

    if (memcmp(DrumInstallHistory[5].SerialNumber, DrumSN, sizeof(DrumSN)) == 0)
    {
        DrumInstallHistory[5].TotalPrintedPage++;
    }
    else
    {
        memcpy(DrumInstallHistory[5].SerialNumber, DrumSN, sizeof(DrumSN));
        DrumInstallHistory[5].TotalPrintedPage = 1;
    }

    FlashSrv_WriteDrumInfomation((Uint8 *)&DrumInstallHistory[0]);
}

// For debug use
void par_PrintTonerInfoInHistory(void)
{
    int i = 0;

    if (TonerInstallHistory == tNULL) return;

    for (i = 0; i < 2; i++)
    {
        PRTDRV_PRINTF("[%d] [%s] [%d] [%d] [%d]\n", i + 1, TonerInstallHistory[i].SerialNumber, TonerInstallHistory[i].TotalPrintedDot,
                      TonerInstallHistory[i].TotalPrintedPage, TonerInstallHistory[i].InstallationRanking);
    }
}

void par_UpdateTonerInfo_IncDotCntInHistory(unsigned long dotcnt)
{
    if (TonerInstallHistory == tNULL) // When start the machine
    {
        TonerInstallHistory = (sFLASHSRV_TonerInfo *)malloc(6 * sizeof(sFLASHSRV_TonerInfo));
        assert(TonerInstallHistory != tNULL);
        FlashSrv_ReadTonerInfomation((Uint8 *)&TonerInstallHistory[0]);
    }

    if (memcmp(TonerInstallHistory[5].SerialNumber, TonerSN, sizeof(TonerSN)) == 0)
    {
        TonerInstallHistory[5].TotalPrintedDot += dotcnt;
    }
    else
    {
        memcpy(TonerInstallHistory[5].SerialNumber, TonerSN, sizeof(TonerSN));
        TonerInstallHistory[5].TotalPrintedDot = dotcnt;
    }

    FlashSrv_WriteTonerInfomation((Uint8 *)&TonerInstallHistory[0]);
}

#if TONER_WITH_CHIP
sFLASHSRV_TonerInfo CurInstallToner = {0};
void                par_UpdateTonerSN(Uint32 *pTonerSN)
{
    sFLASHSRV_TonerInfo NewToner = {0};

    int ret = 0, i = 0, j = 0, k = 0;
    for (i = 0; i < 5; i++)
    {
        for (j = 0; j < 4; j++)
        {
            NewToner.SerialNumber[k] = (pTonerSN[i] >> (j * 8)) & 0xFF;
            k++;
        }
    }

    ret = par_UpdateTonerInfo_IncInstallationInHistory(NewToner);
    if (1 != ret)
    {
        PRTDRV_PRINTF("PRTDRV: Current toner[%d] SN %s\n", ret, NewToner.SerialNumber);
        CurInstallToner = NewToner;
    }
}

void par_UpdateTonerInfo_DecCurInstallTonerInHistory()
{
    int i = 0;

    if ((CurInstallToner.SerialNumber[0] == '\0') || (TonerInstallHistory == tNULL))
    {
        return;
    }

    for (i = 0; i < 5; i++)
    {
        if (memcmp(TonerInstallHistory[i].SerialNumber, CurInstallToner.SerialNumber, sizeof(CurInstallToner.SerialNumber)) == 0)
        {
            break;
        }
    }

    if (i < 5)
    {
        memset(&TonerInstallHistory[i], 0x00, sizeof(sFLASHSRV_TonerInfo));
        FlashSrv_WriteTonerInfomation((Uint8 *)&TonerInstallHistory[0]);
        memset(&CurInstallToner, 0x00, sizeof(sFLASHSRV_TonerInfo));
    }

    return;
}
#endif

int par_UpdateTonerInfo_IncInstallationInHistory(sFLASHSRV_TonerInfo NewToner)
{
#if TONER_WITH_CHIP
    int i = 0;

    if (NewToner.SerialNumber[0] == '\0')
    {
        return 1; // string is empty
    }

    if (TonerInstallHistory == tNULL) // When start the machine
    {
        TonerInstallHistory = (sFLASHSRV_TonerInfo *)malloc(6 * sizeof(sFLASHSRV_TonerInfo));
        assert(TonerInstallHistory != tNULL);
        FlashSrv_ReadTonerInfomation((Uint8 *)&TonerInstallHistory[0]);
    }

    for (i = 0; i < 5; i++)
    {
        if (memcmp(TonerInstallHistory[i].SerialNumber, NewToner.SerialNumber, sizeof(NewToner.SerialNumber)) == 0)
        {
            return 2; // Already has
        }

        if (TonerInstallHistory[i].SerialNumber[0] == '\0')
        {
            NewToner.TotalPrintedDot     = 0;
            NewToner.TotalPrintedPage    = 0;
            NewToner.InstallationRanking = i;
            memcpy(&TonerInstallHistory[i], &NewToner, sizeof(sFLASHSRV_TonerInfo));
            FlashSrv_WriteTonerInfomation((Uint8 *)&TonerInstallHistory[0]);

            return 0; // Add OK
        }
    }

    for (i = 1; i < 5; i++)
    {
        TonerInstallHistory[i - 1] = TonerInstallHistory[i];
    }

    NewToner.TotalPrintedDot     = 0;
    NewToner.TotalPrintedPage    = 0;
    NewToner.InstallationRanking = TonerInstallHistory[4].InstallationRanking + 1;

    memcpy(&TonerInstallHistory[4], &NewToner, sizeof(sFLASHSRV_TonerInfo));

    FlashSrv_WriteTonerInfomation((Uint8 *)&TonerInstallHistory[0]);

    return 0; // Add OK
#else

    if (TonerInstallHistory == tNULL) // When start the machine
    {
        TonerInstallHistory = (sFLASHSRV_TonerInfo *)malloc(6 * sizeof(sFLASHSRV_TonerInfo));
        assert(TonerInstallHistory != tNULL);
        FlashSrv_ReadTonerInfomation((Uint8 *)&TonerInstallHistory[0]);
    }

    if (memcmp(TonerInstallHistory[0].SerialNumber, TonerSN, sizeof(TonerSN)) == 0)
    {
        TonerInstallHistory[0].InstallationRanking++;
    }
    else
    {
        memcpy(TonerInstallHistory[0].SerialNumber, TonerSN, sizeof(TonerSN));
        TonerInstallHistory[0].TotalPrintedDot     = 0;
        TonerInstallHistory[0].TotalPrintedPage    = 0;
        TonerInstallHistory[0].InstallationRanking = 1;
    }

    FlashSrv_WriteTonerInfomation((Uint8 *)&TonerInstallHistory[0]);

    return 0;
#endif
}

int par_UpdateDrumInfo_IncInstallationInHistory(sFLASHSRV_TonerInfo NewDrum)
{
#if DRUM_WITH_CHIP
    int i = 0;

    if (NewDrum.SerialNumber[0] == '\0') return 1; // string is empty

    if (DrumInstallHistory == tNULL) // When start the machine
    {
        DrumInstallHistory = (sFLASHSRV_TonerInfo *)malloc(6 * sizeof(sFLASHSRV_TonerInfo));
        assert(DrumInstallHistory != tNULL);
        FlashSrv_ReadDrumInfomation((Uint8 *)&DrumInstallHistory[0]);
    }

    for (i = 0; i < 5; i++)
    {
        if (memcmp(DrumInstallHistory[i].SerialNumber, NewDrum.SerialNumber, sizeof(NewDrum.SerialNumber)) == 0)
        {
            return 2; // Already has
        }

        if (DrumInstallHistory[i].SerialNumber[0] == '\0')
        {
            NewDrum.TotalPrintedDot     = 0;
            NewDrum.TotalPrintedPage    = 0;
            NewDrum.InstallationRanking = i;
            memcpy(&DrumInstallHistory[i], &NewDrum, sizeof(sFLASHSRV_TonerInfo));
            FlashSrv_WriteDrumInfomation((Uint8 *)&DrumInstallHistory[0]);

            return 0; // Add OK
        }
    }

    for (i = 1; i < 5; i++)
    {
        DrumInstallHistory[i - 1] = DrumInstallHistory[i];
    }

    NewDrum.TotalPrintedDot     = 0;
    NewDrum.TotalPrintedPage    = 0;
    NewDrum.InstallationRanking = DrumInstallHistory[4].InstallationRanking + 1;

    memcpy(&DrumInstallHistory[4], &NewDrum, sizeof(sFLASHSRV_TonerInfo));

    FlashSrv_WriteDrumInfomation((Uint8 *)&DrumInstallHistory[0]);

    return 0; // Add OK
#else
    if (DrumInstallHistory == tNULL) // When start the machine
    {
        DrumInstallHistory = (sFLASHSRV_TonerInfo *)malloc(6 * sizeof(sFLASHSRV_TonerInfo));
        assert(DrumInstallHistory != tNULL);
        FlashSrv_ReadDrumInfomation((Uint8 *)&DrumInstallHistory[0]);
    }

    if (memcmp(DrumInstallHistory[0].SerialNumber, DrumSN, sizeof(DrumSN)) == 0)
    {
        DrumInstallHistory[0].InstallationRanking++;
    }
    else
    {
        memcpy(DrumInstallHistory[0].SerialNumber, DrumSN, sizeof(DrumSN));
        DrumInstallHistory[0].TotalPrintedDot     = 0;
        DrumInstallHistory[0].TotalPrintedPage    = 0;
        DrumInstallHistory[0].InstallationRanking = 1;
    }

    FlashSrv_WriteDrumInfomation((Uint8 *)&DrumInstallHistory[0]);

    return 0;
#endif
}

void par_ResetTonerDrumInfo()
{
    if (TonerInstallHistory)
    {
        memset((tUint8 *)TonerInstallHistory, 0x00, 6 * sizeof(sFLASHSRV_TonerInfo));
    }

    if (DrumInstallHistory)
    {
        memset((tUint8 *)DrumInstallHistory, 0x00, 6 * sizeof(sFLASHSRV_TonerInfo));
    }
}

tStatus CRM_Value_Get(int option, void *value)
{
    switch (option)
    {
        case 1: // Machine SN
            //if(serialnoArray[0] == 0)
            Engine_serialNO_get(serialnoArray);

            memcpy((unsigned char *)value, &serialnoArray[0], sizeof(serialnoArray));

            break;
        case 2: // Total Counter
            StatusServ_GetPrnMeter(STATUSSERV_PNPrnMeter4, (tUint32 *)value);
            break;

        case 3: // Toner SN
#if TONER_WITH_CHIP
            if (CurInstallToner.SerialNumber[0] == '\0')
            {
                memset((unsigned char *)value, 0, 20);
            }
            else
            {
                memcpy((unsigned char *)value, CurInstallToner.SerialNumber, sizeof(CurInstallToner.SerialNumber));
            }
#else
            memset((unsigned char *)value, 0, 20);
#endif
            break;

        default:
            return tERROR;
    }

    return tOK;
}
